<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Track extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'release_id',
        'title',
        'file_path',
        'file_type',
        'duration_seconds',
        'track_number',
        'is_explicit',
        'lyrics',
        'meta_info',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'meta_info' => 'json',
        'is_explicit' => 'boolean',
        'duration_seconds' => 'integer',
        'track_number' => 'integer',
    ];

    /**
     * Get the release that owns the track.
     */
    public function release(): BelongsTo
    {
        return $this->belongsTo(Release::class);
    }

    /**
     * Get the analytics for the track.
     */
    public function analytics(): MorphMany
    {
        return $this->morphMany(Analytic::class, 'analyzable');
    }

    /**
     * Format the duration as minutes:seconds.
     */
    public function getFormattedDurationAttribute(): string
    {
        $minutes = floor($this->duration_seconds / 60);
        $seconds = $this->duration_seconds % 60;

        return sprintf('%d:%02d', $minutes, $seconds);
    }

    /**
     * Get the URL to the track file.
     */
    public function getFileUrlAttribute(): string
    {
        return asset('storage/' . $this->file_path);
    }

    /**
     * Get the artist of the track through the release.
     */
    public function getArtistAttribute()
    {
        return $this->release->artist;
    }
}
