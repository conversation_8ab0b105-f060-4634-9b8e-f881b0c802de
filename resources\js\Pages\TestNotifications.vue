<template>
    <div class="test-notifications">
        <h1>Тест системы уведомлений</h1>
        
        <div class="test-section">
            <h2>Статус подключения</h2>
            <div class="status-indicator" :class="{ connected: notificationStore.isConnected }">
                {{ notificationStore.isConnected ? 'Подключено' : 'Отключено' }}
            </div>
        </div>

        <div class="test-section">
            <h2>Уведомления ({{ notificationStore.notifications.length }})</h2>
            <div class="notifications-count">
                Непрочитанных: {{ notificationStore.unreadCount }}
            </div>
            
            <div class="notifications-list">
                <div 
                    v-for="notification in notificationStore.notifications" 
                    :key="notification.id"
                    class="notification-item"
                    :class="{ unread: !notification.read_at }"
                >
                    <div class="notification-content">
                        <strong>{{ notification.data?.message }}</strong>
                        <div class="notification-time">
                            {{ formatDate(notification.created_at) }}
                        </div>
                    </div>
                    <button 
                        @click="markAsRead(notification.id)"
                        v-if="!notification.read_at"
                        class="mark-read-btn"
                    >
                        Прочитать
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Тестовые действия</h2>
            <button @click="createTestApplication" class="test-btn">
                Создать тестовую заявку
            </button>
            <button @click="loadNotifications" class="test-btn">
                Обновить уведомления
            </button>
            <button @click="markAllAsRead" class="test-btn">
                Прочитать все
            </button>
        </div>
    </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount } from 'vue';
import { useNotificationStore } from '@/Stores/notifications';
import { usePage } from '@inertiajs/vue3';
import axios from 'axios';

const notificationStore = useNotificationStore();
const user = usePage().props.auth.user;

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Только что';
    if (diffMins < 60) return `${diffMins} мин. назад`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} ч. назад`;
    
    return date.toLocaleDateString();
};

const markAsRead = async (notificationId) => {
    await notificationStore.markAsRead(notificationId);
};

const loadNotifications = async () => {
    await notificationStore.loadNotifications();
};

const markAllAsRead = async () => {
    await notificationStore.markAllAsRead();
};

const createTestApplication = async () => {
    try {
        const response = await axios.post('/api/applications', {
            type: 'promo',
            title: `Тестовая заявка ${new Date().toLocaleTimeString()}`,
            description: 'Это тестовая заявка для проверки системы уведомлений'
        });
        
        console.log('Тестовая заявка создана:', response.data);
    } catch (error) {
        console.error('Ошибка создания заявки:', error);
    }
};

onMounted(async () => {
    console.log('Инициализация тестовой страницы уведомлений');
    
    if (user) {
        await notificationStore.loadNotifications();
        notificationStore.setupRealTimeListeners(user);
    }
});

onBeforeUnmount(() => {
    notificationStore.disconnectRealTime();
});
</script>

<style scoped>
.test-notifications {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.status-indicator {
    padding: 10px;
    border-radius: 4px;
    background-color: #f44336;
    color: white;
    display: inline-block;
}

.status-indicator.connected {
    background-color: #4caf50;
}

.notifications-count {
    margin-bottom: 15px;
    font-weight: bold;
}

.notification-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
}

.notification-item.unread {
    background-color: #f0f8ff;
    border-color: #2196f3;
}

.notification-content {
    flex: 1;
}

.notification-time {
    font-size: 0.8em;
    color: #666;
    margin-top: 5px;
}

.test-btn, .mark-read-btn {
    padding: 10px 15px;
    margin-right: 10px;
    margin-bottom: 10px;
    border: none;
    border-radius: 4px;
    background-color: #2196f3;
    color: white;
    cursor: pointer;
}

.test-btn:hover, .mark-read-btn:hover {
    background-color: #1976d2;
}

.mark-read-btn {
    background-color: #4caf50;
}

.mark-read-btn:hover {
    background-color: #45a049;
}
</style>
