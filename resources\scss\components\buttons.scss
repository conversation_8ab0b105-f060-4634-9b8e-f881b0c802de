// Современная система кнопок 2025
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    line-height: 1;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 44px;

    // Базовый эффект свечения
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left var(--transition-slow);
        z-index: 1;
    }

    &:hover::before {
        left: 100%;
    }

    &:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;

        &::before {
            display: none;
        }
    }

    // Размеры
    &.btn-xs {
        padding: 6px 12px;
        font-size: var(--font-size-xs);
        min-height: 32px;
        border-radius: var(--radius-md);
    }

    &.btn-sm {
        padding: 8px 16px;
        font-size: var(--font-size-sm);
        min-height: 36px;
        border-radius: var(--radius-md);
    }

    &.btn-lg {
        padding: 16px 32px;
        font-size: var(--font-size-lg);
        min-height: 52px;
        border-radius: var(--radius-xl);
    }

    &.btn-xl {
        padding: 20px 40px;
        font-size: var(--font-size-xl);
        min-height: 60px;
        border-radius: var(--radius-2xl);
    }

    // Варианты кнопок
    &.btn-primary {
        background: var(--primary-gradient);
        color: var(--text-inverse);
        border-color: var(--primary-color);
        box-shadow: var(--shadow-md);

        &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-glow);
        }

        &:active {
            transform: translateY(0);
        }
    }

    &.btn-secondary {
        background: var(--secondary-gradient);
        color: var(--text-inverse);
        border-color: var(--secondary-color);

        &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
    }

    &.btn-success {
        background: var(--success-gradient);
        color: var(--text-inverse);
        border-color: var(--success-color);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 20px rgba(var(--success-color-rgb), 0.4);
        }
    }

    &.btn-danger {
        background: var(--danger-gradient);
        color: var(--text-inverse);
        border-color: var(--danger-color);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 20px rgba(var(--danger-color-rgb), 0.4);
        }
    }

    &.btn-warning {
        background: var(--warning-gradient);
        color: var(--text-inverse);
        border-color: var(--warning-color);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 20px rgba(var(--warning-color-rgb), 0.4);
        }
    }

    &.btn-info {
        background: var(--info-gradient);
        color: var(--text-inverse);
        border-color: var(--info-color);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 20px rgba(var(--info-color-rgb), 0.4);
        }
    }

    // Outline варианты
    &.btn-outline-primary {
        background: transparent;
        color: var(--primary-color);
        border-color: var(--primary-color);

        &:hover {
            background: var(--primary-gradient);
            color: var(--text-inverse);
            transform: translateY(-2px);
            box-shadow: var(--shadow-glow);
        }
    }

    &.btn-outline-secondary {
        background: transparent;
        color: var(--secondary-color);
        border-color: var(--secondary-color);

        &:hover {
            background: var(--secondary-gradient);
            color: var(--text-inverse);
            transform: translateY(-2px);
        }
    }

    // Ghost варианты
    &.btn-ghost {
        background: transparent;
        color: var(--text-primary);
        border-color: transparent;

        &:hover {
            background: var(--bg-secondary);
            transform: translateY(-1px);
        }
    }

    &.btn-ghost-primary {
        background: transparent;
        color: var(--primary-color);
        border-color: transparent;

        &:hover {
            background: var(--primary-light);
            transform: translateY(-1px);
        }
    }

    // Кнопка с иконкой
    .icon {
        transition: transform var(--transition-bounce);

        &:only-child {
            margin: 0;
        }
    }

    &:hover .icon {
        transform: scale(1.1);
    }
}

// Группа кнопок - современный дизайн
.btn-group {
    display: inline-flex;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);

    .btn {
        border-radius: 0;
        border-right: 1px solid rgba(255, 255, 255, 0.2);

        &:first-child {
            border-top-left-radius: var(--radius-lg);
            border-bottom-left-radius: var(--radius-lg);
        }

        &:last-child {
            border-top-right-radius: var(--radius-lg);
            border-bottom-right-radius: var(--radius-lg);
            border-right: none;
        }

        &:hover {
            z-index: 1;
            transform: none;
            box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.3);
        }
    }
}

// Floating Action Button
.btn-fab {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-full);
    padding: 0;
    background: var(--primary-gradient);
    color: var(--text-inverse);
    border: none;
    box-shadow: var(--shadow-lg);
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: var(--z-fab);

    &:hover {
        transform: scale(1.1);
        box-shadow: var(--shadow-2xl);
    }

    &:active {
        transform: scale(0.95);
    }

    &.btn-fab-sm {
        width: 40px;
        height: 40px;
    }

    &.btn-fab-lg {
        width: 72px;
        height: 72px;
    }
}

// Кнопка загрузки
.btn-loading {
    position: relative;

    &.loading {
        color: transparent;
        pointer-events: none;

        &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: var(--radius-full);
            animation: spin 1s linear infinite;
        }
    }
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

// Кнопка с эффектом ripple
.btn-ripple {
    position: relative;
    overflow: hidden;

    &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: var(--radius-full);
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    &:active::after {
        width: 300px;
        height: 300px;
    }
}
