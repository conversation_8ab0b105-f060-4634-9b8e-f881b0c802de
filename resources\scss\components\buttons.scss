.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    cursor: pointer;
    border: none;
    outline: none;

    &:hover {
        text-decoration: none;
    }

    &:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }

    &:disabled {
        opacity: 0.65;
        cursor: not-allowed;
    }

    // Варианты кнопок
    &.btn-primary {
        background-color: var(--primary-color);
        color: white;

        &:hover {
            background-color: darken(#3f51b5, 10%);
        }
    }

    &.btn-secondary {
        background-color: var(--secondary-color);
        color: white;

        &:hover {
            background-color: darken(#6673e4, 10%);
        }
    }

    &.btn-success {
        background-color: var(--success-color);
        color: white;

        &:hover {
            background-color: darken(#4caf50, 10%);
        }
    }

    &.btn-danger {
        background-color: var(--danger-color);
        color: white;

        &:hover {
            background-color: darken(#f44336, 10%);
        }
    }

    &.btn-warning {
        background-color: var(--warning-color);
        color: white;

        &:hover {
            background-color: darken(#ff9800, 10%);
        }
    }

    &.btn-outline {
        background-color: transparent;
        color: var(--text-primary);
        border: 1px solid var(--text-disabled);

        &:hover {
            background-color: var(--bg-secondary);
        }
    }

    // Размеры кнопок
    &.btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    &.btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    // Кнопка с иконкой
    .icon {
        margin-right: 0.5rem;

        &:only-child {
            margin-right: 0;
        }
    }
}

// Группа кнопок
.btn-group {
    display: inline-flex;

    .btn {
        border-radius: 0;

        &:first-child {
            border-top-left-radius: var(--radius-md);
            border-bottom-left-radius: var(--radius-md);
        }

        &:last-child {
            border-top-right-radius: var(--radius-md);
            border-bottom-right-radius: var(--radius-md);
        }
    }
}
