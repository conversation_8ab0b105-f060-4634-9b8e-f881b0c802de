<?php

namespace App\Listeners;

use App\Events\ApplicationStatusUpdated;
use App\Notifications\ApplicationStatusChanged;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendApplicationStatusNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ApplicationStatusUpdated $event): void
    {
        // Отправляем уведомление пользователю, который создал заявку
        $event->application->user->notify(
            new ApplicationStatusChanged(
                $event->application,
                $event->oldStatus,
                $event->newStatus
            )
        );
    }
}
