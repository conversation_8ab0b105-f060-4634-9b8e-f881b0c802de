<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;

class ProcessFileUpload implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 минут
    public $tries = 3;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public string $filePath,
        public string $originalName,
        public string $mimeType,
        public string $destinationPath,
        public ?array $metadata = null
    ) {
        $this->onQueue('files');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Начинаем обработку файла: {$this->originalName}");

            // Проверяем, существует ли временный файл
            if (!Storage::disk('local')->exists($this->filePath)) {
                throw new \Exception("Временный файл не найден: {$this->filePath}");
            }

            // Получаем содержимое файла
            $fileContent = Storage::disk('local')->get($this->filePath);

            // Определяем финальный путь для сохранения
            $finalPath = $this->destinationPath . '/' . $this->generateFileName();

            // Сохраняем файл в постоянное хранилище
            Storage::disk('public')->put($finalPath, $fileContent);

            // Если это изображение, создаем миниатюры
            if ($this->isImage()) {
                $this->createThumbnails($finalPath);
            }

            // Если это аудио файл, извлекаем метаданные
            if ($this->isAudio()) {
                $this->extractAudioMetadata($finalPath);
            }

            // Удаляем временный файл
            Storage::disk('local')->delete($this->filePath);

            Log::info("Файл успешно обработан: {$this->originalName} -> {$finalPath}");

        } catch (\Exception $e) {
            Log::error("Ошибка обработки файла {$this->originalName}: " . $e->getMessage());
            
            // Удаляем временный файл в случае ошибки
            if (Storage::disk('local')->exists($this->filePath)) {
                Storage::disk('local')->delete($this->filePath);
            }
            
            throw $e;
        }
    }

    /**
     * Generate unique filename.
     */
    private function generateFileName(): string
    {
        $extension = pathinfo($this->originalName, PATHINFO_EXTENSION);
        $name = pathinfo($this->originalName, PATHINFO_FILENAME);
        $timestamp = now()->format('Y-m-d_H-i-s');
        $hash = substr(md5($this->originalName . time()), 0, 8);
        
        return "{$name}_{$timestamp}_{$hash}.{$extension}";
    }

    /**
     * Check if file is an image.
     */
    private function isImage(): bool
    {
        return str_starts_with($this->mimeType, 'image/');
    }

    /**
     * Check if file is an audio file.
     */
    private function isAudio(): bool
    {
        return str_starts_with($this->mimeType, 'audio/');
    }

    /**
     * Create thumbnails for images.
     */
    private function createThumbnails(string $filePath): void
    {
        // Здесь можно добавить логику создания миниатюр
        // Например, используя Intervention Image
        Log::info("Создание миниатюр для изображения: {$filePath}");
    }

    /**
     * Extract metadata from audio files.
     */
    private function extractAudioMetadata(string $filePath): void
    {
        try {
            // Попытка извлечения метаданных с помощью getID3 (если установлен)
            if (class_exists('\getID3')) {
                $getID3 = new \getID3();
                $fullPath = Storage::disk('public')->path($filePath);
                $fileInfo = $getID3->analyze($fullPath);

                $metadata = [
                    'duration_seconds' => isset($fileInfo['playtime_seconds'])
                        ? (int)$fileInfo['playtime_seconds']
                        : 0,
                    'bitrate' => $fileInfo['audio']['bitrate'] ?? null,
                    'sample_rate' => $fileInfo['audio']['sample_rate'] ?? null,
                    'channels' => $fileInfo['audio']['channels'] ?? null,
                    'format' => $fileInfo['fileformat'] ?? null,
                ];

                // Сохраняем метаданные в отдельный файл
                $metadataPath = str_replace(pathinfo($filePath, PATHINFO_EXTENSION), 'json', $filePath);
                Storage::disk('public')->put($metadataPath, json_encode($metadata, JSON_PRETTY_PRINT));

                Log::info("Метаданные извлечены для аудио файла: {$filePath}", $metadata);
            } else {
                Log::warning("getID3 не установлен, метаданные не извлечены для: {$filePath}");
            }
        } catch (\Exception $e) {
            Log::error("Ошибка извлечения метаданных из аудио файла {$filePath}: " . $e->getMessage());
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Не удалось обработать файл {$this->originalName}: " . $exception->getMessage());
        
        // Удаляем временный файл
        if (Storage::disk('local')->exists($this->filePath)) {
            Storage::disk('local')->delete($this->filePath);
        }
    }
}
