<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Release;
use App\Models\Track;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AnalyticsController extends Controller
{
    /**
     * Display analytics dashboard.
     */
    public function index(Request $request): Response
    {
        $user = auth()->user();

        // Проверка доступа
        if (!$user->isAdmin() && !$user->isManager()) {
            abort(403, 'Доступ запрещен');
        }

        // Получаем период для анализа (по умолчанию последние 30 дней)
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays((int)$period);
        $endDate = Carbon::now();

        // Общая статистика
        $totalStats = $this->getTotalStats();

        // Статистика за период
        $periodStats = $this->getPeriodStats($startDate, $endDate);

        // Данные для графиков
        $chartsData = $this->getChartsData($startDate, $endDate, $period);

        // Топ исполнители
        $topArtists = $this->getTopArtists($startDate, $endDate);

        // Последние активности
        $recentActivities = $this->getRecentActivities();

        return Inertia::render('Analytics/Index', [
            'totalStats' => $totalStats,
            'periodStats' => $periodStats,
            'chartsData' => $chartsData,
            'topArtists' => $topArtists,
            'recentActivities' => $recentActivities,
            'period' => $period,
        ]);
    }

    /**
     * Получить общую статистику.
     */
    private function getTotalStats(): array
    {
        return [
            'users' => [
                'total' => User::count(),
                'artists' => User::where('role', 'artist')->count(),
                'admins' => User::where('role', 'admin')->count(),
                'managers' => User::where('role', 'manager')->count(),
            ],
            'releases' => [
                'total' => Release::count(),
                'published' => Release::where('status', 'published')->count(),
                'pending' => Release::where('status', 'pending')->count(),
                'draft' => Release::where('status', 'draft')->count(),
            ],
            'tracks' => [
                'total' => Track::count(),
            ],
            'applications' => [
                'total' => Application::count(),
                'pending' => Application::where('status', 'pending')->count(),
                'approved' => Application::where('status', 'approved')->count(),
                'rejected' => Application::where('status', 'rejected')->count(),
            ],
        ];
    }

    /**
     * Получить статистику за период.
     */
    private function getPeriodStats(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'users' => [
                'new' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
            ],
            'releases' => [
                'new' => Release::whereBetween('created_at', [$startDate, $endDate])->count(),
                'published' => Release::where('status', 'published')
                    ->whereBetween('updated_at', [$startDate, $endDate])
                    ->count(),
            ],
            'tracks' => [
                'new' => Track::whereBetween('created_at', [$startDate, $endDate])->count(),
            ],
            'applications' => [
                'new' => Application::whereBetween('created_at', [$startDate, $endDate])->count(),
                'processed' => Application::whereIn('status', ['approved', 'rejected'])
                    ->whereBetween('updated_at', [$startDate, $endDate])
                    ->count(),
            ],
        ];
    }

    /**
     * Получить данные для графиков.
     */
    private function getChartsData(Carbon $startDate, Carbon $endDate, string $period): array
    {
        $days = (int)$period;
        $dates = [];
        $usersData = [];
        $releasesData = [];
        $applicationsData = [];

        // Генерируем даты для оси X
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dates[] = $date->format('Y-m-d');

            // Подсчитываем данные для каждого дня
            $dayStart = $date->startOfDay();
            $dayEnd = $date->endOfDay();

            $usersData[] = User::whereBetween('created_at', [$dayStart, $dayEnd])->count();
            $releasesData[] = Release::whereBetween('created_at', [$dayStart, $dayEnd])->count();
            $applicationsData[] = Application::whereBetween('created_at', [$dayStart, $dayEnd])->count();
        }

        return [
            'dates' => $dates,
            'users' => $usersData,
            'releases' => $releasesData,
            'applications' => $applicationsData,
        ];
    }

    /**
     * Получить топ исполнителей.
     */
    private function getTopArtists(Carbon $startDate, Carbon $endDate): array
    {
        try {
            return User::where('role', 'artist')
                ->with('artist')
                ->whereHas('artist')
                ->get()
                ->map(function ($user) use ($startDate, $endDate) {
                    $artist = $user->artist;

                    $releasesCount = Release::where('artist_id', $artist->id)
                        ->whereBetween('created_at', [$startDate, $endDate])
                        ->count();

                    $tracksCount = Track::whereHas('release', function ($query) use ($artist, $startDate, $endDate) {
                        $query->where('artist_id', $artist->id)
                            ->whereBetween('created_at', [$startDate, $endDate]);
                    })->count();

                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'avatar' => $user->avatar,
                        'releases_count' => $releasesCount,
                        'tracks_count' => $tracksCount,
                    ];
                })
                ->filter(function ($artist) {
                    return $artist['releases_count'] > 0;
                })
                ->sortByDesc('releases_count')
                ->sortByDesc('tracks_count')
                ->take(10)
                ->values()
                ->toArray();
        } catch (\Exception $e) {
            // Возвращаем пустой массив если что-то пошло не так
            return [];
        }
    }

    /**
     * Получить последние активности.
     */
    private function getRecentActivities(): array
    {
        try {
            $activities = collect();

            // Последние релизы
            $recentReleases = Release::with('artist.user')
                ->latest()
                ->limit(5)
                ->get()
                ->map(function ($release) {
                    $user = $release->artist->user ?? null;
                    return [
                        'type' => 'release',
                        'title' => "Новый релиз: {$release->title}",
                        'description' => "Создан пользователем " . ($user ? $user->name : 'Неизвестно'),
                        'date' => $release->created_at,
                        'user' => $user ? $user->name : 'Неизвестно',
                        'status' => $release->status,
                    ];
                });

        // Последние заявки
        $recentApplications = Application::with('user')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($application) {
                return [
                    'type' => 'application',
                    'title' => "Новая заявка: {$application->title}",
                    'description' => "Подана пользователем {$application->user->name}",
                    'date' => $application->created_at,
                    'user' => $application->user->name,
                    'status' => $application->status,
                ];
            });

        // Новые пользователи
        $recentUsers = User::latest()
            ->limit(5)
            ->get()
            ->map(function ($user) {
                return [
                    'type' => 'user',
                    'title' => "Новый пользователь: {$user->name}",
                    'description' => "Зарегистрировался как {$user->role}",
                    'date' => $user->created_at,
                    'user' => $user->name,
                    'status' => 'active',
                ];
            });

        // Объединяем и сортируем по дате
        $activities = $activities
            ->concat($recentReleases)
            ->concat($recentApplications)
            ->concat($recentUsers)
            ->sortByDesc('date')
            ->take(15)
            ->values()
            ->toArray();

        return $activities;
        } catch (\Exception $e) {
            // Возвращаем пустой массив если что-то пошло не так
            return [];
        }
    }
}
