<template>
    <DashboardLayout :title="release.title">
        <Head :title="release.title" />
        <div class="content-container">
            <div class="actions-toolbar">
                <Link :href="route('releases.index')" class="back-btn">
                    <span>← К списку релизов</span>
                </Link>
                <Link
                    v-if="canEdit"
                    :href="route('releases.edit', release.id)"
                    class="btn btn-primary"
                >
                    Редактировать
                </Link>
            </div>

            <div class="release-type-badge">
                <Music class="badge-icon" size="18" />
                <span>{{ getReleaseTypeLabel(release.type) }}</span>
            </div>

            <div class="release-main">
                <div class="release-cover">
                    <img
                        :src="
                            release.cover_image
                                ? `/storage/${release.cover_image}`
                                : '/images/covers/placeholder.png'
                        "
                        :alt="release.title"
                    />
                </div>
                <div class="release-info">
                    <h1 class="release-title">{{ release.title }}</h1>
                    <div class="release-meta">
                        <span class="meta-item">
                            <User size="16" class="meta-icon" />
                            {{
                                release.artist?.display_name ||
                                "Неизвестный артист"
                            }}
                        </span>
                        <span class="meta-item">
                            <Calendar size="16" class="meta-icon" />
                            {{ formatDate(release.release_date) }}
                        </span>
                        <span class="meta-item">
                            Жанр: {{ release.genre }}
                        </span>
                        <span class="meta-item">
                            Язык: {{ release.language }}
                        </span>
                        <span class="meta-item" v-if="release.is_explicit">
                            18+ Контент
                        </span>
                        <span
                            class="meta-item status-badge"
                            :class="'status-' + release.status"
                        >
                            {{ getStatusLabel(release.status) }}
                        </span>
                    </div>
                    <div class="release-description" v-if="release.description">
                        <h3>Описание</h3>
                        <p>{{ release.description }}</p>
                    </div>

                    <div class="release-stats">
                        <div
                            class="stat-item"
                            v-if="release.tracks && release.tracks.length > 0"
                        >
                            <span class="stat-value">{{
                                release.tracks.length
                            }}</span>
                            <span class="stat-label">{{
                                release.tracks.length === 1 ? "трек" : "треков"
                            }}</span>
                        </div>
                        <div class="stat-item" v-if="getTotalDuration() > 0">
                            <span class="stat-value">{{
                                formatDuration(getTotalDuration())
                            }}</span>
                            <span class="stat-label">общая длительность</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">{{
                                formatDate(release.created_at)
                            }}</span>
                            <span class="stat-label">дата создания</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tracks-section">
                <h3 class="section-title">Треки</h3>
                <div v-if="release.tracks && release.tracks.length > 0">
                    <!-- Список треков -->
                    <div class="tracks-list">
                        <div
                            v-for="track in release.tracks"
                            :key="track.id"
                            class="track-item"
                            @mouseenter="hoveredTrack = track.id"
                            @mouseleave="hoveredTrack = null"
                        >
                            <div class="track-number-container">
                                <div
                                    v-if="
                                        hoveredTrack !== track.id &&
                                        currentTrack?.id !== track.id
                                    "
                                    class="track-number"
                                >
                                    {{ track.track_number }}
                                </div>
                                <button
                                    v-else
                                    class="play-button"
                                    @click="playTrack(track)"
                                    :class="{
                                        playing:
                                            currentTrack?.id === track.id &&
                                            isPlaying,
                                    }"
                                >
                                    <Play
                                        v-if="
                                            currentTrack?.id !== track.id ||
                                            !isPlaying
                                        "
                                        size="16"
                                    />
                                    <Pause v-else size="16" />
                                </button>
                            </div>
                            <div class="track-info">
                                <div class="track-title">
                                    {{ track.title }}
                                    <span
                                        v-if="track.is_explicit"
                                        class="explicit-badge"
                                        >E</span
                                    >
                                </div>
                                <div class="track-meta">
                                    <span class="duration">
                                        <Music size="14" class="meta-icon" />
                                        {{
                                            formatDuration(
                                                track.duration_seconds
                                            )
                                        }}
                                    </span>
                                    <span class="file-type">
                                        {{
                                            track.file_type?.toUpperCase() ||
                                            "MP3"
                                        }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="no-tracks">
                    <Music size="32" class="empty-icon" />
                    <p class="empty-text">Треки еще не добавлены</p>
                </div>
            </div>

            <!-- Встроенный плеер -->
            <div v-if="currentTrack" class="mini-player">
                <div class="player-info">
                    <div class="track-details">
                        <span class="track-title">{{
                            currentTrack.title
                        }}</span>
                        <span class="track-artist">{{
                            release.artist?.stage_name ||
                            release.artist?.display_name ||
                            "Неизвестный артист"
                        }}</span>
                    </div>
                    <div class="time-info">
                        <span class="current-time">{{
                            formatTime(currentTime)
                        }}</span>
                        <span class="separator">/</span>
                        <span class="total-time">{{
                            formatTime(duration)
                        }}</span>
                    </div>
                </div>

                <div class="progress-container" @click="seekTo">
                    <div class="progress-bar">
                        <div
                            class="progress-fill"
                            :style="{ width: progressPercentage + '%' }"
                        ></div>
                    </div>
                </div>
            </div>

            <!-- Скрытый аудио элемент -->
            <audio
                ref="audioElement"
                @loadedmetadata="onLoadedMetadata"
                @timeupdate="onTimeUpdate"
                @ended="onTrackEnded"
                preload="metadata"
            ></audio>
        </div>
    </DashboardLayout>
</template>

<script setup>
import { Head, Link } from "@inertiajs/vue3";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import { Music, Calendar, User, Play, Pause } from "lucide-vue-next";
import { ref, computed } from "vue";

const props = defineProps({
    release: Object,
    canEdit: Boolean,
});

// Плеер состояние
const currentTrack = ref(null);
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const audioElement = ref(null);
const hoveredTrack = ref(null);

function getReleaseTypeLabel(type) {
    switch (type) {
        case "album":
            return "Альбом";
        case "ep":
            return "EP";
        case "single":
            return "Сингл";
        default:
            return type;
    }
}

function getStatusLabel(status) {
    switch (status) {
        case "draft":
            return "Черновик";
        case "pending":
            return "На рассмотрении";
        case "approved":
            return "Одобрен";
        case "published":
            return "Опубликован";
        case "rejected":
            return "Отклонен";
        default:
            return status;
    }
}

function getTotalDuration() {
    if (!props.release.tracks || props.release.tracks.length === 0) {
        return 0;
    }

    return props.release.tracks.reduce((total, track) => {
        return total + (track.duration_seconds || 0);
    }, 0);
}

// Плеер функции
function playTrack(track) {
    if (currentTrack.value?.id === track.id && isPlaying.value) {
        pauseTrack();
        return;
    }

    currentTrack.value = track;

    if (audioElement.value) {
        // Определяем правильный путь к файлу
        let trackUrl;
        if (track.file_path.startsWith("temp/uploads/")) {
            trackUrl = `/storage/${track.file_path}`;
        } else if (track.file_path.startsWith("tracks/")) {
            trackUrl = `/storage/${track.file_path}`;
        } else {
            // Fallback для старых путей
            trackUrl = `/storage/tracks/${track.file_path}`;
        }

        console.log("Trying to play:", trackUrl);
        audioElement.value.src = trackUrl;
        audioElement.value.load();

        audioElement.value
            .play()
            .then(() => {
                isPlaying.value = true;
                console.log("Playing successfully:", trackUrl);
                updateMediaSession();
            })
            .catch((error) => {
                console.error("Error playing audio:", error);
                console.error("Failed URL:", trackUrl);
            });
    }
}

function pauseTrack() {
    if (audioElement.value) {
        audioElement.value.pause();
        isPlaying.value = false;
    }
}

function onTimeUpdate() {
    if (audioElement.value) {
        currentTime.value = audioElement.value.currentTime;

        // Обновляем Media Session позицию
        if ("mediaSession" in navigator && duration.value > 0) {
            navigator.mediaSession.setPositionState({
                duration: duration.value,
                playbackRate: 1,
                position: currentTime.value,
            });
        }
    }
}

function onLoadedMetadata() {
    if (audioElement.value) {
        duration.value = audioElement.value.duration || 0;
        updateMediaSession();
    }
}

function onTrackEnded() {
    isPlaying.value = false;
    currentTime.value = 0;
}

function seekTo(event) {
    if (!audioElement.value || duration.value === 0) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration.value;

    audioElement.value.currentTime = newTime;
    currentTime.value = newTime;
}

const progressPercentage = computed(() => {
    if (duration.value === 0) return 0;
    return (currentTime.value / duration.value) * 100;
});

// Media Session API
function updateMediaSession() {
    if ("mediaSession" in navigator && currentTrack.value) {
        navigator.mediaSession.metadata = new MediaMetadata({
            title: currentTrack.value.title,
            artist:
                props.release.artist?.stage_name ||
                props.release.artist?.display_name ||
                "Неизвестный артист",
            album: props.release.title,
            artwork: [
                {
                    src: props.release.cover_image
                        ? `${window.location.origin}/storage/${props.release.cover_image}`
                        : `${window.location.origin}/images/covers/placeholder.png`,
                    sizes: "512x512",
                    type: "image/png",
                },
            ],
        });

        navigator.mediaSession.setActionHandler("play", () => {
            if (audioElement.value) {
                audioElement.value.play();
            }
        });

        navigator.mediaSession.setActionHandler("pause", () => {
            pauseTrack();
        });

        navigator.mediaSession.setActionHandler("seekto", (details) => {
            if (audioElement.value && details.seekTime) {
                audioElement.value.currentTime = details.seekTime;
                currentTime.value = details.seekTime;
            }
        });

        // Обновляем позицию воспроизведения
        navigator.mediaSession.setPositionState({
            duration: duration.value,
            playbackRate: 1,
            position: currentTime.value,
        });
    }
}

function formatDate(dateString) {
    if (!dateString) return "";
    const options = { day: "2-digit", month: "2-digit", year: "numeric" };
    return new Date(dateString).toLocaleDateString("ru-RU", options);
}

function formatDuration(seconds) {
    if (!seconds) return "0:00";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
}

function formatTime(seconds) {
    if (!seconds || isNaN(seconds)) return "0:00";

    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
}
</script>

<style scoped>
.content-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 16px 40px;
}
.actions-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.back-btn {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}
.btn {
    padding: 8px 18px;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
    text-decoration: none;
}
.btn-primary {
    background-color: var(--primary-color);
    color: white;
}
.release-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    margin-bottom: 16px;
    font-weight: 500;
}
.release-main {
    display: flex;
    gap: 32px;
    margin-bottom: 32px;
}
.release-cover img {
    width: 220px;
    height: 220px;
    object-fit: cover;
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);

    &:hover {
        transform: scale(1.02);
        box-shadow: var(--shadow-lg);
    }
}
.release-info {
    flex: 1;
}
.release-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 12px;
}
.release-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 16px;
    color: var(--text-secondary);
}
.meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.95rem;
}
.release-description {
    margin-top: 18px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: var(--radius-lg);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    transition: var(--transition-fast);

    &.status-draft {
        background: var(--bg-secondary);
        color: var(--text-secondary);
    }

    &.status-pending {
        background: rgba(var(--warning-color-rgb), 0.1);
        color: var(--warning-color);
    }

    &.status-approved {
        background: rgba(var(--success-color-rgb), 0.1);
        color: var(--success-color);
    }

    &.status-published {
        background: rgba(var(--success-color-rgb), 0.1);
        color: var(--success-color);
    }

    &.status-rejected {
        background: rgba(var(--danger-color-rgb), 0.1);
        color: var(--danger-color);
    }
}

.release-stats {
    display: flex;
    gap: 24px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 12px;
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    transition: var(--transition-fast);

    &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-sm);
    }
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 12px;
}
.tracks-section {
    margin-top: 32px;
}
.tracks-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}
.track-item {
    display: flex;
    align-items: center;
    gap: 18px;
    padding: 12px 16px;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    transition: var(--transition-fast);
    margin-bottom: 4px;

    &:hover {
        background-color: var(--bg-secondary);
        border-color: var(--border-color);
        transform: translateY(-1px);
    }
}
.track-number-container {
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.track-number {
    font-weight: 600;
    color: var(--primary-color);
}

.play-button {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);

    &:hover {
        background: var(--primary-color);
        color: white;
        transform: scale(1.1);
    }

    &.playing {
        background: var(--primary-color);
        color: white;
    }
}
.track-info {
    flex: 1;
}
.track-title {
    font-size: 1.05rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}
.explicit-badge {
    background: var(--danger-color);
    color: #fff;
    border-radius: 4px;
    font-size: 0.7em;
    padding: 2px 6px;
    margin-left: 6px;
}
.track-meta {
    display: flex;
    gap: 16px;
    color: var(--text-secondary);
    font-size: 0.92em;
    margin-top: 2px;
}
.no-tracks {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    margin-top: 16px;
}
.empty-icon {
    opacity: 0.3;
}

/* Мини-плеер */
.mini-player {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(
        135deg,
        var(--primary-color) 0%,
        rgba(var(--primary-color-rgb), 0.8) 100%
    );
    color: white;
    padding: 16px 24px;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    animation: slideUp var(--transition-normal) ease-out;
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

.player-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.track-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.track-details .track-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.track-details .track-artist {
    font-size: 14px;
    opacity: 0.8;
}

.time-info {
    font-size: 14px;
    font-weight: 500;
}

.time-info .separator {
    margin: 0 4px;
    opacity: 0.6;
}

.progress-container {
    cursor: pointer;
}

.progress-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: white;
    border-radius: var(--radius-sm);
    transition: width var(--transition-fast);
}

@media (max-width: 768px) {
    .mini-player {
        padding: 12px 16px;
    }

    .player-info {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .time-info {
        align-self: flex-end;
    }
}
</style>
