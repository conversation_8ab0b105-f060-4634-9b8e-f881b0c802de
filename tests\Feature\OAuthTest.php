<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\OAuthService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Laravel\Socialite\Facades\Socialite;
use Lara<PERSON>\Socialite\Two\User as SocialiteUser;
use Mockery;
use Tests\TestCase;

class OAuthTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    /** @test */
    public function it_redirects_to_oauth_provider()
    {
        $response = $this->get('/auth/google');
        
        $this->assertTrue($response->isRedirection());
    }

    /** @test */
    public function it_rejects_unsupported_provider()
    {
        $response = $this->get('/auth/unsupported');
        
        $response->assertRedirect('/login');
        $response->assertSessionHasErrors(['provider']);
    }

    /** @test */
    public function it_creates_new_user_from_oauth()
    {
        // Настраиваем Google как валидный провайдер
        config(['services.google.client_id' => 'test_id']);
        config(['services.google.client_secret' => 'test_secret']);

        $socialiteUser = Mockery::mock(SocialiteUser::class);
        $socialiteUser->shouldReceive('getId')->andReturn('123456');
        $socialiteUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $socialiteUser->shouldReceive('getName')->andReturn('Test User');
        $socialiteUser->shouldReceive('getNickname')->andReturn('testuser');
        $socialiteUser->shouldReceive('getAvatar')->andReturn('https://example.com/avatar.jpg');
        $socialiteUser->shouldReceive('getRaw')->andReturn(['id' => '123456']);
        $socialiteUser->token = 'access_token';
        $socialiteUser->refreshToken = 'refresh_token';

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();
        
        Socialite::shouldReceive('user')
            ->andReturn($socialiteUser);

        $response = $this->get('/auth/google/callback');

        $response->assertRedirect('/dashboard');
        
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'provider' => 'google',
            'provider_id' => '123456',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals('artist', $user->role);
        $this->assertNull($user->password);
        $this->assertEquals('google', $user->provider);
        $this->assertEquals('123456', $user->provider_id);
    }

    /** @test */
    public function it_updates_existing_user_with_oauth_data()
    {
        // Настраиваем Google как валидный провайдер
        config(['services.google.client_id' => 'test_id']);
        config(['services.google.client_secret' => 'test_secret']);

        $existingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Existing User',
            'provider' => null,
            'provider_id' => null,
        ]);

        $socialiteUser = Mockery::mock(SocialiteUser::class);
        $socialiteUser->shouldReceive('getId')->andReturn('123456');
        $socialiteUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $socialiteUser->shouldReceive('getName')->andReturn('Test User');
        $socialiteUser->shouldReceive('getNickname')->andReturn('testuser');
        $socialiteUser->shouldReceive('getAvatar')->andReturn('https://example.com/avatar.jpg');
        $socialiteUser->shouldReceive('getRaw')->andReturn(['id' => '123456']);
        $socialiteUser->token = 'access_token';
        $socialiteUser->refreshToken = 'refresh_token';

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();
        
        Socialite::shouldReceive('user')
            ->andReturn($socialiteUser);

        $response = $this->get('/auth/google/callback');

        $response->assertRedirect('/dashboard');
        
        $existingUser->refresh();
        $this->assertEquals('google', $existingUser->provider);
        $this->assertEquals('123456', $existingUser->provider_id);
        $this->assertEquals('access_token', $existingUser->provider_token);
    }

    /** @test */
    public function oauth_service_returns_available_providers()
    {
        config(['services.google.client_id' => 'test_id']);
        config(['services.google.client_secret' => 'test_secret']);
        config(['services.vkontakte.client_id' => null]);

        $providers = OAuthService::getAvailableProviders();

        $this->assertArrayHasKey('google', $providers);
        $this->assertArrayHasKey('vkontakte', $providers);
        $this->assertTrue($providers['google']['enabled']);
        $this->assertFalse($providers['vkontakte']['enabled']);
    }

    /** @test */
    public function oauth_service_returns_only_enabled_providers()
    {
        config(['services.google.client_id' => 'test_id']);
        config(['services.google.client_secret' => 'test_secret']);
        config(['services.vkontakte.client_id' => null]);

        $enabledProviders = OAuthService::getEnabledProviders();

        $this->assertCount(1, $enabledProviders);
        $this->assertArrayHasKey('google', $enabledProviders);
        $this->assertArrayNotHasKey('vkontakte', $enabledProviders);
    }

    /** @test */
    public function user_can_check_if_oauth_user()
    {
        $oauthUser = User::factory()->create([
            'provider' => 'google',
            'provider_id' => '123456',
        ]);

        $regularUser = User::factory()->create([
            'provider' => null,
            'provider_id' => null,
        ]);

        $this->assertTrue($oauthUser->isOAuthUser());
        $this->assertFalse($regularUser->isOAuthUser());
    }

    /** @test */
    public function user_can_check_if_has_password()
    {
        $userWithPassword = User::factory()->create([
            'password' => bcrypt('password'),
        ]);

        $oauthUser = User::factory()->create([
            'password' => null,
            'provider' => 'google',
            'provider_id' => '123456',
        ]);

        $this->assertTrue($userWithPassword->hasPassword());
        $this->assertFalse($oauthUser->hasPassword());
    }

    /** @test */
    public function user_cannot_unlink_oauth_without_password()
    {
        $oauthUser = User::factory()->create([
            'password' => null,
            'provider' => 'google',
            'provider_id' => '123456',
        ]);

        $this->actingAs($oauthUser);

        $response = $this->post('/auth/google/unlink');

        $response->assertRedirect();
        $response->assertSessionHasErrors(['oauth']);
        
        $oauthUser->refresh();
        $this->assertEquals('google', $oauthUser->provider);
    }

    /** @test */
    public function user_can_unlink_oauth_with_password()
    {
        $user = User::factory()->create([
            'password' => bcrypt('password'),
            'provider' => 'google',
            'provider_id' => '123456',
        ]);

        $this->actingAs($user);

        $response = $this->post('/auth/google/unlink');

        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $user->refresh();
        $this->assertNull($user->provider);
        $this->assertNull($user->provider_id);
    }

    /** @test */
    public function oauth_service_validates_providers()
    {
        config(['services.google.client_id' => 'test_id']);
        config(['services.google.client_secret' => 'test_secret']);

        $this->assertTrue(OAuthService::isValidProvider('google'));
        $this->assertFalse(OAuthService::isValidProvider('facebook'));
        $this->assertFalse(OAuthService::isValidProvider('invalid'));
    }

    /** @test */
    public function oauth_service_gets_provider_display_name()
    {
        $this->assertEquals('Google', OAuthService::getProviderDisplayName('google'));
        $this->assertEquals('ВКонтакте', OAuthService::getProviderDisplayName('vkontakte'));
        $this->assertEquals('Facebook', OAuthService::getProviderDisplayName('facebook'));
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
