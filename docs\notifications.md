# Система уведомлений Madd Label

## Обзор

Система уведомлений обеспечивает информирование пользователей о важных событиях в приложении, таких как изменение статуса заявок, новые заявки для администраторов и другие системные события.

## Архитектура

### Backend (Laravel)

#### События (Events)
- `ApplicationCreated` - срабатывает при создании новой заявки
- `ApplicationStatusUpdated` - срабатывает при изменении статуса заявки

#### Слушатели (Listeners)
- `SendNewApplicationNotification` - отправляет уведомления админам о новых заявках
- `SendApplicationStatusNotification` - отправляет уведомления пользователям об изменении статуса

#### Уведомления (Notifications)
- `NewApplicationReceived` - уведомление для админов о новой заявке
- `ApplicationStatusChanged` - уведомление пользователю об изменении статуса заявки

#### API Endpoints
- `GET /api/notifications` - получить список уведомлений
- `GET /api/notifications/unread-count` - количество непрочитанных уведомлений
- `POST /api/notifications/{id}/read` - отметить уведомление как прочитанное
- `POST /api/notifications/mark-all-read` - отметить все как прочитанные
- `DELETE /api/notifications/{id}` - удалить уведомление
- `DELETE /api/notifications` - очистить все уведомления

### Frontend (Vue 3)

#### Pinia Store
`useNotificationStore` - централизованное управление состоянием уведомлений:
- Загрузка уведомлений
- Управление счетчиком непрочитанных
- Real-time подключения (когда настроен broadcasting)

#### Компоненты
- `NotificationCenter.vue` - центр уведомлений в header
- `Header.vue` - кнопка уведомлений с счетчиком

## Настройка

### Broadcasting (опционально)

Для real-time уведомлений можно настроить Pusher или Socket.io:

1. Установить зависимости:
```bash
npm install pusher-js
```

2. Настроить .env:
```env
BROADCAST_CONNECTION=pusher
PUSHER_APP_ID=your_app_id
PUSHER_APP_KEY=your_app_key
PUSHER_APP_SECRET=your_app_secret
PUSHER_APP_CLUSTER=your_cluster
```

3. Раскомментировать Pusher конфигурацию в `resources/js/bootstrap.js`

### Тестирование

Запуск тестов:
```bash
php artisan test --filter=NotificationTest
php artisan test --filter=NotificationEndToEndTest
```

Тестовая страница: `/test-notifications` (только для разработки)

## Использование

### Отправка уведомлений

```php
// Отправка уведомления конкретному пользователю
$user->notify(new ApplicationStatusChanged($application, $oldStatus, $newStatus));

// Отправка уведомления группе пользователей
$admins = User::whereIn('role', ['admin', 'manager'])->get();
Notification::send($admins, new NewApplicationReceived($application));
```

### Работа с уведомлениями во frontend

```javascript
import { useNotificationStore } from '@/Stores/notifications';

const notificationStore = useNotificationStore();

// Загрузить уведомления
await notificationStore.loadNotifications();

// Отметить как прочитанное
await notificationStore.markAsRead(notificationId);

// Настроить real-time слушатели
notificationStore.setupRealTimeListeners(user);
```

## Безопасность

- Пользователи могут видеть только свои уведомления
- API endpoints защищены middleware `auth:sanctum`
- Broadcasting каналы имеют авторизацию

## Расширение

Для добавления новых типов уведомлений:

1. Создать новый класс уведомления:
```bash
php artisan make:notification YourNotification
```

2. Добавить событие и слушатель:
```bash
php artisan make:event YourEvent
php artisan make:listener YourListener
```

3. Зарегистрировать в `EventServiceProvider`

4. Обновить frontend для обработки нового типа уведомлений

## Статус

✅ **Завершено:**
- Backend система уведомлений
- API endpoints
- Frontend компоненты
- Pinia store
- Тестирование
- Broadcasting каналы (настроены, но используется null driver)

🔄 **В разработке:**
- Real-time уведомления (требует настройки Pusher/Socket.io)
- Email уведомления
- Push уведомления
