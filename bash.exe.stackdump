Stack trace:
Frame         Function      Args
0007FFFF7B40  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF6A40) msys-2.0.dll+0x2118E
0007FFFF7B40  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF7E18) msys-2.0.dll+0x69BA
0007FFFF7B40  0002100469F2 (00021028DF99, 0007FFFF79F8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF7B40  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF7B40  00021006A545 (0007FFFF7B50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF7E20  00021006B9A5 (0007FFFF7B50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF39D40000 ntdll.dll
7FFF37D90000 KERNEL32.DLL
7FFF374C0000 KERNELBASE.dll
7FFF37B20000 USER32.dll
7FFF37960000 win32u.dll
7FFF37AE0000 GDI32.dll
7FFF37380000 gdi32full.dll
7FFF378B0000 msvcp_win.dll
7FFF37990000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF38C20000 advapi32.dll
7FFF388F0000 msvcrt.dll
7FFF38F40000 sechost.dll
7FFF39B70000 RPCRT4.dll
7FFF36490000 CRYPTBASE.DLL
7FFF36E90000 bcryptPrimitives.dll
7FFF38160000 IMM32.DLL
7FFF0F260000 windhawk.dll
7FFF131F0000 shrink-address-bar-height_1.0.1_195656.dll
