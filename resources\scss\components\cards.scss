.card {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: box-shadow var(--transition-fast),
        transform var(--transition-fast);

    &:hover {
        box-shadow: var(--shadow-md);
    }

    &.card-hover:hover {
        transform: translateY(-4px);
    }

    .card-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--bg-secondary);
        display: flex;
        align-items: center;
        justify-content: space-between;

        .card-title {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
    }

    .card-body {
        padding: 1.5rem;
    }

    .card-footer {
        padding: 1rem 1.5rem;
        border-top: 1px solid var(--bg-secondary);
    }
}

// Карточка релиза
.release-card {
    display: flex;
    align-items: center;
    padding: 1rem;

    .release-cover {
        width: 80px;
        height: 80px;
        border-radius: var(--radius-md);
        overflow: hidden;
        margin-right: 1rem;
        flex-shrink: 0;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .release-info {
        flex: 1;

        .release-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .release-artist {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }

        .release-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 0.75rem;
            color: var(--text-secondary);

            .meta-item {
                display: flex;
                align-items: center;

                .icon {
                    margin-right: 0.25rem;
                }
            }
        }
    }

    .release-actions {
        display: flex;
        gap: 0.5rem;
    }
}

// Карточка трека
.track-card {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);

    &:hover {
        background-color: var(--bg-secondary);
    }

    .track-number {
        width: 24px;
        font-size: 0.875rem;
        color: var(--text-secondary);
        text-align: center;
        margin-right: 1rem;
    }

    .track-info {
        flex: 1;

        .track-title {
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }

        .track-artist {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }
    }

    .track-duration {
        font-size: 0.75rem;
        color: var(--text-secondary);
        margin-right: 1rem;
    }

    .track-actions {
        display: flex;
        gap: 0.5rem;
    }
}

// Статус-бейдж
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 1rem;
    font-weight: 500;

    &.status-draft {
        background-color: rgba(97, 97, 97, 0.15);
        color: #616161;
    }

    &.status-pending {
        background-color: rgba(255, 152, 0, 0.15);
        color: #ff9800;
    }

    &.status-approved {
        background-color: rgba(76, 175, 80, 0.15);
        color: #4caf50;
    }

    &.status-rejected {
        background-color: rgba(244, 67, 54, 0.15);
        color: #f44336;
    }

    .icon {
        font-size: 0.75rem;
        margin-right: 0.25rem;
    }
}
