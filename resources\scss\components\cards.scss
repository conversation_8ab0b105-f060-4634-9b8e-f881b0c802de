// Современная система карточек 2025
.card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;

    // Эффект стекломорфизма
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        z-index: -1;
        border-radius: inherit;
    }

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-2xl);
        border-color: var(--primary-color);
    }

    // Варианты карточек
    &.card-elevated {
        box-shadow: var(--shadow-lg);

        &:hover {
            box-shadow: var(--shadow-2xl);
        }
    }

    &.card-outlined {
        border: 2px solid var(--border-medium);
        box-shadow: none;

        &:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-glow);
        }
    }

    &.card-filled {
        background: var(--bg-secondary);
        border: none;
    }

    &.card-glass {
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    // Размеры карточек
    &.card-sm {
        border-radius: var(--radius-lg);
    }

    &.card-lg {
        border-radius: var(--radius-2xl);
    }

    &.card-xl {
        border-radius: var(--radius-3xl);
    }

    .card-header {
        padding: 24px 24px 0;
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 24px;
        position: relative;

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 24px;
            right: 24px;
            height: 1px;
            background: var(--primary-gradient);
            opacity: 0.3;
        }

        .card-title {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .card-subtitle {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin: 0 0 16px 0;
            font-weight: var(--font-weight-medium);
        }

        .card-actions {
            display: flex;
            gap: 8px;
            margin-top: 16px;
        }
    }

    .card-body {
        padding: 24px;

        .card-text {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    .card-footer {
        padding: 16px 24px 24px;
        border-top: 1px solid var(--border-color);
        background: var(--bg-secondary);
        margin-top: auto;

        .card-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 12px;

            &.card-actions-center {
                justify-content: center;
            }

            &.card-actions-end {
                justify-content: flex-end;
            }
        }
    }
}

// Изображение карточки
.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;

    &.card-image-sm {
        height: 150px;
    }

    &.card-image-lg {
        height: 250px;
    }

    &.card-image-xl {
        height: 300px;
    }
}

// Карточка с изображением
.card-with-image {
    .card-image-container {
        position: relative;
        overflow: hidden;

        .card-image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, transparent 100%);
            display: flex;
            align-items: flex-end;
            padding: 24px;

            .overlay-content {
                color: var(--text-inverse);

                .overlay-title {
                    font-size: var(--font-size-lg);
                    font-weight: var(--font-weight-bold);
                    margin-bottom: 8px;
                }

                .overlay-text {
                    font-size: var(--font-size-sm);
                    opacity: 0.9;
                }
            }
        }
    }
}

// Карточка релиза - современный дизайн
.release-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--primary-gradient);
        opacity: 0;
        transition: all var(--transition-normal);
        z-index: -1;
    }

    &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-color);

        &::before {
            left: 0;
            opacity: 0.05;
        }

        .release-cover {
            transform: scale(1.05);
        }
    }

    .release-cover {
        width: 80px;
        height: 80px;
        border-radius: var(--radius-lg);
        overflow: hidden;
        margin-right: 20px;
        flex-shrink: 0;
        transition: transform var(--transition-bounce);
        border: 2px solid var(--border-color);

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .release-info {
        flex: 1;

        .release-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-bold);
            margin-bottom: 6px;
            color: var(--text-primary);
        }

        .release-artist {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: 12px;
            font-weight: var(--font-weight-medium);
        }

        .release-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);

            .meta-item {
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 4px 8px;
                background: var(--bg-secondary);
                border-radius: var(--radius-md);

                .icon {
                    color: var(--primary-color);
                }
            }
        }
    }

    .release-actions {
        display: flex;
        gap: 8px;
    }
}

// Карточка трека - современный дизайн
.track-card {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--primary-gradient);
        opacity: 0;
        transition: all var(--transition-normal);
        z-index: -1;
    }

    &:hover {
        background: var(--bg-secondary);
        border-color: var(--primary-color);
        transform: translateX(4px);
        box-shadow: var(--shadow-md);

        &::before {
            left: 0;
            opacity: 0.05;
        }

        .track-number {
            color: var(--primary-color);
            transform: scale(1.1);
        }
    }

    .track-number {
        width: 32px;
        height: 32px;
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        text-align: center;
        margin-right: 16px;
        background: var(--bg-tertiary);
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: var(--font-weight-semibold);
        transition: all var(--transition-bounce);
    }

    .track-info {
        flex: 1;

        .track-title {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-semibold);
            margin-bottom: 4px;
            color: var(--text-primary);
        }

        .track-artist {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
        }
    }

    .track-duration {
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
        margin-right: 16px;
        font-weight: var(--font-weight-medium);
        padding: 4px 8px;
        background: var(--bg-tertiary);
        border-radius: var(--radius-sm);
    }

    .track-actions {
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: opacity var(--transition-normal);
    }

    &:hover .track-actions {
        opacity: 1;
    }
}

// Статус-бейдж - современный дизайн
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    font-size: var(--font-size-xs);
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid transparent;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left var(--transition-slow);
    }

    &:hover::before {
        left: 100%;
    }

    &.status-draft {
        background: linear-gradient(135deg, rgba(97, 97, 97, 0.15) 0%, rgba(97, 97, 97, 0.05) 100%);
        color: #616161;
        border-color: rgba(97, 97, 97, 0.3);

        &:hover {
            background: linear-gradient(135deg, rgba(97, 97, 97, 0.25) 0%, rgba(97, 97, 97, 0.15) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(97, 97, 97, 0.2);
        }
    }

    &.status-pending {
        background: linear-gradient(135deg, rgba(255, 152, 0, 0.15) 0%, rgba(255, 152, 0, 0.05) 100%);
        color: #ff9800;
        border-color: rgba(255, 152, 0, 0.3);

        &:hover {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.25) 0%, rgba(255, 152, 0, 0.15) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 152, 0, 0.2);
        }
    }

    &.status-approved {
        background: linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(76, 175, 80, 0.05) 100%);
        color: #4caf50;
        border-color: rgba(76, 175, 80, 0.3);

        &:hover {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.25) 0%, rgba(76, 175, 80, 0.15) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.2);
        }
    }

    &.status-rejected {
        background: linear-gradient(135deg, rgba(244, 67, 54, 0.15) 0%, rgba(244, 67, 54, 0.05) 100%);
        color: #f44336;
        border-color: rgba(244, 67, 54, 0.3);

        &:hover {
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.25) 0%, rgba(244, 67, 54, 0.15) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(244, 67, 54, 0.2);
        }
    }

    .icon {
        font-size: var(--font-size-xs);
        transition: transform var(--transition-bounce);
    }

    &:hover .icon {
        transform: scale(1.1);
    }
}

// Карточка статистики
.stats-card {
    background: var(--bg-gradient-surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: 24px;
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--primary-gradient);
        opacity: 0;
        transition: opacity var(--transition-normal);
        z-index: -1;
    }

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-2xl);
        border-color: var(--primary-color);

        &::before {
            opacity: 0.05;
        }

        .stats-icon {
            transform: scale(1.1);
        }

        .stats-value {
            color: var(--primary-color);
        }
    }

    .stats-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 16px;
        background: var(--primary-gradient);
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-inverse);
        transition: transform var(--transition-bounce);
    }

    .stats-value {
        font-size: var(--font-size-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin-bottom: 8px;
        transition: color var(--transition-normal);
    }

    .stats-label {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .stats-change {
        margin-top: 12px;
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-semibold);

        &.positive {
            color: var(--success-color);
        }

        &.negative {
            color: var(--danger-color);
        }
    }
}
