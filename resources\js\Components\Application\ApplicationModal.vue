<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { useForm } from "@inertiajs/vue3";
import {
    X,
    Upload,
    FileText,
    Send,
    Loader2,
    AlertCircle,
    CheckCircle,
    Clock,
    User,
    Calendar,
    MessageSquare,
    Paperclip,
    Download,
    Trash2,
    Eye,
    EyeOff,
} from "lucide-vue-next";
import FileUploader from "@/Components/FileUpload/FileUploader.vue";
import AttachmentsList from "@/Components/Application/AttachmentsList.vue";

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    application: {
        type: Object,
        default: null,
    },
    mode: {
        type: String,
        default: "create", // create, edit, view, review
    },
    canReview: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(["close", "submitted", "reviewed"]);

// Типы заявок
const applicationTypes = [
    {
        value: "promo",
        label: "Промо-кампания",
        description: "Продвижение релиза или артиста",
    },
    {
        value: "collaboration",
        label: "Сотрудничество",
        description: "Предложение о сотрудничестве",
    },
    { value: "release", label: "Релиз", description: "Подача релиза на лейбл" },
    { value: "custom", label: "Другое", description: "Индивидуальный запрос" },
];

// Статусы заявок
const statusConfig = {
    pending: { label: "На рассмотрении", color: "warning", icon: Clock },
    approved: { label: "Одобрена", color: "success", icon: CheckCircle },
    rejected: { label: "Отклонена", color: "danger", icon: X },
};

// Состояние компонента
const isVisible = ref(false);
const newAttachments = ref([]);

// Форма заявки
const form = useForm({
    type: "",
    title: "",
    description: "",
    attachments: [],
});

// Форма рецензирования
const reviewForm = useForm({
    status: "",
    admin_notes: "",
});

// Вычисляемые свойства
const isReadonly = computed(() => props.mode === "view");
const isReviewMode = computed(() => props.mode === "review");
const isCreateMode = computed(() => props.mode === "create");
const isEditMode = computed(() => props.mode === "edit");

const selectedType = computed(() => {
    return applicationTypes.find((type) => type.value === form.type);
});

const currentStatus = computed(() => {
    if (!props.application) return null;
    return statusConfig[props.application.status];
});

const modalTitle = computed(() => {
    switch (props.mode) {
        case "create":
            return "Новая заявка";
        case "edit":
            return "Редактировать заявку";
        case "review":
            return "Рассмотрение заявки";
        case "view":
        default:
            return "Просмотр заявки";
    }
});

const canSubmit = computed(() => {
    if (isReviewMode.value) {
        return reviewForm.status && !reviewForm.processing;
    }
    return form.type && form.title && form.description && !form.processing;
});

// Методы управления модальным окном
function openModal() {
    isVisible.value = true;
    document.body.style.overflow = "hidden";

    if (props.application) {
        loadApplicationData();
    } else {
        resetForm();
    }
}

function closeModal() {
    isVisible.value = false;
    document.body.style.overflow = "";
    resetForm();
    emit("close");
}

function resetForm() {
    form.reset();
    reviewForm.reset();
    newAttachments.value = [];
}

function loadApplicationData() {
    if (!props.application) return;

    form.type = props.application.type;
    form.title = props.application.title;
    form.description = props.application.description;

    // Очищаем новые вложения при загрузке существующей заявки
    newAttachments.value = [];
}

// Обработчики FileUploader
function onFilesAdded(files) {
    // Добавляем файлы в форму
    files.forEach((fileData) => {
        form.attachments.push(fileData.file);
    });
}

function onFileRemoved(fileData) {
    // Удаляем файл из формы
    const fileIndex = form.attachments.findIndex((f) => f === fileData.file);
    if (fileIndex !== -1) {
        form.attachments.splice(fileIndex, 1);
    }
}

function onFileError(error) {
    console.error("Ошибка загрузки файла:", error);
    // Можно показать уведомление пользователю
}

// Методы для работы с существующими вложениями
function downloadAttachment(attachment, index) {
    if (props.application) {
        window.open(`/storage/applications/${attachment.path}`, "_blank");
    }
}

function removeExistingAttachment(attachment, index) {
    // Для существующих вложений можно добавить логику удаления
    console.log("Remove existing attachment:", attachment);
}

// Отправка формы
function submitApplication() {
    if (!canSubmit.value) return;

    const url = isEditMode.value
        ? route("applications.update", props.application.id)
        : route("applications.store");

    const method = isEditMode.value ? "put" : "post";

    form[method](url, {
        onSuccess: () => {
            emit("submitted");
            closeModal();
        },
        preserveScroll: true,
    });
}

function submitReview() {
    if (!canSubmit.value || !props.application) return;

    reviewForm.post(route("applications.review", props.application.id), {
        onSuccess: () => {
            emit("reviewed");
            closeModal();
        },
        preserveScroll: true,
    });
}

// Форматирование размера файла
function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// Форматирование даты
function formatDate(date) {
    return new Date(date).toLocaleDateString("ru-RU", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
    });
}

// Watchers
watch(
    () => props.show,
    (newValue) => {
        if (newValue) {
            nextTick(() => openModal());
        } else {
            closeModal();
        }
    }
);

// Обработка клавиши Escape
function handleKeydown(event) {
    if (event.key === "Escape" && isVisible.value) {
        closeModal();
    }
}

// Подключение обработчика клавиш
watch(isVisible, (newValue) => {
    if (newValue) {
        document.addEventListener("keydown", handleKeydown);
    } else {
        document.removeEventListener("keydown", handleKeydown);
    }
});
</script>

<template>
    <!-- Backdrop -->
    <Transition name="modal-backdrop">
        <div v-if="isVisible" class="modal-backdrop" @click="closeModal"></div>
    </Transition>

    <!-- Modal -->
    <Transition name="modal">
        <div v-if="isVisible" class="modal-container">
            <div class="modal-content" @click.stop>
                <!-- Header -->
                <div class="modal-header">
                    <div class="header-content">
                        <h2 class="modal-title">{{ modalTitle }}</h2>

                        <!-- Статус заявки -->
                        <div
                            v-if="currentStatus && !isCreateMode"
                            class="status-badge"
                            :class="currentStatus.color"
                        >
                            <component
                                :is="currentStatus.icon"
                                size="16"
                                class="status-icon"
                            />
                            <span>{{ currentStatus.label }}</span>
                        </div>
                    </div>

                    <button
                        type="button"
                        class="close-btn"
                        @click="closeModal"
                        title="Закрыть"
                    >
                        <X size="20" />
                    </button>
                </div>

                <!-- Content -->
                <div class="modal-body">
                    <!-- Информация о заявке (режим просмотра) -->
                    <div
                        v-if="isReadonly && props.application"
                        class="application-info"
                    >
                        <div class="info-grid">
                            <div class="info-item">
                                <label class="info-label">Автор</label>
                                <div class="info-value">
                                    <User size="16" class="info-icon" />
                                    <span>{{
                                        props.application.user?.name ||
                                        "Неизвестно"
                                    }}</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <label class="info-label">Дата создания</label>
                                <div class="info-value">
                                    <Calendar size="16" class="info-icon" />
                                    <span>{{
                                        formatDate(props.application.created_at)
                                    }}</span>
                                </div>
                            </div>

                            <div
                                v-if="props.application.reviewed_at"
                                class="info-item"
                            >
                                <label class="info-label"
                                    >Дата рассмотрения</label
                                >
                                <div class="info-value">
                                    <Calendar size="16" class="info-icon" />
                                    <span>{{
                                        formatDate(
                                            props.application.reviewed_at
                                        )
                                    }}</span>
                                </div>
                            </div>

                            <div
                                v-if="props.application.reviewer"
                                class="info-item"
                            >
                                <label class="info-label">Рассмотрел</label>
                                <div class="info-value">
                                    <User size="16" class="info-icon" />
                                    <span>{{
                                        props.application.reviewer.name
                                    }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Форма заявки -->
                    <form
                        v-if="!isReviewMode"
                        @submit.prevent="submitApplication"
                        class="application-form"
                    >
                        <!-- Тип заявки -->
                        <div class="form-group">
                            <label for="type" class="form-label">
                                Тип заявки *
                            </label>
                            <select
                                id="type"
                                v-model="form.type"
                                class="form-select"
                                :class="{ error: form.errors.type }"
                                :disabled="isReadonly"
                                required
                            >
                                <option value="">Выберите тип заявки</option>
                                <option
                                    v-for="type in applicationTypes"
                                    :key="type.value"
                                    :value="type.value"
                                >
                                    {{ type.label }}
                                </option>
                            </select>

                            <!-- Описание типа -->
                            <p v-if="selectedType" class="form-hint">
                                {{ selectedType.description }}
                            </p>

                            <p v-if="form.errors.type" class="error-text">
                                {{ form.errors.type }}
                            </p>
                        </div>

                        <!-- Заголовок -->
                        <div class="form-group">
                            <label for="title" class="form-label">
                                Заголовок заявки *
                            </label>
                            <input
                                id="title"
                                v-model="form.title"
                                type="text"
                                class="form-input"
                                :class="{ error: form.errors.title }"
                                :readonly="isReadonly"
                                placeholder="Краткое описание вашей заявки"
                                required
                            />
                            <p v-if="form.errors.title" class="error-text">
                                {{ form.errors.title }}
                            </p>
                        </div>

                        <!-- Описание -->
                        <div class="form-group">
                            <label for="description" class="form-label">
                                Подробное описание *
                            </label>
                            <textarea
                                id="description"
                                v-model="form.description"
                                class="form-textarea"
                                :class="{ error: form.errors.description }"
                                :readonly="isReadonly"
                                placeholder="Подробно опишите вашу заявку, цели и ожидания..."
                                rows="6"
                                required
                            ></textarea>
                            <p
                                v-if="form.errors.description"
                                class="error-text"
                            >
                                {{ form.errors.description }}
                            </p>
                        </div>

                        <!-- Вложения -->
                        <div class="form-group">
                            <label class="form-label">
                                Вложения
                                <span class="optional-text"
                                    >(необязательно)</span
                                >
                            </label>

                            <!-- Существующие вложения -->
                            <AttachmentsList
                                v-if="
                                    props.application?.attachments?.length > 0
                                "
                                :attachments="props.application.attachments"
                                :readonly="isReadonly"
                                @download="downloadAttachment"
                                @remove="removeExistingAttachment"
                            />

                            <!-- Загрузчик новых файлов -->
                            <FileUploader
                                v-if="!isReadonly"
                                v-model="newAttachments"
                                :multiple="true"
                                :max-files="10"
                                :max-size="10 * 1024 * 1024"
                                :allowed-types="[
                                    'application/pdf',
                                    'application/msword',
                                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                    'text/plain',
                                    'image/jpeg',
                                    'image/png',
                                    'image/gif',
                                    'audio/mpeg',
                                    'audio/wav',
                                    'audio/flac',
                                    'application/zip',
                                    'application/x-rar-compressed',
                                ]"
                                accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.mp3,.wav,.flac,.zip,.rar"
                                upload-text="Перетащите файлы сюда или нажмите для выбора"
                                hint-text="PDF, DOC, TXT, изображения, аудио, архивы • Макс. 10MB на файл • До 10 файлов"
                                @files-added="onFilesAdded"
                                @file-removed="onFileRemoved"
                                @error="onFileError"
                            />

                            <p
                                v-if="form.errors.attachments"
                                class="error-text"
                            >
                                {{ form.errors.attachments }}
                            </p>
                        </div>
                    </form>

                    <!-- Форма рецензирования -->
                    <form
                        v-if="isReviewMode"
                        @submit.prevent="submitReview"
                        class="review-form"
                    >
                        <div class="review-header">
                            <h3 class="review-title">Рассмотрение заявки</h3>
                            <p class="review-subtitle">
                                Примите решение по заявке и оставьте комментарий
                                для автора
                            </p>
                        </div>

                        <!-- Решение -->
                        <div class="form-group">
                            <label class="form-label">Решение *</label>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input
                                        v-model="reviewForm.status"
                                        type="radio"
                                        value="approved"
                                        class="radio-input"
                                    />
                                    <span class="radio-custom success"></span>
                                    <span class="radio-text">
                                        <CheckCircle
                                            size="16"
                                            class="radio-icon"
                                        />
                                        Одобрить заявку
                                    </span>
                                </label>

                                <label class="radio-label">
                                    <input
                                        v-model="reviewForm.status"
                                        type="radio"
                                        value="rejected"
                                        class="radio-input"
                                    />
                                    <span class="radio-custom danger"></span>
                                    <span class="radio-text">
                                        <X size="16" class="radio-icon" />
                                        Отклонить заявку
                                    </span>
                                </label>
                            </div>
                            <p
                                v-if="reviewForm.errors.status"
                                class="error-text"
                            >
                                {{ reviewForm.errors.status }}
                            </p>
                        </div>

                        <!-- Комментарий -->
                        <div class="form-group">
                            <label for="admin_notes" class="form-label">
                                Комментарий для автора
                            </label>
                            <textarea
                                id="admin_notes"
                                v-model="reviewForm.admin_notes"
                                class="form-textarea"
                                :class="{
                                    error: reviewForm.errors.admin_notes,
                                }"
                                placeholder="Оставьте комментарий для автора заявки..."
                                rows="4"
                            ></textarea>
                            <p
                                v-if="reviewForm.errors.admin_notes"
                                class="error-text"
                            >
                                {{ reviewForm.errors.admin_notes }}
                            </p>
                        </div>
                    </form>

                    <!-- Комментарии администратора (режим просмотра) -->
                    <div
                        v-if="isReadonly && props.application?.admin_notes"
                        class="admin-notes"
                    >
                        <h4 class="notes-title">
                            <MessageSquare size="16" class="notes-icon" />
                            Комментарий администратора
                        </h4>
                        <div class="notes-content">
                            {{ props.application.admin_notes }}
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="modal-footer">
                    <button
                        type="button"
                        class="btn btn-outline"
                        @click="closeModal"
                    >
                        {{ isReadonly ? "Закрыть" : "Отмена" }}
                    </button>

                    <button
                        v-if="!isReadonly"
                        type="submit"
                        class="btn btn-primary"
                        :disabled="!canSubmit"
                        @click="
                            isReviewMode ? submitReview() : submitApplication()
                        "
                    >
                        <Loader2
                            v-if="form.processing || reviewForm.processing"
                            class="btn-icon spin"
                            size="16"
                        />
                        <Send v-else class="btn-icon" size="16" />
                        <span v-if="form.processing || reviewForm.processing">
                            {{
                                isReviewMode
                                    ? "Сохранение..."
                                    : isEditMode
                                    ? "Обновление..."
                                    : "Отправка..."
                            }}
                        </span>
                        <span v-else>
                            {{
                                isReviewMode
                                    ? "Сохранить решение"
                                    : isEditMode
                                    ? "Обновить заявку"
                                    : "Отправить заявку"
                            }}
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </Transition>
</template>

<style lang="scss" scoped>
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    overflow-y: auto;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 16px;
    margin-bottom: 24px;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;

    &.warning {
        background: rgba(255, 193, 7, 0.1);
        color: #856404;
    }

    &.success {
        background: rgba(40, 167, 69, 0.1);
        color: #155724;
    }

    &.danger {
        background: rgba(220, 53, 69, 0.1);
        color: #721c24;
    }
}

.status-icon {
    flex-shrink: 0;
}

.close-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.05);
        color: var(--text-primary);
    }
}

.modal-body {
    padding: 0 24px;
    overflow-y: auto;
    flex: 1;
}

.application-info {
    margin-bottom: 24px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-primary);
    font-weight: 500;
}

.info-icon {
    color: var(--primary-color);
    flex-shrink: 0;
}

.application-form,
.review-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.optional-text {
    color: var(--text-secondary);
    font-weight: 400;
    font-size: 0.75rem;
}

.form-input,
.form-textarea,
.form-select {
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }

    &:read-only {
        background: var(--bg-secondary);
        color: var(--text-secondary);
    }

    &.error {
        border-color: var(--danger-color);
    }
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-hint {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

.error-text {
    color: var(--danger-color);
    font-size: 0.75rem;
    margin: 0;
}

.attachments-list {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    overflow: hidden;
    margin-bottom: 16px;
}

.attachment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:last-child {
        border-bottom: none;
    }
}

.attachment-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.attachment-icon {
    color: var(--primary-color);
    flex-shrink: 0;
}

.attachment-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
}

.attachment-name {
    font-weight: 500;
    color: var(--text-primary);
    truncate: true;
}

.attachment-size {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.attachment-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &.download-btn {
        background: rgba(var(--primary-rgb), 0.1);
        color: var(--primary-color);

        &:hover {
            background: rgba(var(--primary-rgb), 0.2);
        }
    }

    &.remove-btn {
        background: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);

        &:hover {
            background: rgba(220, 53, 69, 0.2);
        }
    }
}

.upload-area {
    border: 2px dashed rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover,
    &.drag-over {
        border-color: var(--primary-color);
        background: rgba(var(--primary-rgb), 0.05);
    }
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.upload-icon {
    color: var(--text-secondary);
}

.upload-text {
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.upload-hint {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

.file-input {
    display: none;
}

.review-form {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: var(--radius-md);
    margin-bottom: 20px;
}

.review-header {
    margin-bottom: 20px;
}

.review-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px;
}

.review-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 12px;
    border-radius: var(--radius-md);
    transition: background-color 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.02);
    }
}

.radio-input {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    position: relative;
    transition: all 0.2s ease;

    &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    &.success::after {
        background: #28a745;
    }

    &.danger::after {
        background: #dc3545;
    }
}

.radio-input:checked + .radio-custom {
    &.success {
        border-color: #28a745;
    }

    &.danger {
        border-color: #dc3545;
    }

    &::after {
        opacity: 1;
    }
}

.radio-text {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: var(--text-primary);
}

.radio-icon {
    flex-shrink: 0;
}

.admin-notes {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: var(--radius-md);
    margin-bottom: 20px;
}

.notes-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 12px;
}

.notes-icon {
    color: var(--primary-color);
}

.notes-content {
    color: var(--text-primary);
    line-height: 1.6;
    white-space: pre-wrap;
}

.modal-footer {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    padding: 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.btn {
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    border: none;

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.btn-outline {
    background: transparent;
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: var(--text-primary);

    &:hover:not(:disabled) {
        background: rgba(0, 0, 0, 0.05);
    }
}

.btn-primary {
    background: var(--primary-color);
    color: white;

    &:hover:not(:disabled) {
        background: var(--primary-hover);
    }
}

.btn-icon {
    flex-shrink: 0;
}

.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// Transitions
.modal-backdrop-enter-active,
.modal-backdrop-leave-active {
    transition: opacity 0.3s ease;
}

.modal-backdrop-enter-from,
.modal-backdrop-leave-to {
    opacity: 0;
}

.modal-enter-active,
.modal-leave-active {
    transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

@media (max-width: 768px) {
    .modal-container {
        padding: 10px;
        align-items: flex-start;
        padding-top: 20px;
    }

    .modal-content {
        max-height: calc(100vh - 40px);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .radio-group {
        gap: 8px;
    }

    .radio-label {
        padding: 8px;
    }
}
</style>
