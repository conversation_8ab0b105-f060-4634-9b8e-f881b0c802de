<script setup>
import { computed } from "vue";
import {
    FileText,
    Image,
    Music,
    Archive,
    Download,
    Trash2,
    File,
} from "lucide-vue-next";

const props = defineProps({
    attachments: {
        type: Array,
        default: () => [],
    },
    readonly: {
        type: <PERSON><PERSON>an,
        default: false,
    },
});

const emit = defineEmits(["download", "remove"]);

// Утилиты
function formatFileSize(bytes) {
    if (!bytes || bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function getFileIcon(type) {
    if (!type || typeof type !== 'string') return File;
    if (type.startsWith("image/")) return Image;
    if (type.startsWith("audio/")) return Music;
    if (type.startsWith("text/") || type.includes("document")) return FileText;
    if (type.includes("zip") || type.includes("rar")) return Archive;
    return File;
}

function downloadAttachment(attachment, index) {
    emit("download", attachment, index);
}

function removeAttachment(attachment, index) {
    emit("remove", attachment, index);
}
</script>

<template>
    <div v-if="attachments.length > 0" class="attachments-list">
        <div
            v-for="(attachment, index) in attachments"
            :key="index"
            class="attachment-item"
        >
            <div class="attachment-info">
                <div class="attachment-icon">
                    <component 
                        :is="getFileIcon(attachment.mime_type || attachment.type)" 
                        size="20" 
                    />
                </div>
                <div class="attachment-details">
                    <span class="attachment-name" :title="attachment.original_name || attachment.name">
                        {{ attachment.original_name || attachment.name }}
                    </span>
                    <span class="attachment-size">
                        {{ formatFileSize(attachment.size) }}
                    </span>
                </div>
            </div>
            
            <div class="attachment-actions">
                <button
                    type="button"
                    class="action-btn download-btn"
                    @click="downloadAttachment(attachment, index)"
                    title="Скачать"
                >
                    <Download size="16" />
                </button>
                
                <button
                    v-if="!readonly"
                    type="button"
                    class="action-btn remove-btn"
                    @click="removeAttachment(attachment, index)"
                    title="Удалить"
                >
                    <Trash2 size="16" />
                </button>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.attachments-list {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    overflow: hidden;
    margin-bottom: 16px;
}

.attachment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background: rgba(0, 0, 0, 0.02);
    }
}

.attachment-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.attachment-icon {
    color: var(--primary-color);
    flex-shrink: 0;
}

.attachment-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
    flex: 1;
}

.attachment-name {
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.attachment-size {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.attachment-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &.download-btn {
        background: rgba(var(--primary-rgb), 0.1);
        color: var(--primary-color);

        &:hover {
            background: rgba(var(--primary-rgb), 0.2);
        }
    }

    &.remove-btn {
        background: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);

        &:hover {
            background: rgba(220, 53, 69, 0.2);
        }
    }
}

@media (max-width: 768px) {
    .attachment-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .attachment-info {
        width: 100%;
    }

    .attachment-actions {
        width: 100%;
        justify-content: flex-end;
    }
}
</style>
