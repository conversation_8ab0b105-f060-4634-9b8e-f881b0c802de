<?php

use App\Http\Controllers\Auth\OAuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\ApplicationController;
use App\Http\Controllers\ArtistAnalyticsController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\ReleaseController;
use App\Http\Controllers\TrackController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Публичные страницы
Route::get('/', function () {
    if (auth()->check()) {
        return redirect()->route('dashboard');
    }
    return redirect()->route('login');
})->name('welcome');

Route::get('/terms-of-service', function () {
    return Inertia::render('TermsOfService');
})->name('terms');

Route::get('/privacy-policy', function () {
    return Inertia::render('PrivacyPolicy');
})->name('privacy');

// OAuth маршруты
Route::prefix('auth')->name('oauth.')->group(function () {
    Route::get('{provider}', [OAuthController::class, 'redirect'])->name('redirect');
    Route::get('{provider}/callback', [OAuthController::class, 'callback'])->name('callback');
    Route::post('{provider}/unlink', [OAuthController::class, 'unlink'])->name('unlink')->middleware('auth');
});

// Защищенные маршруты для авторизованных пользователей
Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    // Дашборд
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Релизы
    Route::get('/releases', [ReleaseController::class, 'index'])->name('releases.index');
    Route::get('/releases/create', [ReleaseController::class, 'create'])->name('releases.create')->middleware('artist.profile');
    Route::post('/releases', [ReleaseController::class, 'store'])->name('releases.store')->middleware('artist.profile');
    Route::get('/releases/{release}', [ReleaseController::class, 'show'])->name('releases.show');
    Route::get('/releases/{release}/edit', [ReleaseController::class, 'edit'])->name('releases.edit');
    Route::put('/releases/{release}', [ReleaseController::class, 'update'])->name('releases.update');
    Route::delete('/releases/{release}', [ReleaseController::class, 'destroy'])->name('releases.destroy');
    Route::patch('/releases/{release}/status', [ReleaseController::class, 'updateStatus'])->name('releases.updateStatus');
    
    // Треки
    Route::get('/tracks', [TrackController::class, 'index'])->name('tracks.index');
    Route::post('/releases/{release}/tracks', [TrackController::class, 'store'])->name('tracks.store');
    Route::put('/tracks/{track}', [TrackController::class, 'update'])->name('tracks.update');
    Route::delete('/tracks/{track}', [TrackController::class, 'destroy'])->name('tracks.destroy');
    
    // Заявки
    Route::resource('applications', ApplicationController::class);
    Route::post('/applications/{application}/review', [ApplicationController::class, 'review'])->name('applications.review');
    Route::get('/applications/{application}/attachments/{attachmentIndex}', [ApplicationController::class, 'downloadAttachment'])->name('applications.download-attachment');
    
    // Аналитика (для артистов)
    Route::get('/analytics', [ArtistAnalyticsController::class, 'index'])->name('analytics.index');
    Route::get('/artist/analytics', [ArtistAnalyticsController::class, 'index'])->name('artist.analytics');
    Route::get('/artist/performance', [ArtistAnalyticsController::class, 'performance'])->name('artist.performance');

    // Админ-панель (только для админов)
    Route::middleware(['admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/analytics', [AdminController::class, 'analytics'])->name('analytics');

        // Управление заявками
        Route::get('/applications', [AdminController::class, 'applications'])->name('applications');
        Route::post('/applications/{application}/status', [AdminController::class, 'updateApplicationStatus'])->name('applications.status');
        Route::post('/applications/bulk', [AdminController::class, 'bulkApplicationAction'])->name('applications.bulk');

        // Управление релизами
        Route::get('/releases', [AdminController::class, 'releases'])->name('releases');
        Route::post('/releases/{release}/status', [AdminController::class, 'updateReleaseStatus'])->name('releases.status');

        // Управление пользователями
        Route::get('/users', [AdminController::class, 'users'])->name('users');
        Route::post('/users/{user}/role', [AdminController::class, 'updateUserRole'])->name('users.role');
    });
    
    // Профиль пользователя
    Route::get('/profile', function () {
        return Inertia::render('Profile/Show', [
            'sessions' => [],
            'confirmsTwoFactorAuthentication' => false
        ]);
    })->name('profile.show');

    // Маршрут для работы с профилем артиста
    Route::post('/artist-profile', function (Request $request) {
        $user = $request->user();
        $validatedData = $request->validate([
            'stage_name' => 'nullable|string|max:255',
            'bio' => 'nullable|string',
            'genre' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:100',
            'website' => 'nullable|url|max:255',
            'social_links' => 'nullable|array',
            'cover_image' => 'nullable|image|max:2048',
        ]);
        
        // Сохраняем обложку, если загружена
        if ($request->hasFile('cover_image')) {
            $coverPath = $request->file('cover_image')
                ->store('public/images/artists');
            $validatedData['cover_image'] = str_replace('public/', '', $coverPath);
        }
        
        if ($user->artist) {
            // Обновляем существующий профиль
            $user->artist->update($validatedData);
            $message = 'Профиль артиста обновлен успешно.';
        } else {
            // Создаем новый профиль артиста
            $user->artist()->create($validatedData);
            $message = 'Профиль артиста создан успешно.';
        }
        
        return redirect()->route('profile.show')->with('success', $message);
    })->name('artist-profile.update');
    
    // Тестовая страница уведомлений (только для разработки)
    Route::get('/test-notifications', function () {
        return Inertia::render('TestNotifications');
    })->name('test.notifications');

    // Маршруты для администраторов
    Route::group(['middleware' => 'admin', 'prefix' => 'admin', 'as' => 'admin.'], function () {
        Route::get('/users', function () {
            return redirect()->route('dashboard')->with('info', 'Управление пользователями находится в разработке');
        })->name('users');

        Route::get('/settings', function () {
            return redirect()->route('dashboard')->with('info', 'Настройки находятся в разработке');
        })->name('settings');
    });
});

// Маршруты для файлов (доступны всем авторизованным пользователям)
Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified'])->group(function () {
    // Изображения (публичные)
    Route::get('/storage/images/{path}', [FileController::class, 'serveImage'])->where('path', '.*')->name('files.image');
    Route::get('/storage/images/covers/{filename}', [FileController::class, 'serveCover'])->name('files.cover');

    // Треки (требуют авторизации)
    Route::get('/storage/tracks/{filename}', [FileController::class, 'serveTrack'])->name('files.track');

    // Вложения заявок (требуют авторизации)
    Route::get('/storage/applications/{filename}', [FileController::class, 'serveAttachment'])->name('files.attachment');
    Route::get('/storage/applications/{filename}/download', [FileController::class, 'downloadAttachment'])->name('files.attachment.download');

    // Временные файлы
    Route::get('/storage/temp/{filename}', [FileController::class, 'serveTempFile'])->name('files.temp');
    Route::get('/storage/temp/uploads/{filename}', [FileController::class, 'serveTempFile'])->name('files.temp.uploads');

    // Отладка (только для админов)
    Route::get('/files/list/{directory?}', [FileController::class, 'listFiles'])->where('directory', '.*')->name('files.list');
});
