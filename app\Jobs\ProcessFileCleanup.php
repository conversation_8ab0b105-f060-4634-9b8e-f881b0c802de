<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class ProcessFileCleanup implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 120; // 2 минуты
    public $tries = 3;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $filePaths,
        public string $reason = 'cleanup',
        public ?int $delaySeconds = null
    ) {
        $this->onQueue('files');
        
        // Если указана задержка, применяем её
        if ($delaySeconds) {
            $this->delay(now()->addSeconds($delaySeconds));
        }
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Начинаем очистку файлов. Причина: {$this->reason}");
            
            $deletedCount = 0;
            $errorCount = 0;

            foreach ($this->filePaths as $filePath) {
                try {
                    if ($this->deleteFile($filePath)) {
                        $deletedCount++;
                        Log::info("Файл удален: {$filePath}");
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    Log::error("Ошибка удаления файла {$filePath}: " . $e->getMessage());
                }
            }

            Log::info("Очистка завершена. Удалено: {$deletedCount}, ошибок: {$errorCount}");

        } catch (\Exception $e) {
            Log::error("Критическая ошибка при очистке файлов: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a single file and its related files.
     */
    private function deleteFile(string $filePath): bool
    {
        $deleted = false;

        // Удаляем основной файл
        if (Storage::disk('public')->exists($filePath)) {
            Storage::disk('public')->delete($filePath);
            $deleted = true;
        }

        // Удаляем связанные файлы (миниатюры, кэш и т.д.)
        $this->deleteRelatedFiles($filePath);

        return $deleted;
    }

    /**
     * Delete related files (thumbnails, cache, etc.).
     */
    private function deleteRelatedFiles(string $filePath): void
    {
        $pathInfo = pathinfo($filePath);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? '';

        // Удаляем миниатюры
        $thumbnailPatterns = [
            "{$directory}/thumbs/{$filename}.*",
            "{$directory}/{$filename}_thumb.*",
            "{$directory}/{$filename}_small.*",
            "{$directory}/{$filename}_medium.*",
        ];

        foreach ($thumbnailPatterns as $pattern) {
            $files = Storage::disk('public')->glob($pattern);
            foreach ($files as $file) {
                Storage::disk('public')->delete($file);
                Log::debug("Удален связанный файл: {$file}");
            }
        }

        // Удаляем кэш файлы
        $cachePatterns = [
            "{$directory}/cache/{$filename}.*",
            "{$directory}/.cache/{$filename}.*",
        ];

        foreach ($cachePatterns as $pattern) {
            $files = Storage::disk('public')->glob($pattern);
            foreach ($files as $file) {
                Storage::disk('public')->delete($file);
                Log::debug("Удален кэш файл: {$file}");
            }
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Не удалось выполнить очистку файлов: " . $exception->getMessage());
        Log::error("Файлы для удаления: " . implode(', ', $this->filePaths));
    }

    /**
     * Create a cleanup job for release files after approval.
     */
    public static function cleanupAfterApproval(array $filePaths, int $delayHours = 24): self
    {
        return new self(
            filePaths: $filePaths,
            reason: 'approved_release_cleanup',
            delaySeconds: $delayHours * 3600
        );
    }

    /**
     * Create a cleanup job for rejected application files.
     */
    public static function cleanupAfterRejection(array $filePaths, int $delayHours = 72): self
    {
        return new self(
            filePaths: $filePaths,
            reason: 'rejected_application_cleanup',
            delaySeconds: $delayHours * 3600
        );
    }

    /**
     * Create an immediate cleanup job for temporary files.
     */
    public static function cleanupTemporary(array $filePaths): self
    {
        return new self(
            filePaths: $filePaths,
            reason: 'temporary_cleanup'
        );
    }
}
