<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from "vue";
import { router } from "@inertiajs/vue3";
import {
    XCircle,
    BellRing,
    CheckCircle,
    AlertCircle,
    Info,
} from "lucide-vue-next";
import { clickOutside } from "@/Directives/clickOutside";
import { useNotificationStore } from "@/Stores/notifications";

const props = defineProps({
    unreadCount: {
        type: Number,
        default: 0,
    },
});

const emit = defineEmits(["close", "updateUnreadCount"]);

// Используем notification store
const notificationStore = useNotificationStore();

// Загрузка уведомлений при открытии
const loadNotifications = () => {
    // Уведомления уже инициализированы в Header.vue
    // Здесь можно добавить дополнительную логику если нужно
};

// Отметить уведомление как прочитанное
const markAsRead = async (notificationId) => {
    await notificationStore.markAsRead(notificationId);
    emit("updateUnreadCount");
};

// Отметить все как прочитанные
const markAllAsRead = async () => {
    await notificationStore.markAllAsRead();
    emit("updateUnreadCount");
};
// Фильтрация уведомлений
const filter = ref("all"); // 'all', 'unread', 'read'
const filteredNotifications = computed(() => {
    if (filter.value === "all") {
        return notificationStore.notifications;
    }
    return filter.value === "unread"
        ? notificationStore.unreadNotifications
        : notificationStore.readNotifications;
});

// Обработка клика по уведомлению
const handleNotificationClick = async (notification) => {
    if (!notification.read_at) {
        await markAsRead(notification.id);
    }

    // Если есть ссылка, перейти по ней
    if (notification.data?.action_url) {
        router.visit(notification.data.action_url);
        emit("close");
    }
};

// Удалить уведомление
const deleteNotification = async (notificationId) => {
    await notificationStore.deleteNotification(notificationId);
    emit("updateUnreadCount");
};

// Закрыть центр уведомлений
const closeNotifications = () => {
    emit("close");
};

// Получить иконку в зависимости от типа уведомления
const getNotificationIcon = (type) => {
    switch (type) {
        case "application_status_changed":
        case "new_application_received":
            return BellRing;
        case "release":
            return CheckCircle;
        case "analytics":
            return Info;
        case "system":
        default:
            return AlertCircle;
    }
};

// Загрузка уведомлений при монтировании
onMounted(() => {
    loadNotifications();
});

// Форматирование даты
const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 60) {
        return `${diffMins} мин. назад`;
    } else if (diffHours < 24) {
        return `${diffHours} ч. назад`;
    } else if (diffDays < 7) {
        return `${diffDays} дн. назад`;
    } else {
        return date.toLocaleDateString();
    }
};

// Обработка клика вне компонента
const handleClickOutside = () => {
    emit("close");
};
</script>

<template>
    <div class="notification-center" v-click-outside="handleClickOutside">
        <!-- Заголовок -->
        <div class="notification-header">
            <h3>Уведомления</h3>
            <button class="close-button" @click="closeNotifications">
                <XCircle size="20" />
            </button>
        </div>

        <!-- Фильтры -->
        <div class="notification-filters">
            <button
                class="filter-button"
                :class="{ active: filter === 'all' }"
                @click="filter = 'all'"
            >
                Все
            </button>
            <button
                class="filter-button"
                :class="{ active: filter === 'unread' }"
                @click="filter = 'unread'"
            >
                Новые
            </button>
            <button
                class="filter-button"
                :class="{ active: filter === 'read' }"
                @click="filter = 'read'"
            >
                Прочитанные
            </button>

            <button
                class="mark-all-read"
                @click="markAllAsRead"
                v-if="notificationStore.unreadCount > 0"
            >
                Прочитать все
            </button>
        </div>

        <!-- Список уведомлений -->
        <div class="notifications-list">
            <div v-if="notificationStore.loading" class="loading-state">
                <div class="loading-spinner"></div>
                <p>Загрузка уведомлений...</p>
            </div>

            <template v-else-if="filteredNotifications.length">
                <div
                    v-for="notification in filteredNotifications"
                    :key="notification.id"
                    class="notification-item"
                    :class="{ unread: !notification.read_at }"
                    @click="handleNotificationClick(notification)"
                >
                    <div class="notification-icon">
                        <component
                            :is="getNotificationIcon(notification.data?.type)"
                            size="20"
                            :class="`icon-${notification.data?.type}`"
                        />
                    </div>
                    <div class="notification-content">
                        <div class="notification-message">
                            {{ notification.data?.message }}
                        </div>
                        <div class="notification-time">
                            {{ formatDate(notification.created_at) }}
                        </div>
                    </div>
                    <div class="notification-actions">
                        <button
                            @click.stop="deleteNotification(notification.id)"
                            class="delete-btn"
                            title="Удалить уведомление"
                        >
                            <XCircle size="16" />
                        </button>
                    </div>
                </div>
            </template>

            <div v-else class="empty-notifications">
                <AlertCircle size="32" class="empty-icon" />
                <p>Нет уведомлений</p>
            </div>
        </div>

        <!-- Футер -->
        <div class="notification-footer">
            <a href="/notifications" class="view-all"
                >Просмотреть все уведомления</a
            >
        </div>
    </div>
</template>

<style lang="scss">
.notification-center {
    position: absolute;
    top: calc(var(--header-height) - 10px);
    right: 0;
    width: 360px;
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    z-index: 100;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - var(--header-height) - 20px);
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background-color: var(--bg-primary);
    position: sticky;
    top: 0;
    z-index: 10;

    h3 {
        margin: 0;
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-primary);
    }
}

.close-button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 4px;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        color: var(--text-primary);
        background-color: var(--bg-secondary);
    }
}

.notification-filters {
    display: flex;
    padding: 8px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background-color: var(--bg-primary);
    position: sticky;
    top: 54px;
    z-index: 5;
    align-items: center;
}

.filter-button {
    background: none;
    border: none;
    padding: 4px 8px;
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: var(--radius-sm);
    margin-right: 8px;

    &:hover {
        background-color: var(--bg-secondary);
        color: var(--text-primary);
    }

    &.active {
        font-weight: 600;
        color: var(--primary-color);
    }
}

.mark-all-read {
    margin-left: auto;
    background: none;
    border: none;
    padding: 4px 8px;
    font-size: 0.75rem;
    color: var(--primary-color);
    cursor: pointer;
    border-radius: var(--radius-sm);

    &:hover {
        text-decoration: underline;
    }
}

.notifications-list {
    overflow-y: auto;
    flex-grow: 1;
}

.notification-item {
    display: flex;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    cursor: pointer;
    transition: background-color var(--transition-fast);

    &:hover {
        background-color: var(--bg-secondary);
    }

    &.unread {
        background-color: rgba(63, 81, 181, 0.05);

        .notification-title {
            font-weight: 600;
        }

        &::after {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 70%;
            background-color: var(--primary-color);
            border-radius: 0 4px 4px 0;
        }
    }
}

.notification-icon {
    margin-right: 12px;
    display: flex;
    align-items: flex-start;
}

.icon-application {
    color: var(--primary-color);
}

.icon-release {
    color: var(--success-color);
}

.icon-analytics {
    color: var(--info-color);
}

.icon-system {
    color: var(--warning-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.notification-message {
    font-size: 0.8125rem;
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: 8px;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--text-disabled);
}

.empty-notifications {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;
    color: var(--text-disabled);
    text-align: center;
}

.empty-icon {
    margin-bottom: 12px;
    opacity: 0.5;
}

.notification-footer {
    padding: 12px 16px;
    text-align: center;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.view-all {
    font-size: 0.875rem;
    color: var(--primary-color);
    text-decoration: none;

    &:hover {
        text-decoration: underline;
    }
}

/* Состояние загрузки */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: var(--text-muted);

    p {
        margin-top: 0.5rem;
        font-size: 0.875rem;
    }
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Действия уведомления */
.notification-actions {
    display: flex;
    align-items: center;
    margin-left: auto;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}

.delete-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
        color: var(--danger-color);
        background-color: rgba(239, 68, 68, 0.1);
    }
}

/* Адаптивность */
@media (max-width: 576px) {
    .notification-center {
        width: calc(100vw - 32px);
        max-height: calc(100vh - var(--header-height) - 40px);
        right: 16px;
    }

    .notification-actions {
        opacity: 1; /* Всегда показываем на мобильных */
    }
}
</style>
