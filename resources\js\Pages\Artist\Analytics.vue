<script setup>
import { ref, computed } from "vue";
import { router } from "@inertiajs/vue3";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import {
    Music,
    FileText,
    BarChart3,
    TrendingUp,
    Calendar,
    Clock,
    CheckCircle,
    XCircle,
    AlertCircle,
    Eye,
    Play,
    Download,
} from "lucide-vue-next";

const props = defineProps({
    totalStats: {
        type: Object,
        required: true,
    },
    periodStats: {
        type: Object,
        required: true,
    },
    chartsData: {
        type: Object,
        required: true,
    },
    recentReleases: {
        type: Array,
        required: true,
    },
    recentApplications: {
        type: Array,
        required: true,
    },
    period: {
        type: String,
        default: "30",
    },
    artist: {
        type: Object,
        required: true,
    },
});

// Состояние компонента
const selectedPeriod = ref(props.period);

// Опции периодов
const periodOptions = [
    { value: "7", label: "7 дней" },
    { value: "30", label: "30 дней" },
    { value: "90", label: "90 дней" },
    { value: "365", label: "1 год" },
];

// Вычисляемые свойства
const statsCards = computed(() => [
    {
        title: "Мои релизы",
        total: props.totalStats.releases.total,
        new: props.periodStats.releases.new,
        icon: Music,
        color: "blue",
        details: [
            {
                label: "Опубликованы",
                value: props.totalStats.releases.published,
            },
            {
                label: "На рассмотрении",
                value: props.totalStats.releases.pending,
            },
            { label: "Черновики", value: props.totalStats.releases.draft },
        ],
    },
    {
        title: "Мои треки",
        total: props.totalStats.tracks.total,
        new: props.periodStats.tracks.new,
        icon: Play,
        color: "green",
        details: [],
    },
    {
        title: "Мои заявки",
        total: props.totalStats.applications.total,
        new: props.periodStats.applications.new,
        icon: FileText,
        color: "orange",
        details: [
            {
                label: "На рассмотрении",
                value: props.totalStats.applications.pending,
            },
            {
                label: "Одобрены",
                value: props.totalStats.applications.approved,
            },
            {
                label: "Отклонены",
                value: props.totalStats.applications.rejected,
            },
        ],
    },
]);

// Методы
function changePeriod() {
    router.get(
        "/artist/analytics",
        { period: selectedPeriod.value },
        {
            preserveState: true,
            replace: true,
        }
    );
}

function getStatusIcon(status) {
    switch (status) {
        case "published":
        case "approved":
            return CheckCircle;
        case "pending":
            return Clock;
        case "rejected":
            return XCircle;
        case "draft":
            return FileText;
        default:
            return AlertCircle;
    }
}

function getStatusColor(status) {
    switch (status) {
        case "published":
        case "approved":
            return "text-green-600";
        case "pending":
            return "text-yellow-600";
        case "rejected":
            return "text-red-600";
        case "draft":
            return "text-gray-600";
        default:
            return "text-gray-600";
    }
}

function formatDate(date) {
    return new Date(date).toLocaleDateString("ru-RU", {
        year: "numeric",
        month: "short",
        day: "numeric",
    });
}

function getTypeLabel(type) {
    const types = {
        single: "Сингл",
        ep: "EP",
        album: "Альбом",
        promo: "Промо-кампания",
        collaboration: "Сотрудничество",
        release: "Релиз",
        custom: "Другое",
    };
    return types[type] || type;
}

// Подготовка данных для графика
const chartData = computed(() => {
    return {
        labels: props.chartsData.dates,
        datasets: [
            {
                label: "Релизы",
                data: props.chartsData.releases,
                borderColor: "rgb(34, 197, 94)",
                backgroundColor: "rgba(34, 197, 94, 0.1)",
                tension: 0.4,
            },
            {
                label: "Треки",
                data: props.chartsData.tracks,
                borderColor: "rgb(59, 130, 246)",
                backgroundColor: "rgba(59, 130, 246, 0.1)",
                tension: 0.4,
            },
            {
                label: "Заявки",
                data: props.chartsData.applications,
                borderColor: "rgb(249, 115, 22)",
                backgroundColor: "rgba(249, 115, 22, 0.1)",
                tension: 0.4,
            },
        ],
    };
});
</script>

<template>
    <DashboardLayout title="Моя аналитика">
        <template #header>
            <div class="page-header">
                <div class="header-content">
                    <h1 class="page-title">Моя аналитика</h1>
                    <p class="page-subtitle">
                        Статистика ваших релизов, треков и заявок
                    </p>
                </div>

                <div class="header-actions">
                    <select
                        v-model="selectedPeriod"
                        class="period-select"
                        @change="changePeriod"
                    >
                        <option
                            v-for="option in periodOptions"
                            :key="option.value"
                            :value="option.value"
                        >
                            {{ option.label }}
                        </option>
                    </select>
                </div>
            </div>
        </template>

        <div class="artist-analytics">
            <!-- Приветствие -->
            <div class="welcome-section">
                <div class="welcome-content">
                    <h2 class="welcome-title">
                        Добро пожаловать, {{ artist.stage_name }}!
                    </h2>
                    <p class="welcome-text">
                        Здесь вы можете отслеживать прогресс ваших релизов и
                        заявок
                    </p>
                </div>
            </div>

            <!-- Статистические карточки -->
            <div class="stats-grid">
                <div
                    v-for="card in statsCards"
                    :key="card.title"
                    class="stats-card"
                    :class="`stats-card--${card.color}`"
                >
                    <div class="stats-header">
                        <div class="stats-icon">
                            <component :is="card.icon" size="24" />
                        </div>
                        <div class="stats-info">
                            <h3 class="stats-title">{{ card.title }}</h3>
                            <div class="stats-numbers">
                                <span class="stats-total">{{
                                    card.total
                                }}</span>
                                <span v-if="card.new > 0" class="stats-new">
                                    +{{ card.new }} за период
                                </span>
                            </div>
                        </div>
                    </div>

                    <div v-if="card.details.length > 0" class="stats-details">
                        <div
                            v-for="detail in card.details"
                            :key="detail.label"
                            class="stats-detail"
                        >
                            <span class="detail-label">{{ detail.label }}</span>
                            <span class="detail-value">{{ detail.value }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- График активности -->
            <div class="chart-section">
                <div class="section-header">
                    <h2 class="section-title">
                        <BarChart3 class="section-icon" size="20" />
                        Моя активность за период
                    </h2>
                </div>

                <div class="chart-container">
                    <canvas
                        id="artistActivityChart"
                        class="chart-canvas"
                    ></canvas>
                </div>
            </div>

            <div class="content-grid">
                <!-- Последние релизы -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <Music class="section-icon" size="20" />
                            Последние релизы
                        </h2>
                        <router-link to="/releases" class="section-link">
                            Все релизы
                        </router-link>
                    </div>

                    <div class="items-list">
                        <div
                            v-for="release in recentReleases"
                            :key="release.id"
                            class="item-card"
                        >
                            <div class="item-info">
                                <h4 class="item-title">{{ release.title }}</h4>
                                <div class="item-meta">
                                    <span class="meta-item">
                                        {{ getTypeLabel(release.type) }}
                                    </span>
                                    <span class="meta-item">
                                        {{ release.tracks_count }} треков
                                    </span>
                                    <span class="meta-item">
                                        {{ formatDate(release.created_at) }}
                                    </span>
                                </div>
                            </div>

                            <div
                                class="item-status"
                                :class="getStatusColor(release.status)"
                            >
                                <component
                                    :is="getStatusIcon(release.status)"
                                    size="16"
                                />
                                <span>{{ release.status }}</span>
                            </div>
                        </div>

                        <div
                            v-if="recentReleases.length === 0"
                            class="empty-state"
                        >
                            <Music size="32" />
                            <p>У вас пока нет релизов</p>
                            <router-link
                                to="/releases/create"
                                class="btn btn-primary btn-sm"
                            >
                                Создать релиз
                            </router-link>
                        </div>
                    </div>
                </div>

                <!-- Последние заявки -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <FileText class="section-icon" size="20" />
                            Последние заявки
                        </h2>
                        <router-link to="/applications" class="section-link">
                            Все заявки
                        </router-link>
                    </div>

                    <div class="items-list">
                        <div
                            v-for="application in recentApplications"
                            :key="application.id"
                            class="item-card"
                        >
                            <div class="item-info">
                                <h4 class="item-title">
                                    {{ application.title }}
                                </h4>
                                <div class="item-meta">
                                    <span class="meta-item">
                                        {{ getTypeLabel(application.type) }}
                                    </span>
                                    <span class="meta-item">
                                        {{ formatDate(application.created_at) }}
                                    </span>
                                </div>
                            </div>

                            <div
                                class="item-status"
                                :class="getStatusColor(application.status)"
                            >
                                <component
                                    :is="getStatusIcon(application.status)"
                                    size="16"
                                />
                                <span>{{ application.status }}</span>
                            </div>
                        </div>

                        <div
                            v-if="recentApplications.length === 0"
                            class="empty-state"
                        >
                            <FileText size="32" />
                            <p>У вас пока нет заявок</p>
                            <router-link
                                to="/applications/create"
                                class="btn btn-primary btn-sm"
                            >
                                Создать заявку
                            </router-link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </DashboardLayout>
</template>

<style lang="scss" scoped>
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    margin-bottom: 32px;

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}

.header-content {
    flex: 1;
}

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px;
}

.page-subtitle {
    color: var(--text-secondary);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 16px;
}

.period-select {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }
}

.artist-analytics {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.welcome-section {
    background: linear-gradient(
        135deg,
        var(--primary-color),
        var(--primary-hover)
    );
    border-radius: var(--radius-lg);
    padding: 32px;
    color: white;
    text-align: center;
}

.welcome-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 8px;
}

.welcome-text {
    opacity: 0.9;
    margin: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.stats-card {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
    transition: all 0.2s ease;

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    &--blue {
        border-left: 4px solid #3b82f6;
    }

    &--green {
        border-left: 4px solid #22c55e;
    }

    &--orange {
        border-left: 4px solid #f97316;
    }
}

.stats-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stats-card--blue .stats-icon {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.stats-card--green .stats-icon {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.stats-card--orange .stats-icon {
    background: rgba(249, 115, 22, 0.1);
    color: #f97316;
}

.stats-info {
    flex: 1;
}

.stats-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin: 0 0 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-numbers {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stats-total {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.stats-new {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.stats-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stats-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: var(--radius-sm);
}

.detail-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.detail-value {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-section {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-icon {
    color: var(--primary-color);
}

.section-link {
    font-size: 0.875rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;

    &:hover {
        text-decoration: underline;
    }
}

.chart-container {
    height: 300px;
    position: relative;
}

.chart-canvas {
    width: 100% !important;
    height: 100% !important;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;

    @media (max-width: 1024px) {
        grid-template-columns: 1fr;
    }
}

.content-section {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
}

.items-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.item-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    padding: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.02);
        border-color: rgba(0, 0, 0, 0.1);
    }
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px;
    line-height: 1.4;
}

.item-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
}

.meta-item {
    font-size: 0.625rem;
    color: var(--text-secondary);
    padding: 2px 6px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: var(--radius-sm);
}

.item-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);

    svg {
        margin-bottom: 12px;
        opacity: 0.5;
    }

    p {
        margin: 0 0 16px;
        font-size: 0.875rem;
    }
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    text-decoration: none;
    font-size: 0.75rem;

    &.btn-primary {
        background: var(--primary-color);
        color: white;

        &:hover {
            background: var(--primary-hover);
        }
    }

    &.btn-sm {
        padding: 6px 12px;
        font-size: 0.75rem;
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .item-card {
        flex-direction: column;
        align-items: flex-start;
    }

    .item-status {
        align-self: flex-end;
    }
}
</style>
