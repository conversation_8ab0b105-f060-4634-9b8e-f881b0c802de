<script setup>
import { ref, computed, onMounted } from "vue";
import { router } from "@inertiajs/vue3";
import {
    TrendingUp,
    TrendingDown,
    Users,
    Music,
    FileText,
    Calendar,
    Activity,
    BarChart3,
    <PERSON><PERSON>hart,
    Clock,
    CheckCircle,
    XCircle,
    AlertCircle,
} from "lucide-vue-next";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";

const props = defineProps({
    totalStats: {
        type: Object,
        required: true,
    },
    periodStats: {
        type: Object,
        required: true,
    },
    chartsData: {
        type: Object,
        required: true,
    },
    topArtists: {
        type: Array,
        required: true,
    },
    recentActivities: {
        type: Array,
        required: true,
    },
    period: {
        type: String,
        default: "30",
    },
});

// Состояние компонента
const selectedPeriod = ref(props.period);

// Опции периодов
const periodOptions = [
    { value: "7", label: "7 дней" },
    { value: "30", label: "30 дней" },
    { value: "90", label: "90 дней" },
    { value: "365", label: "1 год" },
];

// Вычисляемые свойства
const statsCards = computed(() => [
    {
        title: "Пользователи",
        total: props.totalStats.users.total,
        new: props.periodStats.users.new,
        icon: Users,
        color: "blue",
        details: [
            { label: "Исполнители", value: props.totalStats.users.artists },
            { label: "Администраторы", value: props.totalStats.users.admins },
            { label: "Менеджеры", value: props.totalStats.users.managers },
        ],
    },
    {
        title: "Релизы",
        total: props.totalStats.releases.total,
        new: props.periodStats.releases.new,
        icon: Music,
        color: "green",
        details: [
            {
                label: "Опубликованы",
                value: props.totalStats.releases.published,
            },
            {
                label: "На рассмотрении",
                value: props.totalStats.releases.pending,
            },
            { label: "Черновики", value: props.totalStats.releases.draft },
        ],
    },
    {
        title: "Треки",
        total: props.totalStats.tracks.total,
        new: props.periodStats.tracks.new,
        icon: Music,
        color: "purple",
        details: [],
    },
    {
        title: "Заявки",
        total: props.totalStats.applications.total,
        new: props.periodStats.applications.new,
        icon: FileText,
        color: "orange",
        details: [
            {
                label: "На рассмотрении",
                value: props.totalStats.applications.pending,
            },
            {
                label: "Одобрены",
                value: props.totalStats.applications.approved,
            },
            {
                label: "Отклонены",
                value: props.totalStats.applications.rejected,
            },
        ],
    },
]);

// Методы
function changePeriod() {
    router.get(
        route("analytics.index"),
        { period: selectedPeriod.value },
        {
            preserveState: true,
            replace: true,
        }
    );
}

function getActivityIcon(type) {
    switch (type) {
        case "release":
            return Music;
        case "application":
            return FileText;
        case "user":
            return Users;
        default:
            return Activity;
    }
}

function getActivityColor(type) {
    switch (type) {
        case "release":
            return "text-green-600";
        case "application":
            return "text-orange-600";
        case "user":
            return "text-blue-600";
        default:
            return "text-gray-600";
    }
}

function getStatusIcon(status) {
    switch (status) {
        case "published":
        case "approved":
        case "active":
            return CheckCircle;
        case "pending":
            return Clock;
        case "rejected":
            return XCircle;
        default:
            return AlertCircle;
    }
}

function getStatusColor(status) {
    switch (status) {
        case "published":
        case "approved":
        case "active":
            return "text-green-600";
        case "pending":
            return "text-yellow-600";
        case "rejected":
            return "text-red-600";
        default:
            return "text-gray-600";
    }
}

function formatDate(date) {
    return new Date(date).toLocaleDateString("ru-RU", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
    });
}

// Подготовка данных для графика
const chartData = computed(() => {
    return {
        labels: props.chartsData.dates,
        datasets: [
            {
                label: "Пользователи",
                data: props.chartsData.users,
                borderColor: "rgb(59, 130, 246)",
                backgroundColor: "rgba(59, 130, 246, 0.1)",
                tension: 0.4,
            },
            {
                label: "Релизы",
                data: props.chartsData.releases,
                borderColor: "rgb(34, 197, 94)",
                backgroundColor: "rgba(34, 197, 94, 0.1)",
                tension: 0.4,
            },
            {
                label: "Заявки",
                data: props.chartsData.applications,
                borderColor: "rgb(249, 115, 22)",
                backgroundColor: "rgba(249, 115, 22, 0.1)",
                tension: 0.4,
            },
        ],
    };
});
</script>

<template>
    <DashboardLayout title="Аналитика">
        <template #header>
            <div class="page-header">
                <div class="header-content">
                    <h1 class="page-title">Аналитика</h1>
                    <p class="page-subtitle">
                        Статистика и аналитика платформы
                    </p>
                </div>

                <div class="header-actions">
                    <select
                        v-model="selectedPeriod"
                        class="period-select"
                        @change="changePeriod"
                    >
                        <option
                            v-for="option in periodOptions"
                            :key="option.value"
                            :value="option.value"
                        >
                            {{ option.label }}
                        </option>
                    </select>
                </div>
            </div>
        </template>

        <div class="analytics-page">
            <!-- Статистические карточки -->
            <div class="stats-grid">
                <div
                    v-for="card in statsCards"
                    :key="card.title"
                    class="stats-card"
                    :class="`stats-card--${card.color}`"
                >
                    <div class="stats-header">
                        <div class="stats-icon">
                            <component :is="card.icon" size="24" />
                        </div>
                        <div class="stats-info">
                            <h3 class="stats-title">{{ card.title }}</h3>
                            <div class="stats-numbers">
                                <span class="stats-total">{{
                                    card.total
                                }}</span>
                                <span v-if="card.new > 0" class="stats-new">
                                    +{{ card.new }} за период
                                </span>
                            </div>
                        </div>
                    </div>

                    <div v-if="card.details.length > 0" class="stats-details">
                        <div
                            v-for="detail in card.details"
                            :key="detail.label"
                            class="stats-detail"
                        >
                            <span class="detail-label">{{ detail.label }}</span>
                            <span class="detail-value">{{ detail.value }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- График активности -->
            <div class="chart-section">
                <div class="section-header">
                    <h2 class="section-title">
                        <BarChart3 class="section-icon" size="20" />
                        Активность за период
                    </h2>
                </div>

                <div class="chart-container">
                    <canvas id="activityChart" class="chart-canvas"></canvas>
                </div>
            </div>

            <div class="content-grid">
                <!-- Топ исполнители -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <TrendingUp class="section-icon" size="20" />
                            Топ исполнители
                        </h2>
                    </div>

                    <div class="artists-list">
                        <div
                            v-for="(artist, index) in topArtists"
                            :key="artist.id"
                            class="artist-item"
                        >
                            <div class="artist-rank">{{ index + 1 }}</div>
                            <div class="artist-info">
                                <div class="artist-name">{{ artist.name }}</div>
                                <div class="artist-stats">
                                    {{ artist.releases_count }} релизов •
                                    {{ artist.tracks_count }} треков
                                </div>
                            </div>
                        </div>

                        <div v-if="topArtists.length === 0" class="empty-state">
                            <p>Нет данных за выбранный период</p>
                        </div>
                    </div>
                </div>

                <!-- Последние активности -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <Activity class="section-icon" size="20" />
                            Последние активности
                        </h2>
                    </div>

                    <div class="activities-list">
                        <div
                            v-for="activity in recentActivities"
                            :key="`${activity.type}-${activity.date}`"
                            class="activity-item"
                        >
                            <div
                                class="activity-icon"
                                :class="getActivityColor(activity.type)"
                            >
                                <component
                                    :is="getActivityIcon(activity.type)"
                                    size="16"
                                />
                            </div>

                            <div class="activity-content">
                                <div class="activity-title">
                                    {{ activity.title }}
                                </div>
                                <div class="activity-description">
                                    {{ activity.description }}
                                </div>
                                <div class="activity-meta">
                                    <span class="activity-date">{{
                                        formatDate(activity.date)
                                    }}</span>
                                    <div
                                        class="activity-status"
                                        :class="getStatusColor(activity.status)"
                                    >
                                        <component
                                            :is="getStatusIcon(activity.status)"
                                            size="12"
                                        />
                                        <span>{{ activity.status }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div
                            v-if="recentActivities.length === 0"
                            class="empty-state"
                        >
                            <p>Нет активности</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </DashboardLayout>
</template>

<style lang="scss" scoped>
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    margin-bottom: 32px;

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}

.header-content {
    flex: 1;
}

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px;
}

.page-subtitle {
    color: var(--text-secondary);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 16px;
}

.period-select {
    padding: 8px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    font-size: 0.875rem;

    &:focus {
        outline: none;
        border-color: var(--primary-color);
    }
}

.analytics-page {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
}

.stats-card {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
    transition: all 0.2s ease;

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &--blue {
        border-left: 4px solid #3b82f6;
    }

    &--green {
        border-left: 4px solid #22c55e;
    }

    &--purple {
        border-left: 4px solid #a855f7;
    }

    &--orange {
        border-left: 4px solid #f97316;
    }
}

.stats-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
    flex-shrink: 0;
}

.stats-card--blue .stats-icon {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.stats-card--green .stats-icon {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.stats-card--purple .stats-icon {
    background: rgba(168, 85, 247, 0.1);
    color: #a855f7;
}

.stats-card--orange .stats-icon {
    background: rgba(249, 115, 22, 0.1);
    color: #f97316;
}

.stats-info {
    flex: 1;
}

.stats-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin: 0 0 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-numbers {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stats-total {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.stats-new {
    font-size: 0.75rem;
    color: var(--success-color);
    font-weight: 500;
}

.stats-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.stats-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.detail-value {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.chart-section {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-icon {
    color: var(--primary-color);
}

.chart-container {
    height: 300px;
    position: relative;
}

.chart-canvas {
    width: 100% !important;
    height: 100% !important;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;

    @media (max-width: 1024px) {
        grid-template-columns: 1fr;
    }
}

.content-section {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
}

.artists-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.artist-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px;
    border-radius: var(--radius-md);
    transition: background-color 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.02);
    }
}

.artist-rank {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.artist-info {
    flex: 1;
}

.artist-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.artist-stats {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.activities-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.activity-item {
    display: flex;
    gap: 12px;
    padding: 12px;
    border-radius: var(--radius-md);
    transition: background-color 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.02);
    }
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
    line-height: 1.4;
}

.activity-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 8px;
    line-height: 1.4;
}

.activity-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

.activity-date {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.activity-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stats-header {
        gap: 12px;
    }

    .stats-icon {
        width: 40px;
        height: 40px;
    }

    .stats-total {
        font-size: 1.5rem;
    }

    .activity-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}
</style>
