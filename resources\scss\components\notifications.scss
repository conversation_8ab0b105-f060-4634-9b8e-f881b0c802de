// Контейнер для уведомлений
.notifications-container {
    position: fixed;
    z-index: 1100;
    width: 100%;
    max-width: 350px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

// Отдельное уведомление
.notification {
    background-color: var(--bg-card);
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-md);
    padding: 1rem;
    display: flex;
    align-items: flex-start;
    position: relative;
    transition: transform var(--transition-normal),
        opacity var(--transition-normal);

    &.v-enter-from,
    &.v-leave-to {
        opacity: 0;
        transform: translateX(30px);
    }

    &.v-enter-active {
        transition: transform var(--transition-normal),
            opacity var(--transition-normal);
    }

    &.v-leave-active {
        position: absolute;
        transition: all var(--transition-normal);
    }

    // Типы уведомлений
    &.notification-info {
        border-left: 4px solid var(--info-color);

        .notification-icon {
            color: var(--info-color);
        }
    }

    &.notification-success {
        border-left: 4px solid var(--success-color);

        .notification-icon {
            color: var(--success-color);
        }
    }

    &.notification-warning {
        border-left: 4px solid var(--warning-color);

        .notification-icon {
            color: var(--warning-color);
        }
    }

    &.notification-error {
        border-left: 4px solid var(--danger-color);

        .notification-icon {
            color: var(--danger-color);
        }
    }

    // Компоненты уведомления
    .notification-icon {
        margin-right: 0.75rem;
        font-size: 1.25rem;
    }

    .notification-content {
        flex: 1;

        .notification-title {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .notification-message {
            font-size: 0.8125rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }

        .notification-time {
            font-size: 0.75rem;
            color: var(--text-disabled);
        }

        .notification-actions {
            margin-top: 0.5rem;

            a {
                font-size: 0.75rem;
                font-weight: 500;
            }
        }
    }

    .notification-close {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        background: none;
        border: none;
        padding: 0.25rem;
        cursor: pointer;
        color: var(--text-secondary);

        &:hover {
            color: var(--text-primary);
        }
    }
}

// Значок уведомлений в хедере
.notifications-icon {
    position: relative;

    .notifications-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 18px;
        height: 18px;
        background-color: var(--danger-color);
        color: white;
        border-radius: 50%;
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid var(--bg-primary);
    }
}

// Центр уведомлений
.notifications-center {
    width: 350px;
    max-height: 500px;
    overflow-y: auto;
    background-color: var(--bg-card);
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-lg);

    .notifications-header {
        padding: 1rem;
        border-bottom: 1px solid var(--bg-secondary);
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
            font-size: 1rem;
            margin: 0;
        }

        .clear-all {
            font-size: 0.75rem;
            color: var(--primary-color);
            cursor: pointer;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    .notifications-list {
        padding: 0.5rem 0;

        .notification-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background-color var(--transition-fast);
            border-bottom: 1px solid var(--bg-secondary);

            &:last-child {
                border-bottom: none;
            }

            &:hover {
                background-color: var(--bg-secondary);
            }

            &.unread {
                position: relative;

                &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 4px;
                    height: 4px;
                    background-color: var(--primary-color);
                    border-radius: 50%;
                }
            }
        }
    }

    .empty-state {
        padding: 2rem;
        text-align: center;
        color: var(--text-secondary);

        .icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        p {
            font-size: 0.875rem;
        }
    }
}
