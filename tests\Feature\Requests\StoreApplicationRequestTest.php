<?php

namespace Tests\Feature\Requests;

use App\Http\Requests\StoreApplicationRequest;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class StoreApplicationRequestTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'role' => 'artist'
        ]);
        
        Storage::fake('public');
    }

    /** @test */
    public function it_validates_required_fields()
    {
        $request = new StoreApplicationRequest();
        $validator = Validator::make([], $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('type', $validator->errors()->toArray());
        $this->assertArrayHasKey('title', $validator->errors()->toArray());
        $this->assertArrayHasKey('description', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_type_field()
    {
        $request = new StoreApplicationRequest();
        
        // Валидный тип
        $validator = Validator::make([
            'type' => 'promo',
            'title' => 'Test Title',
            'description' => 'Test description that is long enough'
        ], $request->rules());
        
        $this->assertFalse($validator->fails());
        
        // Невалидный тип
        $validator = Validator::make([
            'type' => 'invalid_type',
            'title' => 'Test Title',
            'description' => 'Test description that is long enough'
        ], $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('type', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_title_length()
    {
        $request = new StoreApplicationRequest();
        
        // Слишком короткий заголовок
        $validator = Validator::make([
            'type' => 'promo',
            'title' => 'ab',
            'description' => 'Test description that is long enough'
        ], $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('title', $validator->errors()->toArray());
        
        // Слишком длинный заголовок
        $validator = Validator::make([
            'type' => 'promo',
            'title' => str_repeat('a', 256),
            'description' => 'Test description that is long enough'
        ], $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('title', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_description_length()
    {
        $request = new StoreApplicationRequest();
        
        // Слишком короткое описание
        $validator = Validator::make([
            'type' => 'promo',
            'title' => 'Valid Title',
            'description' => 'short'
        ], $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('description', $validator->errors()->toArray());
        
        // Слишком длинное описание
        $validator = Validator::make([
            'type' => 'promo',
            'title' => 'Valid Title',
            'description' => str_repeat('a', 5001)
        ], $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('description', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_attachments()
    {
        $request = new StoreApplicationRequest();
        
        // Валидные файлы
        $validFile = UploadedFile::fake()->create('document.pdf', 1024);
        
        $validator = Validator::make([
            'type' => 'promo',
            'title' => 'Valid Title',
            'description' => 'Valid description that is long enough',
            'attachments' => [$validFile]
        ], $request->rules());
        
        $this->assertFalse($validator->fails());
        
        // Слишком большой файл
        $largeFile = UploadedFile::fake()->create('large.pdf', 11000); // 11MB
        
        $validator = Validator::make([
            'type' => 'promo',
            'title' => 'Valid Title',
            'description' => 'Valid description that is long enough',
            'attachments' => [$largeFile]
        ], $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('attachments.0', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_file_types()
    {
        $request = new StoreApplicationRequest();
        
        // Недопустимый тип файла
        $invalidFile = UploadedFile::fake()->create('script.exe', 1024);
        
        $validator = Validator::make([
            'type' => 'promo',
            'title' => 'Valid Title',
            'description' => 'Valid description that is long enough',
            'attachments' => [$invalidFile]
        ], $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('attachments.0', $validator->errors()->toArray());
    }

    /** @test */
    public function it_limits_number_of_attachments()
    {
        $request = new StoreApplicationRequest();
        
        // Слишком много файлов
        $files = [];
        for ($i = 0; $i < 11; $i++) {
            $files[] = UploadedFile::fake()->create("file{$i}.pdf", 1024);
        }
        
        $validator = Validator::make([
            'type' => 'promo',
            'title' => 'Valid Title',
            'description' => 'Valid description that is long enough',
            'attachments' => $files
        ], $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('attachments', $validator->errors()->toArray());
    }

    /** @test */
    public function it_authorizes_authenticated_users()
    {
        $this->actingAs($this->user);
        
        $request = new StoreApplicationRequest();
        $this->assertTrue($request->authorize());
    }

    /** @test */
    public function it_denies_unauthenticated_users()
    {
        $request = new StoreApplicationRequest();
        $this->assertFalse($request->authorize());
    }

    /** @test */
    public function it_prepares_data_correctly()
    {
        $this->actingAs($this->user);

        $data = [
            'type' => 'promo',
            'title' => 'Test Title',
            'description' => 'Test description that is long enough'
        ];

        $request = StoreApplicationRequest::create('/test', 'POST', $data);
        $request->setContainer($this->app);
        $request->setUserResolver(function () {
            return $this->user;
        });

        // Симулируем валидацию
        $validator = \Illuminate\Support\Facades\Validator::make($data, $request->rules());
        $this->assertFalse($validator->fails());

        // Проверяем авторизацию
        $this->assertTrue($request->authorize());
    }
}
