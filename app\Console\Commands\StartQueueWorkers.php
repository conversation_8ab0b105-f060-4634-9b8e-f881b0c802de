<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Process;

class StartQueueWorkers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:start-workers {--stop : Stop all queue workers}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start or stop Redis queue workers for different queues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('stop')) {
            $this->stopWorkers();
            return;
        }

        $this->startWorkers();
    }

    /**
     * Start queue workers.
     */
    private function startWorkers(): void
    {
        $this->info('Запуск Redis queue workers...');

        $workers = [
            [
                'queue' => 'default',
                'processes' => 2,
                'timeout' => 60,
                'description' => 'Основная очередь'
            ],
            [
                'queue' => 'files',
                'processes' => 3,
                'timeout' => 300,
                'description' => 'Файловые операции'
            ],
            [
                'queue' => 'notifications',
                'processes' => 2,
                'timeout' => 60,
                'description' => 'Уведомления'
            ]
        ];

        foreach ($workers as $worker) {
            $this->startWorker($worker);
        }

        $this->info('Все queue workers запущены!');
        $this->newLine();
        $this->info('Для остановки используйте: php artisan queue:start-workers --stop');
        $this->info('Для мониторинга: php artisan queue:monitor');
    }

    /**
     * Start individual worker.
     */
    private function startWorker(array $config): void
    {
        $queue = $config['queue'];
        $processes = $config['processes'];
        $timeout = $config['timeout'];
        $description = $config['description'];

        $this->line("Запуск worker для очереди '{$queue}' ({$description})...");

        for ($i = 1; $i <= $processes; $i++) {
            $command = sprintf(
                'php artisan queue:work redis --queue=%s --timeout=%d --tries=3 --daemon',
                $queue,
                $timeout
            );

            // В Windows используем start для запуска в фоне
            if (PHP_OS_FAMILY === 'Windows') {
                $command = "start /B {$command}";
            } else {
                $command = "{$command} &";
            }

            $this->line("  Процесс {$i}/{$processes}: {$command}");
            
            // Запускаем команду
            if (PHP_OS_FAMILY === 'Windows') {
                pclose(popen($command, 'r'));
            } else {
                exec($command);
            }
        }

        $this->info("✓ Worker для очереди '{$queue}' запущен ({$processes} процессов)");
    }

    /**
     * Stop all queue workers.
     */
    private function stopWorkers(): void
    {
        $this->info('Остановка всех queue workers...');

        if (PHP_OS_FAMILY === 'Windows') {
            // Windows
            $processes = shell_exec('tasklist /FI "IMAGENAME eq php.exe" /FO CSV');
            if ($processes) {
                $lines = explode("\n", $processes);
                foreach ($lines as $line) {
                    if (strpos($line, 'queue:work') !== false) {
                        preg_match('/"(\d+)"/', $line, $matches);
                        if (isset($matches[1])) {
                            shell_exec("taskkill /PID {$matches[1]} /F");
                            $this->line("Остановлен процесс PID: {$matches[1]}");
                        }
                    }
                }
            }
        } else {
            // Linux/Mac
            exec('pkill -f "queue:work"');
        }

        $this->info('✓ Все queue workers остановлены');
    }
}
