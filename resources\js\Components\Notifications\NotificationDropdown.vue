<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import {
    Bell,
    CheckCircle,
    AlertCircle,
    Info,
    Clock,
    Trash2,
    Check,
    X,
} from "lucide-vue-next";

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    notifications: {
        type: Array,
        default: () => [],
    },
});

const emit = defineEmits(["close", "markAsRead", "markAllAsRead", "delete"]);

// Refs
const dropdownRef = ref(null);

// Computed
const unreadCount = computed(() => {
    return props.notifications.filter((n) => !n.is_read).length;
});

const hasNotifications = computed(() => {
    return props.notifications.length > 0;
});

// Methods
function markAsRead(notification) {
    emit("markAsRead", notification);
}

function markAllAsRead() {
    emit("markAllAsRead");
}

function deleteNotification(notification) {
    emit("delete", notification);
}

function getNotificationIcon(type) {
    switch (type) {
        case "application_approved":
        case "release_published":
            return CheckCircle;
        case "application_rejected":
        case "release_rejected":
            return AlertCircle;
        case "application_pending":
        case "release_pending":
            return Clock;
        default:
            return Info;
    }
}

function getNotificationColor(type) {
    switch (type) {
        case "application_approved":
        case "release_published":
            return "text-green-600";
        case "application_rejected":
        case "release_rejected":
            return "text-red-600";
        case "application_pending":
        case "release_pending":
            return "text-yellow-600";
        default:
            return "text-blue-600";
    }
}

function formatDate(date) {
    const now = new Date();
    const notificationDate = new Date(date);
    const diffInHours = Math.floor((now - notificationDate) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
        return "Только что";
    } else if (diffInHours < 24) {
        return `${diffInHours} ч. назад`;
    } else {
        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays === 1) {
            return "Вчера";
        } else if (diffInDays < 7) {
            return `${diffInDays} дн. назад`;
        } else {
            return notificationDate.toLocaleDateString("ru-RU", {
                day: "numeric",
                month: "short",
            });
        }
    }
}

// Click outside handler
function handleClickOutside(event) {
    if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
        emit("close");
    }
}

onMounted(() => {
    document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener("click", handleClickOutside);
});
</script>

<template>
    <div
        v-if="show"
        ref="dropdownRef"
        class="notification-dropdown"
        @click.stop
    >
        <!-- Header -->
        <div class="dropdown-header">
            <div class="header-content">
                <h3 class="dropdown-title">Уведомления</h3>
                <span v-if="unreadCount > 0" class="unread-badge">
                    {{ unreadCount }}
                </span>
            </div>
            
            <div class="header-actions">
                <button
                    v-if="unreadCount > 0"
                    type="button"
                    class="action-btn"
                    @click="markAllAsRead"
                    title="Отметить все как прочитанные"
                >
                    <Check size="14" />
                </button>
                
                <button
                    type="button"
                    class="action-btn close-btn"
                    @click="$emit('close')"
                >
                    <X size="14" />
                </button>
            </div>
        </div>

        <!-- Body -->
        <div class="dropdown-body">
            <div v-if="hasNotifications" class="notifications-list">
                <div
                    v-for="notification in notifications"
                    :key="notification.id"
                    class="notification-item"
                    :class="{ unread: !notification.is_read }"
                >
                    <div class="notification-icon">
                        <component 
                            :is="getNotificationIcon(notification.type)" 
                            size="16"
                            :class="getNotificationColor(notification.type)"
                        />
                    </div>
                    
                    <div class="notification-content">
                        <h4 class="notification-title">
                            {{ notification.title }}
                        </h4>
                        <p class="notification-message">
                            {{ notification.message }}
                        </p>
                        <span class="notification-time">
                            {{ formatDate(notification.created_at) }}
                        </span>
                    </div>
                    
                    <div class="notification-actions">
                        <button
                            v-if="!notification.is_read"
                            type="button"
                            class="action-btn read-btn"
                            @click="markAsRead(notification)"
                            title="Отметить как прочитанное"
                        >
                            <CheckCircle size="12" />
                        </button>
                        
                        <button
                            type="button"
                            class="action-btn delete-btn"
                            @click="deleteNotification(notification)"
                            title="Удалить"
                        >
                            <Trash2 size="12" />
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Empty state -->
            <div v-else class="empty-state">
                <Bell size="32" class="empty-icon" />
                <p class="empty-text">Нет уведомлений</p>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.notification-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    width: 380px;
    max-height: 500px;
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    overflow: hidden;
    
    @media (max-width: 480px) {
        width: 320px;
        right: -20px;
    }
}

.dropdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(0, 0, 0, 0.02);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.dropdown-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.unread-badge {
    background: var(--primary-color);
    color: white;
    font-size: 0.625rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

.header-actions {
    display: flex;
    gap: 4px;
}

.action-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    background: transparent;
    color: var(--text-secondary);

    &:hover {
        background: rgba(0, 0, 0, 0.05);
        color: var(--text-primary);
    }

    &.read-btn {
        color: #22c55e;
        
        &:hover {
            background: rgba(34, 197, 94, 0.1);
        }
    }

    &.delete-btn {
        color: #ef4444;
        
        &:hover {
            background: rgba(239, 68, 68, 0.1);
        }
    }
}

.dropdown-body {
    max-height: 400px;
    overflow-y: auto;
}

.notifications-list {
    display: flex;
    flex-direction: column;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.02);
    }

    &.unread {
        background: rgba(var(--primary-rgb), 0.02);
        border-left: 3px solid var(--primary-color);
        padding-left: 17px;
    }

    &:last-child {
        border-bottom: none;
    }
}

.notification-icon {
    flex-shrink: 0;
    margin-top: 2px;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px;
    line-height: 1.3;
}

.notification-message {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0 0 6px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.notification-time {
    font-size: 0.625rem;
    color: var(--text-tertiary);
}

.notification-actions {
    display: flex;
    gap: 2px;
    flex-shrink: 0;
    margin-top: 2px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.empty-icon {
    margin-bottom: 12px;
    opacity: 0.5;
}

.empty-text {
    margin: 0;
    font-size: 0.875rem;
}

/* Scrollbar styling */
.dropdown-body::-webkit-scrollbar {
    width: 4px;
}

.dropdown-body::-webkit-scrollbar-track {
    background: transparent;
}

.dropdown-body::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.dropdown-body::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}
</style>
