<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Laravel\Jetstream\HasProfilePhoto;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'avatar',
        'provider',
        'provider_id',
        'provider_token',
        'provider_refresh_token',
        'provider_data',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
        'provider_token',
        'provider_refresh_token',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'provider_data' => 'json',
        ];
    }

    /**
     * Get the user's artist profile.
     */
    public function artist(): HasOne
    {
        return $this->hasOne(Artist::class);
    }

    /**
     * Get user's applications.
     */
    public function applications(): HasMany
    {
        return $this->hasMany(Application::class);
    }



    /**
     * Get user's settings.
     */
    public function settings(): MorphMany
    {
        return $this->morphMany(Setting::class, 'settable');
    }

    /**
     * Determine if the user is an administrator.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Determine if the user is an artist.
     */
    public function isArtist(): bool
    {
        return $this->role === 'artist';
    }

    /**
     * Determine if the user is a manager.
     */
    public function isManager(): bool
    {
        return $this->role === 'manager';
    }

    /**
     * Get user's avatar URL.
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }

        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&color=7F9CF5&background=EBF4FF';
    }

    /**
     * Check if user is registered via OAuth provider.
     */
    public function isOAuthUser(): bool
    {
        return !empty($this->provider) && !empty($this->provider_id);
    }

    /**
     * Check if user can login with password.
     */
    public function hasPassword(): bool
    {
        return !empty($this->password);
    }

    /**
     * Get OAuth provider display name.
     */
    public function getProviderDisplayName(): ?string
    {
        return match($this->provider) {
            'google' => 'Google',
            'vkontakte' => 'ВКонтакте',
            default => $this->provider ? ucfirst($this->provider) : null,
        };
    }

    /**
     * Update OAuth tokens.
     */
    public function updateOAuthTokens(?string $token, ?string $refreshToken = null): void
    {
        $this->update([
            'provider_token' => $token,
            'provider_refresh_token' => $refreshToken,
        ]);
    }
}
