<script setup>
import { computed } from "vue";
import { Link, usePage } from "@inertiajs/vue3";
import {
    Home,
    Music,
    Disc,
    PenSquare,
    FileAudio,
    BarChart2,
    Settings,
    Users,
    ChevronRight,
    LogOut,
    Shield,
    FileText,
    BarChart3,
} from "lucide-vue-next";

const props = defineProps({
    collapsed: {
        type: Boolean,
        default: false,
    },
    mobileOpen: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(["closeMobile"]);

const user = computed(() => usePage().props.auth.user);

// Проверка, есть ли у пользователя профиль артиста
const hasArtistProfile = computed(() => {
    return user.value.artist !== null;
});

// Счетчик непрочитанных заявок
const unreadApplicationsCount = computed(() => {
    return usePage().props.unreadApplicationsCount || 0;
});

// Логаут
const logout = () => {
    window.axios.post(route("logout")).then(() => {
        window.location.href = "/";
    });
};

// Закрытие мобильного сайдбара
const closeMobileSidebar = () => {
    emit("closeMobile");
};
</script>

<template>
    <aside
        class="sidebar"
        :class="{
            'sidebar-collapsed': collapsed,
            'sidebar-mobile-open': mobileOpen,
        }"
    >
        <!-- Логотип -->
        <div class="sidebar-logo">
            <img
                src="/images/madd-logo-icon.svg"
                class="logo-icon"
                alt="Madd Label"
            />
            <img
                src="/images/madd-logo-full.svg"
                class="logo-full"
                alt="Madd Label"
            />
        </div>

        <!-- Навигация -->
        <nav class="sidebar-nav">
            <!-- Основное меню -->
            <div class="nav-group">
                <div class="group-title" v-if="!collapsed">Основное</div>

                <Link
                    :href="route('dashboard')"
                    class="nav-item hover-slide-right animate-fade-in-left animate-delay-100"
                    :class="{ active: route().current('dashboard') }"
                    @click="closeMobileSidebar"
                >
                    <Home class="item-icon" />
                    <span class="item-label">Дашборд</span>
                </Link>

                <Link
                    :href="route('releases.index')"
                    class="nav-item hover-slide-right animate-fade-in-left animate-delay-200"
                    :class="{ active: route().current('releases.*') }"
                    @click="closeMobileSidebar"
                >
                    <Music class="item-icon" />
                    <span class="item-label">Релизы</span>
                </Link>

                <Link
                    :href="route('tracks.index')"
                    class="nav-item hover-slide-right animate-fade-in-left animate-delay-300"
                    :class="{ active: route().current('tracks.*') }"
                    @click="closeMobileSidebar"
                >
                    <FileAudio class="item-icon" />
                    <span class="item-label">Треки</span>
                </Link>
            </div>

            <!-- Заявки и аналитика -->
            <div class="nav-group">
                <div class="group-title" v-if="!collapsed">Управление</div>

                <Link
                    :href="route('applications.index')"
                    class="nav-item"
                    :class="{ active: route().current('applications.*') }"
                    @click="closeMobileSidebar"
                >
                    <PenSquare class="item-icon" />
                    <span class="item-label">Заявки</span>
                    <span v-if="unreadApplicationsCount > 0" class="item-badge">
                        {{ unreadApplicationsCount }}
                    </span>
                </Link>

                <Link
                    :href="route('analytics.index')"
                    class="nav-item"
                    :class="{ active: route().current('analytics.*') }"
                    @click="closeMobileSidebar"
                >
                    <BarChart2 class="item-icon" />
                    <span class="item-label">Аналитика</span>
                </Link>
            </div>

            <!-- Меню администратора (отображается только для админов) -->
            <div
                class="nav-group"
                v-if="user.role === 'admin' || user.role === 'manager'"
            >
                <div class="group-title" v-if="!collapsed">
                    Администрирование
                </div>

                <Link
                    :href="route('admin.dashboard')"
                    class="nav-item"
                    :class="{ active: route().current('admin.dashboard') }"
                    @click="closeMobileSidebar"
                >
                    <Shield class="item-icon" />
                    <span class="item-label">Админ-панель</span>
                </Link>

                <Link
                    :href="route('admin.applications')"
                    class="nav-item"
                    :class="{ active: route().current('admin.applications') }"
                    @click="closeMobileSidebar"
                >
                    <FileText class="item-icon" />
                    <span class="item-label">Управление заявками</span>
                </Link>

                <Link
                    :href="route('admin.releases')"
                    class="nav-item"
                    :class="{ active: route().current('admin.releases') }"
                    @click="closeMobileSidebar"
                >
                    <Music class="item-icon" />
                    <span class="item-label">Управление релизами</span>
                </Link>

                <Link
                    :href="route('admin.users')"
                    class="nav-item"
                    :class="{ active: route().current('admin.users') }"
                    @click="closeMobileSidebar"
                >
                    <Users class="item-icon" />
                    <span class="item-label">Пользователи</span>
                </Link>

                <Link
                    :href="route('admin.analytics')"
                    class="nav-item"
                    :class="{ active: route().current('admin.analytics') }"
                    @click="closeMobileSidebar"
                >
                    <BarChart3 class="item-icon" />
                    <span class="item-label">Общая аналитика</span>
                </Link>
            </div>

            <Link
                :href="route('admin.settings')"
                class="nav-item"
                :class="{ active: route().current('admin.settings') }"
                @click="closeMobileSidebar"
            >
                <Settings class="item-icon" />
                <span class="item-label">Настройки</span>
            </Link>
        </nav>

        <!-- Футер сайдбара -->
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <img :src="user.profile_photo_url" :alt="user.name" />
                </div>

                <div class="user-details">
                    <div class="user-name">{{ user.name }}</div>
                    <div class="user-role">
                        {{
                            user.role === "admin"
                                ? "Администратор"
                                : user.role === "artist"
                                ? "Артист"
                                : "Менеджер"
                        }}
                    </div>
                </div>

                <button class="action-button" @click="logout" title="Выйти">
                    <LogOut size="18" />
                </button>
            </div>
        </div>
    </aside>
</template>
