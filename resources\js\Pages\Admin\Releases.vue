<script setup>
import { ref, computed, watch } from "vue";
import { router, usePage } from "@inertiajs/vue3";
import { $confirm } from "@/composables/useConfirm";
import {
    Search,
    Filter,
    Eye,
    Edit,
    Trash2,
    Clock,
    CheckCircle,
    X,
    User,
    Calendar,
    Music,
    MoreVertical,
    Play,
    Pause,
    Download,
} from "lucide-vue-next";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";

const props = defineProps({
    releases: {
        type: Object,
        required: true,
    },
    filters: {
        type: Object,
        required: true,
    },
});

// Состояние компонента
const searchQuery = ref(props.filters.search || "");
const statusFilter = ref(props.filters.status || "all");
const typeFilter = ref(props.filters.type || "all");
const showFilters = ref(false);
const selectedItems = ref([]);

// Конфигурация статусов
const statusConfig = {
    draft: { label: "Черновик", color: "secondary", icon: Edit },
    pending: { label: "На рассмотрении", color: "warning", icon: Clock },
    approved: { label: "Одобрен", color: "success", icon: CheckCircle },
    published: { label: "Опубликован", color: "success", icon: CheckCircle },
    rejected: { label: "Отклонен", color: "danger", icon: X },
};

// Опции для фильтров
const statusOptions = [
    { value: "all", label: "Все статусы" },
    { value: "draft", label: "Черновики" },
    { value: "pending", label: "На рассмотрении" },
    { value: "approved", label: "Одобренные" },
    { value: "published", label: "Опубликованные" },
    { value: "rejected", label: "Отклоненные" },
];

const typeOptions = [
    { value: "all", label: "Все типы" },
    { value: "single", label: "Сингл" },
    { value: "ep", label: "EP" },
    { value: "album", label: "Альбом" },
    { value: "compilation", label: "Сборник" },
];

// Вычисляемые свойства
const hasFilters = computed(() => {
    return (
        searchQuery.value ||
        statusFilter.value !== "all" ||
        typeFilter.value !== "all"
    );
});

const showBulkActions = computed(() => {
    return selectedItems.value.length > 0;
});

const allSelected = computed(() => {
    return (
        props.releases.data.length > 0 &&
        selectedItems.value.length === props.releases.data.length
    );
});

// Методы
function toggleFilters() {
    showFilters.value = !showFilters.value;
}

function applyFilters() {
    const params = {
        search: searchQuery.value || undefined,
        status: statusFilter.value !== "all" ? statusFilter.value : undefined,
        type: typeFilter.value !== "all" ? typeFilter.value : undefined,
    };

    router.get("/admin/releases", params, {
        preserveState: true,
        replace: true,
    });
}

function clearFilters() {
    searchQuery.value = "";
    statusFilter.value = "all";
    typeFilter.value = "all";
    applyFilters();
}

// Обновление статуса релиза
async function updateReleaseStatus(release, newStatus) {
    if (release.status === newStatus) return;

    const statusLabels = {
        draft: "в черновики",
        pending: "на рассмотрение",
        approved: "одобрить",
        published: "опубликовать",
        rejected: "отклонить",
    };

    const confirmed = await $confirm.show(
        `${
            statusLabels[newStatus].charAt(0).toUpperCase() +
            statusLabels[newStatus].slice(1)
        } релиз "${release.title}"?`,
        "Подтверждение действия"
    );

    if (confirmed) {
        router.post(
            `/admin/releases/${release.id}/status`,
            {
                status: newStatus,
            },
            {
                preserveScroll: true,
            }
        );
    }
}

async function deleteRelease(release) {
    const confirmed = await $confirm.danger(
        `Релиз "${release.title}" будет удален безвозвратно.`,
        "Удалить релиз?"
    );

    if (confirmed) {
        router.delete(`/admin/releases/${release.id}`, {
            preserveScroll: true,
        });
    }
}

function toggleSelectAll() {
    if (allSelected.value) {
        selectedItems.value = [];
    } else {
        selectedItems.value = props.releases.data.map((release) => release.id);
    }
}

function toggleSelectItem(releaseId) {
    const index = selectedItems.value.indexOf(releaseId);
    if (index > -1) {
        selectedItems.value.splice(index, 1);
    } else {
        selectedItems.value.push(releaseId);
    }
}

async function bulkAction(action) {
    if (selectedItems.value.length === 0) return;

    const actionLabels = {
        approve: "одобрить",
        publish: "опубликовать",
        reject: "отклонить",
        delete: "удалить",
    };

    const confirmed = await $confirm.show(
        `${
            actionLabels[action].charAt(0).toUpperCase() +
            actionLabels[action].slice(1)
        } ${selectedItems.value.length} релизов?`,
        "Массовое действие"
    );

    if (confirmed) {
        router.post(
            "/admin/releases/bulk",
            {
                action,
                ids: selectedItems.value,
            },
            {
                preserveScroll: true,
                onSuccess: () => {
                    selectedItems.value = [];
                },
            }
        );
    }
}

function formatDate(date) {
    return new Date(date).toLocaleDateString("ru-RU", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
    });
}

function formatDuration(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
}

// Watchers
watch(
    [searchQuery, statusFilter, typeFilter],
    () => {
        applyFilters();
    },
    { debounce: 300 }
);
</script>

<template>
    <DashboardLayout title="Релизы">
        <template #header>
            <div class="page-header">
                <div class="header-content">
                    <h1 class="page-title">Релизы</h1>
                    <p class="page-subtitle">Управление релизами артистов</p>
                </div>
            </div>
        </template>

        <div class="admin-releases">
            <!-- Фильтры и поиск -->
            <div class="filters-section">
                <div class="search-bar">
                    <div class="search-input-wrapper">
                        <Search class="search-icon" size="20" />
                        <input
                            v-model="searchQuery"
                            type="text"
                            class="search-input"
                            placeholder="Поиск по названию или артисту..."
                        />
                    </div>

                    <button
                        type="button"
                        class="filter-toggle"
                        :class="{ active: showFilters }"
                        @click="toggleFilters"
                    >
                        <Filter size="16" />
                        <span>Фильтры</span>
                    </button>
                </div>

                <Transition name="filters">
                    <div v-if="showFilters" class="filters-panel">
                        <div class="filter-group">
                            <label class="filter-label">Статус</label>
                            <select
                                v-model="statusFilter"
                                class="filter-select"
                            >
                                <option
                                    v-for="option in statusOptions"
                                    :key="option.value"
                                    :value="option.value"
                                >
                                    {{ option.label }}
                                </option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">Тип</label>
                            <select v-model="typeFilter" class="filter-select">
                                <option
                                    v-for="option in typeOptions"
                                    :key="option.value"
                                    :value="option.value"
                                >
                                    {{ option.label }}
                                </option>
                            </select>
                        </div>

                        <div class="filter-actions">
                            <button
                                v-if="hasFilters"
                                type="button"
                                class="btn btn-outline btn-sm"
                                @click="clearFilters"
                            >
                                Сбросить
                            </button>
                        </div>
                    </div>
                </Transition>
            </div>

            <!-- Массовые действия -->
            <Transition name="bulk-actions">
                <div v-if="showBulkActions" class="bulk-actions-bar">
                    <div class="bulk-info">
                        <span>Выбрано: {{ selectedItems.length }}</span>
                    </div>

                    <div class="bulk-actions">
                        <button
                            type="button"
                            class="btn btn-success btn-sm"
                            @click="bulkAction('approve')"
                        >
                            Одобрить
                        </button>

                        <button
                            type="button"
                            class="btn btn-primary btn-sm"
                            @click="bulkAction('publish')"
                        >
                            Опубликовать
                        </button>

                        <button
                            type="button"
                            class="btn btn-danger btn-sm"
                            @click="bulkAction('reject')"
                        >
                            Отклонить
                        </button>

                        <button
                            type="button"
                            class="btn btn-danger btn-sm"
                            @click="bulkAction('delete')"
                        >
                            Удалить
                        </button>
                    </div>
                </div>
            </Transition>

            <!-- Таблица релизов -->
            <div class="releases-table">
                <div class="table-header">
                    <div class="table-cell checkbox-cell">
                        <input
                            type="checkbox"
                            class="checkbox"
                            :checked="allSelected"
                            @change="toggleSelectAll"
                        />
                    </div>
                    <div class="table-cell">Релиз</div>
                    <div class="table-cell">Артист</div>
                    <div class="table-cell">Тип</div>
                    <div class="table-cell">Статус</div>
                    <div class="table-cell">Дата создания</div>
                    <div class="table-cell">Действия</div>
                </div>

                <div class="table-body">
                    <div
                        v-for="release in releases.data"
                        :key="release.id"
                        class="table-row"
                        :class="{
                            selected: selectedItems.includes(release.id),
                        }"
                    >
                        <div class="table-cell checkbox-cell">
                            <input
                                type="checkbox"
                                class="checkbox"
                                :checked="selectedItems.includes(release.id)"
                                @change="toggleSelectItem(release.id)"
                            />
                        </div>

                        <div class="table-cell">
                            <div class="release-info">
                                <h3 class="release-title">
                                    {{ release.title }}
                                </h3>
                                <p class="release-description">
                                    {{ release.description || "Без описания" }}
                                </p>
                                <div class="release-meta">
                                    <span class="track-count"
                                        >{{
                                            release.tracks_count || 0
                                        }}
                                        треков</span
                                    >
                                    <span
                                        v-if="release.duration"
                                        class="duration"
                                    >
                                        {{ formatDuration(release.duration) }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="table-cell">
                            <div class="user-info">
                                <div class="user-name">
                                    {{ release.user?.name || "Неизвестно" }}
                                </div>
                                <div class="user-email">
                                    {{ release.user?.email || "" }}
                                </div>
                            </div>
                        </div>

                        <div class="table-cell">
                            <span class="type-badge">
                                {{ release.type || "Не указан" }}
                            </span>
                        </div>

                        <div class="table-cell">
                            <select
                                class="status-select"
                                :value="release.status"
                                @change="
                                    updateReleaseStatus(
                                        release,
                                        $event.target.value
                                    )
                                "
                                :class="`status-${release.status}`"
                            >
                                <option value="draft">Черновик</option>
                                <option value="pending">На рассмотрении</option>
                                <option value="approved">Одобрен</option>
                                <option value="published">Опубликован</option>
                                <option value="rejected">Отклонен</option>
                            </select>
                        </div>

                        <div class="table-cell">
                            <span class="date-text">{{
                                formatDate(release.created_at)
                            }}</span>
                        </div>

                        <div class="table-cell actions-cell">
                            <div class="actions-dropdown">
                                <button class="dropdown-toggle">
                                    <MoreVertical size="16" />
                                </button>

                                <div class="dropdown-menu">
                                    <button class="dropdown-item">
                                        <Eye size="14" />
                                        Просмотр
                                    </button>

                                    <button class="dropdown-item">
                                        <Edit size="14" />
                                        Редактировать
                                    </button>

                                    <button
                                        class="dropdown-item danger"
                                        @click="deleteRelease(release)"
                                    >
                                        <Trash2 size="14" />
                                        Удалить
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Пустое состояние -->
                <div v-if="releases.data.length === 0" class="empty-state">
                    <Music size="48" class="empty-icon" />
                    <h3 class="empty-title">Релизы не найдены</h3>
                    <p class="empty-text">
                        {{
                            hasFilters
                                ? "По вашему запросу ничего не найдено. Попробуйте изменить фильтры."
                                : "Пока нет релизов для отображения."
                        }}
                    </p>
                </div>
            </div>

            <!-- Пагинация -->
            <div
                v-if="releases.links && releases.links.length > 3"
                class="pagination"
            >
                <a
                    v-for="link in releases.links"
                    :key="link.label"
                    :href="link.url"
                    class="pagination-link"
                    :class="{
                        active: link.active,
                        disabled: !link.url,
                    }"
                    v-html="link.label"
                ></a>
            </div>
        </div>
    </DashboardLayout>
</template>

<style lang="scss" scoped>
// Используем те же стили что и для Applications, но с небольшими изменениями
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    margin-bottom: 32px;

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}

.header-content {
    flex: 1;
}

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px;
}

.page-subtitle {
    color: var(--text-secondary);
    margin: 0;
}

.admin-releases {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.filters-section {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
}

.search-bar {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-bottom: 16px;
}

.search-input-wrapper {
    position: relative;
    flex: 1;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-input {
    width: 100%;
    padding: 12px 12px 12px 44px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }

    &::placeholder {
        color: var(--text-secondary);
    }
}

.filter-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;

    &:hover {
        background: rgba(0, 0, 0, 0.05);
    }

    &.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }
}

.filters-panel {
    display: flex;
    gap: 16px;
    align-items: flex-end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 150px;
}

.filter-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }
}

.filter-actions {
    display: flex;
    gap: 8px;
}

.bulk-actions-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    padding: 16px 24px;
    background: rgba(var(--primary-rgb), 0.05);
    border: 1px solid rgba(var(--primary-rgb), 0.2);
    border-radius: var(--radius-lg);
}

.bulk-info {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.bulk-actions {
    display: flex;
    gap: 8px;
}

.releases-table {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 40px 1fr 200px 120px 140px 120px 120px;
    gap: 16px;
    padding: 16px 24px;
    background: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-body {
    display: flex;
    flex-direction: column;
}

.table-row {
    display: grid;
    grid-template-columns: 40px 1fr 200px 120px 140px 120px 120px;
    gap: 16px;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.02);
    }

    &.selected {
        background: rgba(var(--primary-rgb), 0.05);
        border-color: rgba(var(--primary-rgb), 0.2);
    }

    &:last-child {
        border-bottom: none;
    }
}

.table-cell {
    display: flex;
    align-items: center;
    min-height: 40px;
}

.checkbox-cell {
    justify-content: center;
}

.actions-cell {
    justify-content: flex-end;
}

.checkbox {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-sm);
    cursor: pointer;
    accent-color: var(--primary-color);
}

.release-info {
    min-width: 0;
}

.release-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px;
    line-height: 1.4;
}

.release-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0 0 6px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.release-meta {
    display: flex;
    gap: 8px;
    font-size: 0.625rem;
    color: var(--text-tertiary);
}

.track-count,
.duration {
    background: rgba(0, 0, 0, 0.05);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.user-email {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.type-badge {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    background: rgba(0, 0, 0, 0.05);
    color: var(--text-primary);
    text-transform: capitalize;
}

.status-select {
    padding: 4px 8px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
    }

    &.status-draft {
        background: rgba(156, 163, 175, 0.1);
        color: #6b7280;
        border-color: rgba(156, 163, 175, 0.3);
    }

    &.status-pending {
        background: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
        border-color: rgba(245, 158, 11, 0.3);
    }

    &.status-approved,
    &.status-published {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
        border-color: rgba(34, 197, 94, 0.3);
    }

    &.status-rejected {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        border-color: rgba(239, 68, 68, 0.3);
    }
}

.date-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.actions-dropdown {
    position: relative;
}

.dropdown-toggle {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.05);
        color: var(--text-primary);
    }
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 8px 0;
    min-width: 150px;
    z-index: 10;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
}

.actions-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.05);
    }

    &.danger {
        color: #ef4444;
    }
}

.empty-state {
    text-align: center;
    padding: 60px 24px;
    color: var(--text-secondary);
}

.empty-icon {
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px;
}

.empty-text {
    margin: 0;
    line-height: 1.5;
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 24px;
}

.pagination-link {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s ease;

    &:hover:not(.disabled) {
        background: rgba(0, 0, 0, 0.05);
    }

    &.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    text-decoration: none;
    font-size: 0.875rem;

    &.btn-sm {
        padding: 6px 12px;
        font-size: 0.75rem;
    }

    &.btn-outline {
        background: transparent;
        border: 1px solid rgba(0, 0, 0, 0.2);
        color: var(--text-primary);

        &:hover {
            background: rgba(0, 0, 0, 0.05);
        }
    }

    &.btn-primary {
        background: var(--primary-color);
        color: white;

        &:hover {
            background: var(--primary-hover);
        }
    }

    &.btn-success {
        background: #22c55e;
        color: white;

        &:hover {
            background: #16a34a;
        }
    }

    &.btn-danger {
        background: #ef4444;
        color: white;

        &:hover {
            background: #dc2626;
        }
    }
}

// Transitions
.filters-enter-active,
.filters-leave-active {
    transition: all 0.3s ease;
}

.filters-enter-from,
.filters-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

.bulk-actions-enter-active,
.bulk-actions-leave-active {
    transition: all 0.3s ease;
}

.bulk-actions-enter-from,
.bulk-actions-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

@media (max-width: 1024px) {
    .table-header,
    .table-row {
        grid-template-columns: 40px 1fr 120px 100px 80px;
        gap: 12px;
    }

    .table-cell:nth-child(3),
    .table-cell:nth-child(6) {
        display: none;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }

    .search-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .filters-panel {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .bulk-actions-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .bulk-actions {
        justify-content: stretch;
        flex-wrap: wrap;
    }

    .btn {
        justify-content: center;
        flex: 1;
        min-width: 120px;
    }

    .releases-table {
        border: none;
        background: transparent;
    }

    .table-header {
        display: none;
    }

    .table-body {
        gap: 12px;
    }

    .table-row {
        display: block;
        padding: 20px;
        border-radius: var(--radius-lg);
        margin-bottom: 0;
        border: 1px solid rgba(0, 0, 0, 0.1);
        background: var(--bg-primary);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        &.selected {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
        }
    }

    .table-cell {
        display: block;
        min-height: auto;
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .checkbox-cell {
        position: absolute;
        top: 16px;
        right: 16px;
        margin-bottom: 0;
    }

    .release-info {
        margin-top: 8px;
    }

    .release-title {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .status-select {
        width: 100%;
        padding: 8px 12px;
        font-size: 0.875rem;
        margin-bottom: 8px;
    }

    .actions-cell {
        display: flex;
        gap: 8px;
        justify-content: stretch;
    }

    .actions-dropdown {
        flex: 1;
    }

    .dropdown-toggle {
        width: 100%;
        height: 40px;
        justify-content: center;
        background: rgba(0, 0, 0, 0.05);
        border-radius: var(--radius-md);
    }
}
</style>
