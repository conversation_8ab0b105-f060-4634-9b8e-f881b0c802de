<script setup>
import { ref, computed, watch } from "vue";
import { router } from "@inertiajs/vue3";
import {
    X,
    Bell,
    CheckCircle,
    AlertCircle,
    Info,
    Clock,
    Trash2
} from "lucide-vue-next";

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    notifications: {
        type: Array,
        default: () => [],
    },
});

const emit = defineEmits(["close", "markAsRead", "markAllAsRead", "delete"]);

// Состояние компонента
const isVisible = ref(false);

// Вычисляемые свойства
const unreadCount = computed(() => {
    return props.notifications.filter((n) => !n.is_read).length;
});

const hasNotifications = computed(() => {
    return props.notifications.length > 0;
});

// Методы
function closeModal() {
    emit("close");
}

function markAsRead(notification) {
    emit("markAsRead", notification);
}

function markAllAsRead() {
    emit("markAllAsRead");
}

function deleteNotification(notification) {
    emit("delete", notification);
}

function getNotificationIcon(type) {
    switch (type) {
        case "application_approved":
        case "release_published":
            return CheckCircle;
        case "application_rejected":
        case "release_rejected":
            return AlertCircle;
        case "application_pending":
        case "release_pending":
            return Clock;
        default:
            return Info;
    }
}

function getNotificationColor(type) {
    switch (type) {
        case "application_approved":
        case "release_published":
            return "text-green-600";
        case "application_rejected":
        case "release_rejected":
            return "text-red-600";
        case "application_pending":
        case "release_pending":
            return "text-yellow-600";
        default:
            return "text-blue-600";
    }
}

function formatDate(date) {
    const now = new Date();
    const notificationDate = new Date(date);
    const diffInHours = Math.floor((now - notificationDate) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
        return "Только что";
    } else if (diffInHours < 24) {
        return `${diffInHours} ч. назад`;
    } else {
        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays === 1) {
            return "Вчера";
        } else if (diffInDays < 7) {
            return `${diffInDays} дн. назад`;
        } else {
            return notificationDate.toLocaleDateString("ru-RU", {
                day: "numeric",
                month: "short",
            });
        }
    }
}

// Watchers
watch(() => props.show, (newValue) => {
    isVisible.value = newValue;
});
</script>

<template>
    <Teleport to="body">
        <Transition name="modal-backdrop">
            <div
                v-if="show"
                class="modal-backdrop"
                @click="closeModal"
            ></div>
        </Transition>

        <Transition name="modal">
            <div v-if="show" class="modal-container">
                <div class="modal-content" @click.stop>
                    <!-- Header -->
                    <div class="modal-header">
                        <div class="header-content">
                            <div class="header-icon">
                                <Bell size="24" />
                            </div>
                            <div class="header-text">
                                <h3 class="modal-title">Уведомления</h3>
                                <p v-if="unreadCount > 0" class="unread-count">
                                    {{ unreadCount }} непрочитанных
                                </p>
                            </div>
                        </div>
                        
                        <div class="header-actions">
                            <button
                                v-if="unreadCount > 0"
                                type="button"
                                class="action-btn mark-all-btn"
                                @click="markAllAsRead"
                                title="Отметить все как прочитанные"
                            >
                                <MarkAsRead size="16" />
                            </button>
                            
                            <button
                                type="button"
                                class="action-btn close-btn"
                                @click="closeModal"
                            >
                                <X size="20" />
                            </button>
                        </div>
                    </div>

                    <!-- Body -->
                    <div class="modal-body">
                        <div v-if="hasNotifications" class="notifications-list">
                            <div
                                v-for="notification in notifications"
                                :key="notification.id"
                                class="notification-item"
                                :class="{ unread: !notification.is_read }"
                            >
                                <div class="notification-icon">
                                    <component 
                                        :is="getNotificationIcon(notification.type)" 
                                        size="20"
                                        :class="getNotificationColor(notification.type)"
                                    />
                                </div>
                                
                                <div class="notification-content">
                                    <h4 class="notification-title">
                                        {{ notification.title }}
                                    </h4>
                                    <p class="notification-message">
                                        {{ notification.message }}
                                    </p>
                                    <span class="notification-time">
                                        {{ formatDate(notification.created_at) }}
                                    </span>
                                </div>
                                
                                <div class="notification-actions">
                                    <button
                                        v-if="!notification.is_read"
                                        type="button"
                                        class="action-btn read-btn"
                                        @click="markAsRead(notification)"
                                        title="Отметить как прочитанное"
                                    >
                                        <CheckCircle size="14" />
                                    </button>
                                    
                                    <button
                                        type="button"
                                        class="action-btn delete-btn"
                                        @click="deleteNotification(notification)"
                                        title="Удалить"
                                    >
                                        <Trash2 size="14" />
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Empty state -->
                        <div v-else class="empty-state">
                            <Bell size="48" class="empty-icon" />
                            <h3 class="empty-title">Нет уведомлений</h3>
                            <p class="empty-text">
                                Здесь будут отображаться уведомления о статусе ваших заявок и релизов
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </Transition>
    </Teleport>
</template>

<style lang="scss" scoped>
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1001;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: 20px;
    padding-top: 80px;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-text {
    flex: 1;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px;
}

.unread-count {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &.mark-all-btn {
        background: rgba(var(--primary-rgb), 0.1);
        color: var(--primary-color);

        &:hover {
            background: rgba(var(--primary-rgb), 0.2);
        }
    }

    &.close-btn {
        background: transparent;
        color: var(--text-secondary);

        &:hover {
            background: rgba(0, 0, 0, 0.05);
            color: var(--text-primary);
        }
    }

    &.read-btn {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;

        &:hover {
            background: rgba(34, 197, 94, 0.2);
        }
    }

    &.delete-btn {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;

        &:hover {
            background: rgba(239, 68, 68, 0.2);
        }
    }
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.notifications-list {
    display: flex;
    flex-direction: column;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.02);
    }

    &.unread {
        background: rgba(var(--primary-rgb), 0.02);
        border-left: 3px solid var(--primary-color);
    }

    &:last-child {
        border-bottom: none;
    }
}

.notification-icon {
    flex-shrink: 0;
    margin-top: 2px;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px;
    line-height: 1.4;
}

.notification-message {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0 0 8px;
    line-height: 1.4;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

.notification-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
}

.empty-state {
    text-align: center;
    padding: 60px 24px;
    color: var(--text-secondary);
}

.empty-icon {
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px;
}

.empty-text {
    margin: 0;
    line-height: 1.5;
}

// Transitions
.modal-backdrop-enter-active,
.modal-backdrop-leave-active {
    transition: opacity 0.3s ease;
}

.modal-backdrop-enter-from,
.modal-backdrop-leave-to {
    opacity: 0;
}

.modal-enter-active,
.modal-leave-active {
    transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

@media (max-width: 768px) {
    .modal-container {
        padding: 10px;
        padding-top: 60px;
    }

    .modal-content {
        max-width: 100%;
        max-height: 90vh;
    }

    .notification-item {
        padding: 16px;
    }

    .notification-actions {
        flex-direction: column;
    }
}
</style>
