.main-header {
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    background-color: var(--bg-primary);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: var(--shadow-sm);
    z-index: 30;
    position: fixed;
    top: 0;
    left: var(--sidebar-width);
    right: 0;
    transition: left var(--transition-normal);

    // Когда сайдбар свернут
    .sidebar-collapsed + & {
        left: var(--sidebar-collapsed-width);
    }

    // Мобильная версия
    @media (max-width: 992px) {
        left: 0;
    }
}

.header-left {
    display: flex;
    align-items: center;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.mobile-menu-button {
    display: none;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    margin-right: 16px;
    padding: 5px;
    border-radius: var(--radius-sm);

    &:hover {
        background-color: var(--bg-secondary);
    }

    @media (max-width: 992px) {
        display: block;
    }
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    margin-right: 16px;
    padding: 5px;
    border-radius: var(--radius-sm);

    &:hover {
        background-color: var(--bg-secondary);
    }

    @media (max-width: 992px) {
        display: none;
    }
}

.icon-rotated {
    transform: rotate(180deg);
}

.header-title-section {
    display: flex;
    flex-direction: column;
}

.header-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;

    @media (max-width: 576px) {
        font-size: 1.125rem;
    }
}

.breadcrumbs {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 2px;

    @media (max-width: 576px) {
        display: none;
    }
}

.breadcrumb-item {
    color: var(--text-secondary);
    text-decoration: none;

    &:hover:not(.current) {
        color: var(--primary-color);
        text-decoration: underline;
    }

    &.current {
        font-weight: 500;
        color: var(--text-primary);
    }
}

.breadcrumb-separator {
    margin: 0 4px;
    color: var(--text-disabled);
}

.action-button {
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    border-radius: var(--radius-sm);
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &:hover {
        background-color: var(--bg-secondary);
    }
}

.notifications-container {
    position: relative;
}

.notification-button {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    background-color: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

// Адаптивность
@media (max-width: 1024px) {
    .header {
        .header-search {
            max-width: 300px;
            margin: 0 1rem;
        }
    }
}

@media (max-width: 768px) {
    .header {
        left: 0;

        .header-left {
            .menu-toggle {
                display: block;
            }
        }

        .header-search {
            display: none;
        }
    }
}
