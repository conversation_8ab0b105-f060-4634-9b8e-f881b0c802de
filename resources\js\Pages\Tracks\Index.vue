<script setup>
import { Head } from "@inertiajs/vue3";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import { FileAudio, Info } from "lucide-vue-next";

// Хлебные крошки для страницы
const breadcrumbs = [{ name: "Треки" }];
</script>

<template>
    <DashboardLayout title="Треки" :breadcrumbs="breadcrumbs">
        <Head title="Треки" />

        <div class="content-container">
            <!-- Сообщение о разработке -->
            <div class="coming-soon-message">
                <FileAudio class="big-icon" size="64" />
                <h2>Раздел треков в разработке</h2>
                <p>Эта функциональность будет доступна в скором времени.</p>
                
                <div class="info-box">
                    <Info class="info-icon" size="24" />
                    <div>
                        <h3>Что вы сможете делать:</h3>
                        <ul>
                            <li>Просматривать все треки и их статистику</li>
                            <li>Управлять загруженными треками</li>
                            <li>Фильтровать треки по релизам</li>
                            <li>Анализировать популярность</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </DashboardLayout>
</template>

<style scoped>
.coming-soon-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px;
    text-align: center;
    
    h2 {
        margin: 24px 0 8px;
        font-size: 1.5rem;
    }
    
    p {
        color: var(--text-secondary);
        margin-bottom: 32px;
    }
}

.big-icon {
    color: var(--info-color);
    opacity: 0.7;
}

.info-box {
    display: flex;
    gap: 16px;
    background-color: var(--info-bg);
    border: 1px solid var(--info-color);
    border-radius: var(--radius-md);
    padding: 16px 24px;
    margin: 16px 0;
    max-width: 500px;
    text-align: left;
    
    .info-icon {
        color: var(--info-color);
        flex-shrink: 0;
    }
    
    h3 {
        font-size: 1rem;
        margin: 0 0 8px;
    }
    
    ul {
        margin: 0;
        padding-left: 16px;
        
        li {
            margin-bottom: 4px;
        }
    }
}
</style> 