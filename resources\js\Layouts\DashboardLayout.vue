<script setup>
import { ref, watch, onMounted } from "vue";
import { Head } from "@inertiajs/vue3";
import Sidebar from "@/Components/Layout/Sidebar.vue";
import Header from "@/Components/Layout/Header.vue";
import Footer from "@/Components/Layout/Footer.vue";

const props = defineProps({
    title: {
        type: String,
        required: true,
    },
    breadcrumbs: {
        type: Array,
        default: () => [],
    },
});

// Состояние отображения сайдбара
const sidebarCollapsed = ref(false);
const mobileMenuOpen = ref(false);

// Переключение состояния сайдбара
const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value;
    localStorage.setItem("sidebarCollapsed", sidebarCollapsed.value);
};

// Переключение мобильного меню
const toggleMobileMenu = () => {
    mobileMenuOpen.value = !mobileMenuOpen.value;
};

// Загрузка сохраненного состояния сайдбара при монтировании
onMounted(() => {
    sidebarCollapsed.value =
        localStorage.getItem("sidebarCollapsed") === "true";

    // Добавляем обработчик изменения размера окна
    window.addEventListener("resize", handleResize);

    // Начальная проверка размера
    handleResize();
});

// Обработчик изменения размера окна
const handleResize = () => {
    if (window.innerWidth > 992) {
        mobileMenuOpen.value = false;
    }
};

// Закрытие мобильного меню при клике на маршрут
const closeMobileMenu = () => {
    mobileMenuOpen.value = false;
};
</script>

<template>
    <div class="dashboard-layout">
        <Head :title="title" />

        <!-- Сайдбар -->
        <Sidebar
            :collapsed="sidebarCollapsed"
            :mobileOpen="mobileMenuOpen"
            @closeMobile="closeMobileMenu"
        />

        <!-- Хедер -->
        <Header
            :title="title"
            :breadcrumbs="breadcrumbs"
            :sidebarCollapsed="sidebarCollapsed"
            @toggleSidebar="toggleSidebar"
            @toggleMobileMenu="toggleMobileMenu"
        />

        <!-- Основной контент -->
        <div class="main-content">
            <!-- Основная область с контентом -->
            <main class="page-container">
                <slot></slot>
            </main>

            <!-- Футер -->
            <Footer />
        </div>

        <!-- Оверлей для закрытия мобильного меню -->
        <div
            v-if="mobileMenuOpen"
            class="mobile-overlay"
            @click="closeMobileMenu"
        ></div>
    </div>
</template>

<style lang="scss">
.dashboard-layout {
    display: flex;
    min-height: 100vh;
    position: relative;
}

.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 900;
    display: none;

    @media (max-width: 992px) {
        display: block;
    }
}
</style>
