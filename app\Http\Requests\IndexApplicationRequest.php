<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class IndexApplicationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Все аутентифицированные пользователи могут просматривать заявки
        // (с учётом ограничений в контроллере)
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => [
                'sometimes',
                'string',
                Rule::in(['pending', 'approved', 'rejected', 'all'])
            ],
            'type' => [
                'sometimes',
                'string',
                Rule::in(['promo', 'collaboration', 'release', 'custom', 'all'])
            ],
            'user_id' => [
                'sometimes',
                'integer',
                'exists:users,id'
            ],
            'per_page' => [
                'sometimes',
                'integer',
                'min:1',
                'max:100'
            ],
            'page' => [
                'sometimes',
                'integer',
                'min:1'
            ],
            'sort_by' => [
                'sometimes',
                'string',
                Rule::in(['created_at', 'updated_at', 'title', 'status', 'type'])
            ],
            'sort_direction' => [
                'sometimes',
                'string',
                Rule::in(['asc', 'desc'])
            ],
            'search' => [
                'sometimes',
                'string',
                'max:255'
            ],
            'date_from' => [
                'sometimes',
                'date',
                'before_or_equal:date_to'
            ],
            'date_to' => [
                'sometimes',
                'date',
                'after_or_equal:date_from'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'status.in' => 'Недопустимый статус фильтра.',
            'type.in' => 'Недопустимый тип заявки для фильтра.',
            'user_id.exists' => 'Указанный пользователь не существует.',
            'per_page.min' => 'Количество элементов на странице должно быть минимум :min.',
            'per_page.max' => 'Количество элементов на странице не может превышать :max.',
            'page.min' => 'Номер страницы должен быть минимум :min.',
            'sort_by.in' => 'Недопустимое поле для сортировки.',
            'sort_direction.in' => 'Направление сортировки должно быть "asc" или "desc".',
            'search.max' => 'Поисковый запрос не может превышать :max символов.',
            'date_from.before_or_equal' => 'Дата "от" должна быть раньше или равна дате "до".',
            'date_to.after_or_equal' => 'Дата "до" должна быть позже или равна дате "от".'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'status' => 'статус',
            'type' => 'тип заявки',
            'user_id' => 'пользователь',
            'per_page' => 'количество на странице',
            'page' => 'страница',
            'sort_by' => 'поле сортировки',
            'sort_direction' => 'направление сортировки',
            'search' => 'поиск',
            'date_from' => 'дата от',
            'date_to' => 'дата до'
        ];
    }

    /**
     * Get the validated data with default values.
     *
     * @return array
     */
    public function validatedWithDefaults(): array
    {
        $validated = $this->validated();
        
        return array_merge([
            'status' => 'all',
            'type' => 'all',
            'per_page' => 15,
            'page' => 1,
            'sort_by' => 'created_at',
            'sort_direction' => 'desc'
        ], $validated);
    }
}
