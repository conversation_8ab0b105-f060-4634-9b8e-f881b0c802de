{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@inertiajs/vue3": "^2.0", "@vitejs/plugin-vue": "^5.0.0", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "sass": "^1.72.0", "vite": "^6.2.4", "vue": "^3.3.13"}, "dependencies": {"chart.js": "^4.5.0", "laravel-echo": "^1.15.3", "lucide-vue-next": "^0.359.0", "pinia": "^2.1.7", "pusher-js": "^8.4.0-rc2"}}