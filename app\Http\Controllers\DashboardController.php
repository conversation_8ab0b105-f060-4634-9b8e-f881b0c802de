<?php

namespace App\Http\Controllers;

use App\Models\Release;
use App\Models\Track;
use App\Models\User;
use App\Models\Application;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Показать главную страницу дашборда.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // Статистика
        $stats = $this->getStatsForUser($user);
        
        // Последние релизы
        $releases = $this->getReleasesForUser($user);
        
        // Возвращаем данные в компонент Dashboard
        return Inertia::render('Dashboard', [
            'stats' => $stats,
            'releases' => $releases
        ]);
    }
    
    /**
     * Получить статистику для пользователя в зависимости от его роли.
     *
     * @param User $user
     * @return array
     */
    private function getStatsForUser(User $user): array
    {
        if ($user->isAdmin() || $user->isManager()) {
            return [
                [
                    'title' => 'Всего релизов',
                    'value' => Release::count(),
                    'change' => $this->getPercentChange(
                        Release::whereMonth('created_at', now()->month)->count(),
                        Release::whereMonth('created_at', now()->subMonth()->month)->count()
                    ),
                    'changeType' => $this->getChangeType(
                        Release::whereMonth('created_at', now()->month)->count(),
                        Release::whereMonth('created_at', now()->subMonth()->month)->count()
                    ),
                ],
                [
                    'title' => 'Новые заявки',
                    'value' => Application::where('status', 'pending')->count(),
                    'change' => $this->getPercentChange(
                        Application::whereMonth('created_at', now()->month)->count(),
                        Application::whereMonth('created_at', now()->subMonth()->month)->count()
                    ),
                    'changeType' => $this->getChangeType(
                        Application::whereMonth('created_at', now()->month)->count(),
                        Application::whereMonth('created_at', now()->subMonth()->month)->count()
                    ),
                ],
                [
                    'title' => 'Активных артистов',
                    'value' => User::where('role', 'artist')->count(),
                    'change' => $this->getPercentChange(
                        User::where('role', 'artist')->whereMonth('created_at', now()->month)->count(),
                        User::where('role', 'artist')->whereMonth('created_at', now()->subMonth()->month)->count()
                    ),
                    'changeType' => $this->getChangeType(
                        User::where('role', 'artist')->whereMonth('created_at', now()->month)->count(),
                        User::where('role', 'artist')->whereMonth('created_at', now()->subMonth()->month)->count()
                    ),
                ],
                [
                    'title' => 'Всего треков',
                    'value' => Track::count(),
                    'change' => $this->getPercentChange(
                        Track::whereMonth('created_at', now()->month)->count(),
                        Track::whereMonth('created_at', now()->subMonth()->month)->count()
                    ),
                    'changeType' => $this->getChangeType(
                        Track::whereMonth('created_at', now()->month)->count(),
                        Track::whereMonth('created_at', now()->subMonth()->month)->count()
                    ),
                ],
            ];
        } else {
            // Для обычных артистов
            return [
                [
                    'title' => 'Мои релизы',
                    'value' => Release::whereHas('artist', function ($query) use ($user) {
                        $query->where('user_id', $user->id);
                    })->count(),
                    'change' => null,
                    'changeType' => 'neutral',
                ],
                [
                    'title' => 'Треки',
                    'value' => Track::whereHas('release.artist', function ($query) use ($user) {
                        $query->where('user_id', $user->id);
                    })->count(),
                    'change' => null,
                    'changeType' => 'neutral',
                ],
                [
                    'title' => 'Заявки',
                    'value' => Application::where('user_id', $user->id)->count(),
                    'change' => null,
                    'changeType' => 'neutral',
                ],
                [
                    'title' => 'Одобренные релизы',
                    'value' => Release::whereHas('artist', function ($query) use ($user) {
                        $query->where('user_id', $user->id);
                    })->where('status', 'approved')->count(),
                    'change' => null,
                    'changeType' => 'neutral',
                ],
            ];
        }
    }
    
    /**
     * Получить последние релизы для пользователя в зависимости от его роли.
     *
     * @param User $user
     * @return array
     */
    private function getReleasesForUser(User $user): array
    {
        if ($user->isAdmin() || $user->isManager()) {
            // Для админов и менеджеров показываем все релизы
            $releases = Release::with('artist.user')
                ->latest()
                ->take(5)
                ->get();
        } else {
            // Для артистов показываем только их релизы
            $releases = Release::whereHas('artist', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->with('artist.user')
            ->latest()
            ->take(5)
            ->get();
        }

        return $releases->map(function ($release) {
            return [
                'id' => $release->id,
                'title' => $release->title,
                'type' => $release->type,
                'status' => $release->status,
                'created_at' => $release->created_at,
                'cover_image_url' => $release->cover_image_url,
                'artist' => [
                    'id' => $release->artist->id,
                    'display_name' => $release->artist->display_name,
                ],
            ];
        })->toArray();
    }
    
    /**
     * Вычисляет процентное изменение между текущим и предыдущим значениями.
     *
     * @param int $current
     * @param int $previous
     * @return float|int
     */
    private function getPercentChange(int $current, int $previous): float|int
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        
        return round((($current - $previous) / $previous) * 100);
    }
    
    /**
     * Определяет тип изменения: позитивный, негативный или нейтральный.
     *
     * @param int $current
     * @param int $previous
     * @return string
     */
    private function getChangeType(int $current, int $previous): string
    {
        if ($current == $previous) {
            return 'neutral';
        }
        
        return $current > $previous ? 'positive' : 'negative';
    }
} 