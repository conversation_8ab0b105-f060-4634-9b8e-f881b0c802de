<script setup>
import { ref, computed, watch } from "vue";
import { useForm, router } from "@inertiajs/vue3";
import {
    Upload,
    Music,
    Plus,
    X,
    Clock,
    Disc,
    AlertTriangle,
    Loader2,
    CheckCircle,
    Trash2,
    Edit,
    Play,
    Pause,
    Volume2,
} from "lucide-vue-next";

const props = defineProps({
    release: {
        type: Object,
        required: true,
    },
    tracks: {
        type: Array,
        required: true,
    },
    canEdit: {
        type: Boolean,
        default: false,
    },
});

// Состояние компонента
const isAddingTrack = ref(false);
const isEditingTrack = ref(null);
const filePreview = ref(null);
const fileError = ref("");
const isDragOver = ref(false);
const audioPreview = ref(null);
const isPlaying = ref(false);

// Форма для загрузки трека
const form = useForm({
    title: "",
    track_number: "",
    file: null,
    is_explicit: false,
    lyrics: "",
});

// Вычисляемые свойства
const sortedTracks = computed(() => {
    return [...props.tracks].sort((a, b) => a.track_number - b.track_number);
});

const nextTrackNumber = computed(() => {
    if (props.tracks.length === 0) return 1;
    const maxNumber = Math.max(...props.tracks.map(t => t.track_number));
    return maxNumber + 1;
});

const isValid = computed(() => {
    return form.title && 
           form.track_number && 
           (form.file || isEditingTrack.value) && 
           !fileError.value;
});

// Методы управления формой
function resetForm() {
    form.reset();
    form.track_number = nextTrackNumber.value;
    filePreview.value = null;
    fileError.value = "";
    isAddingTrack.value = false;
    isEditingTrack.value = null;
    stopAudioPreview();
}

function initAddTrackForm() {
    resetForm();
    isAddingTrack.value = true;
    form.track_number = nextTrackNumber.value;
}

function initEditTrackForm(track) {
    resetForm();
    isEditingTrack.value = track.id;
    form.title = track.title;
    form.track_number = track.track_number;
    form.is_explicit = track.is_explicit;
    form.lyrics = track.lyrics || "";
}

function cancelTrackUpload() {
    resetForm();
}

// Обработка файлов
function handleFileUpload(event) {
    const file = event.target.files[0];
    if (file) {
        processFile(file);
    }
}

function handleDrop(event) {
    event.preventDefault();
    isDragOver.value = false;
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    isDragOver.value = true;
}

function handleDragLeave() {
    isDragOver.value = false;
}

function processFile(file) {
    fileError.value = "";

    // Проверка типа файла
    const allowedTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/x-m4a', 'audio/aac', 'audio/flac'];
    if (!allowedTypes.includes(file.type)) {
        fileError.value = "Поддерживаются только аудио файлы (MP3, WAV, OGG, M4A, AAC, FLAC)";
        return;
    }

    // Проверка размера файла (50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
        fileError.value = "Размер файла не должен превышать 50MB";
        return;
    }

    // Установка файла
    form.file = file;
    filePreview.value = file.name;

    // Создание аудио превью
    createAudioPreview(file);

    // Если название трека не указано, используем имя файла
    if (!form.title) {
        form.title = file.name
            .replace(/\.[^/.]+$/, "")
            .replace(/[_-]/g, " ")
            .replace(/\b\w/g, (l) => l.toUpperCase());
    }
}

function removeFile() {
    form.file = null;
    filePreview.value = null;
    fileError.value = "";
    stopAudioPreview();
    
    const fileInput = document.getElementById("track_file");
    if (fileInput) fileInput.value = "";
}

function createAudioPreview(file) {
    const url = URL.createObjectURL(file);
    audioPreview.value = new Audio(url);
    
    audioPreview.value.addEventListener('ended', () => {
        isPlaying.value = false;
    });
}

function toggleAudioPreview() {
    if (!audioPreview.value) return;
    
    if (isPlaying.value) {
        audioPreview.value.pause();
        isPlaying.value = false;
    } else {
        audioPreview.value.play();
        isPlaying.value = true;
    }
}

function stopAudioPreview() {
    if (audioPreview.value) {
        audioPreview.value.pause();
        audioPreview.value.currentTime = 0;
        isPlaying.value = false;
        URL.revokeObjectURL(audioPreview.value.src);
        audioPreview.value = null;
    }
}

// Отправка формы
function submitTrack() {
    if (!isValid.value) return;
    
    if (isEditingTrack.value) {
        // Обновление существующего трека
        form.put(route("tracks.update", isEditingTrack.value), {
            onSuccess: () => {
                resetForm();
            },
            preserveScroll: true,
        });
    } else {
        // Создание нового трека
        form.post(route("tracks.store", props.release.id), {
            onSuccess: () => {
                resetForm();
            },
            preserveScroll: true,
        });
    }
}

// Удаление трека
function deleteTrack(track) {
    if (confirm("Вы уверены, что хотите удалить этот трек?")) {
        router.delete(route("tracks.destroy", track.id), {
            preserveScroll: true,
        });
    }
}

// Форматирование длительности
function formatDuration(seconds) {
    if (!seconds) return "0:00";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// Очистка при размонтировании
watch(() => props.tracks, () => {
    if (isEditingTrack.value && !props.tracks.find(t => t.id === isEditingTrack.value)) {
        resetForm();
    }
});
</script>

<template>
    <div class="track-uploader">
        <h3 class="section-title">
            <Music class="title-icon" size="20" />
            Треки релиза
        </h3>

        <!-- Список треков -->
        <div v-if="sortedTracks.length > 0" class="tracks-list">
            <div
                v-for="track in sortedTracks"
                :key="track.id"
                class="track-item"
            >
                <div class="track-number">
                    {{ track.track_number }}
                </div>

                <div class="track-info">
                    <div class="track-title">
                        {{ track.title }}
                        <span v-if="track.is_explicit" class="explicit-badge">E</span>
                    </div>

                    <div class="track-meta">
                        <span class="duration">
                            <Clock size="14" class="meta-icon" />
                            {{ formatDuration(track.duration_seconds) }}
                        </span>
                        <span class="file-type">
                            <Disc size="14" class="meta-icon" />
                            {{ track.file_type?.toUpperCase() || "MP3" }}
                        </span>
                    </div>
                </div>

                <div v-if="canEdit" class="track-actions">
                    <button
                        type="button"
                        class="action-btn edit-btn"
                        @click="initEditTrackForm(track)"
                        title="Редактировать трек"
                    >
                        <Edit size="16" />
                    </button>
                    <button
                        type="button"
                        class="action-btn delete-btn"
                        @click="deleteTrack(track)"
                        title="Удалить трек"
                    >
                        <Trash2 size="16" />
                    </button>
                </div>
            </div>
        </div>

        <!-- Форма добавления/редактирования трека -->
        <div v-if="isAddingTrack || isEditingTrack" class="track-form">
            <h4 class="form-title">
                {{ isEditingTrack ? 'Редактировать трек' : 'Добавить новый трек' }}
            </h4>

            <form @submit.prevent="submitTrack" class="upload-form">
                <div class="form-row">
                    <!-- Название трека -->
                    <div class="form-group">
                        <label for="track_title" class="form-label">
                            Название трека *
                        </label>
                        <input
                            id="track_title"
                            v-model="form.title"
                            type="text"
                            class="form-input"
                            :class="{ error: form.errors.title }"
                            placeholder="Введите название трека"
                            required
                        />
                        <p v-if="form.errors.title" class="error-text">
                            {{ form.errors.title }}
                        </p>
                    </div>

                    <!-- Номер трека -->
                    <div class="form-group track-number-group">
                        <label for="track_number" class="form-label">
                            Номер трека *
                        </label>
                        <input
                            id="track_number"
                            v-model="form.track_number"
                            type="number"
                            min="1"
                            class="form-input"
                            :class="{ error: form.errors.track_number }"
                            required
                        />
                        <p v-if="form.errors.track_number" class="error-text">
                            {{ form.errors.track_number }}
                        </p>
                    </div>
                </div>

                <!-- Загрузка файла -->
                <div class="form-group">
                    <label class="form-label">
                        Аудио файл {{ isEditingTrack ? '' : '*' }}
                    </label>

                    <!-- Превью файла -->
                    <div v-if="filePreview" class="file-preview">
                        <div class="file-info">
                            <Volume2 class="file-icon" size="20" />
                            <span class="file-name">{{ filePreview }}</span>
                        </div>
                        
                        <div class="file-actions">
                            <button
                                v-if="audioPreview"
                                type="button"
                                class="preview-btn"
                                @click="toggleAudioPreview"
                                :title="isPlaying ? 'Остановить' : 'Воспроизвести'"
                            >
                                <Play v-if="!isPlaying" size="16" />
                                <Pause v-else size="16" />
                            </button>
                            
                            <button
                                type="button"
                                class="remove-btn"
                                @click="removeFile"
                                title="Удалить файл"
                            >
                                <X size="16" />
                            </button>
                        </div>
                    </div>

                    <!-- Область загрузки -->
                    <div
                        v-else
                        class="file-upload-area"
                        :class="{
                            'drag-over': isDragOver,
                            'error': form.errors.file || fileError,
                        }"
                        @drop="handleDrop"
                        @dragover="handleDragOver"
                        @dragleave="handleDragLeave"
                        @click="$refs.fileInput.click()"
                    >
                        <div class="upload-content">
                            <Upload class="upload-icon" size="32" />
                            <p class="upload-text">
                                Перетащите аудиофайл сюда или нажмите для выбора
                            </p>
                            <p class="upload-hint">
                                MP3, WAV, OGG, M4A, AAC, FLAC • Макс. 50MB
                            </p>
                        </div>
                        
                        <input
                            ref="fileInput"
                            id="track_file"
                            type="file"
                            class="file-input"
                            accept="audio/mpeg,audio/mp3,audio/wav,audio/ogg,audio/x-m4a,audio/aac,audio/flac"
                            @change="handleFileUpload"
                            :required="!isEditingTrack"
                        />
                    </div>

                    <!-- Ошибки -->
                    <p v-if="form.errors.file" class="error-text">
                        {{ form.errors.file }}
                    </p>
                    <p v-else-if="fileError" class="error-text">
                        {{ fileError }}
                    </p>
                </div>

                <!-- Дополнительные опции -->
                <div class="form-row">
                    <!-- Explicit контент -->
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input
                                v-model="form.is_explicit"
                                type="checkbox"
                                class="checkbox-input"
                            />
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-text">
                                Нецензурная лексика
                            </span>
                        </label>
                    </div>
                </div>

                <!-- Текст песни -->
                <div class="form-group">
                    <label for="lyrics" class="form-label">
                        Текст песни
                    </label>
                    <textarea
                        id="lyrics"
                        v-model="form.lyrics"
                        class="form-textarea"
                        :class="{ error: form.errors.lyrics }"
                        placeholder="Введите текст песни (необязательно)"
                        rows="4"
                    ></textarea>
                    <p v-if="form.errors.lyrics" class="error-text">
                        {{ form.errors.lyrics }}
                    </p>
                </div>

                <!-- Кнопки формы -->
                <div class="form-actions">
                    <button
                        type="button"
                        class="btn btn-outline"
                        @click="cancelTrackUpload"
                    >
                        Отмена
                    </button>

                    <button
                        type="submit"
                        class="btn btn-primary"
                        :disabled="!isValid || form.processing"
                    >
                        <Loader2
                            v-if="form.processing"
                            class="btn-icon spin"
                            size="16"
                        />
                        <span v-if="form.processing">
                            {{ isEditingTrack ? 'Сохранение...' : 'Загрузка...' }}
                        </span>
                        <span v-else>
                            {{ isEditingTrack ? 'Сохранить изменения' : 'Добавить трек' }}
                        </span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Кнопка добавления трека -->
        <button
            v-else-if="canEdit"
            type="button"
            class="add-track-btn"
            @click="initAddTrackForm"
        >
            <Plus class="btn-icon" size="18" />
            <span>Добавить трек</span>
        </button>

        <!-- Уведомление для релизов без возможности редактирования -->
        <div
            v-else-if="!canEdit && sortedTracks.length === 0"
            class="locked-info"
        >
            <AlertTriangle class="info-icon" size="20" />
            <p class="info-text">
                Нельзя добавлять треки к этому релизу, так как он уже отправлен на модерацию.
            </p>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.track-uploader {
    margin-bottom: 24px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 16px;
    color: var(--text-primary);
}

.title-icon {
    color: var(--primary-color);
}

.tracks-list {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    margin-bottom: 16px;
    overflow: hidden;
}

.track-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background: rgba(0, 0, 0, 0.02);
    }
}

.track-number {
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.track-info {
    flex: 1;
    min-width: 0;
}

.track-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.explicit-badge {
    background: var(--danger-color);
    color: white;
    font-size: 0.625rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    line-height: 1;
}

.track-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.meta-icon {
    margin-right: 4px;
}

.track-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &.edit-btn {
        background: rgba(var(--primary-rgb), 0.1);
        color: var(--primary-color);

        &:hover {
            background: rgba(var(--primary-rgb), 0.2);
        }
    }

    &.delete-btn {
        background: rgba(244, 67, 54, 0.1);
        color: var(--danger-color);

        &:hover {
            background: rgba(244, 67, 54, 0.2);
        }
    }
}

.track-form {
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    padding: 24px;
    margin-bottom: 16px;
}

.form-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 20px;
    color: var(--text-primary);
}

.upload-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 16px;
    align-items: start;

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
    }
}

.track-number-group {
    width: 120px;

    @media (max-width: 768px) {
        width: 100%;
    }
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-input,
.form-textarea {
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }

    &.error {
        border-color: var(--danger-color);
    }
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
}

.checkbox-input {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;

    &::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 6px;
        width: 6px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        opacity: 0;
        transition: opacity 0.2s ease;
    }
}

.checkbox-input:checked + .checkbox-custom {
    background-color: var(--primary-color);
    border-color: var(--primary-color);

    &::after {
        opacity: 1;
    }
}

.checkbox-text {
    font-size: 0.875rem;
    color: var(--text-primary);
}

.file-preview {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.file-icon {
    color: var(--primary-color);
    flex-shrink: 0;
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    truncate: true;
}

.file-actions {
    display: flex;
    gap: 8px;
}

.preview-btn,
.remove-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.preview-btn {
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);

    &:hover {
        background: rgba(var(--primary-rgb), 0.2);
    }
}

.remove-btn {
    background: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);

    &:hover {
        background: rgba(244, 67, 54, 0.2);
    }
}

.file-upload-area {
    border: 2px dashed rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover,
    &.drag-over {
        border-color: var(--primary-color);
        background: rgba(var(--primary-rgb), 0.05);
    }

    &.error {
        border-color: var(--danger-color);
        background: rgba(244, 67, 54, 0.05);
    }
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.upload-icon {
    color: var(--text-secondary);
}

.upload-text {
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.upload-hint {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

.file-input {
    display: none;
}

.error-text {
    color: var(--danger-color);
    font-size: 0.75rem;
    margin-top: 4px;
}

.form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.btn {
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    border: none;

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.btn-outline {
    background: transparent;
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: var(--text-primary);

    &:hover:not(:disabled) {
        background: rgba(0, 0, 0, 0.05);
    }
}

.btn-primary {
    background: var(--primary-color);
    color: white;

    &:hover:not(:disabled) {
        background: var(--primary-hover);
    }
}

.btn-icon {
    flex-shrink: 0;
}

.add-track-btn {
    width: 100%;
    padding: 16px;
    background: transparent;
    border: 2px dashed rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;

    &:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
        background: rgba(var(--primary-rgb), 0.05);
    }
}

.locked-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: var(--radius-md);
    color: #856404;
}

.info-icon {
    flex-shrink: 0;
}

.info-text {
    margin: 0;
    font-size: 0.875rem;
}

.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
