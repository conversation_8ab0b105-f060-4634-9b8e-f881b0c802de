<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Services\OAuthService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use Laravel\Socialite\Two\InvalidStateException;

class OAuthController extends Controller
{

    /**
     * Redirect to OAuth provider.
     */
    public function redirect(string $provider): RedirectResponse
    {
        if (!OAuthService::isValidProvider($provider)) {
            return redirect()->route('login')->withErrors([
                'provider' => 'Неподдерживаемый провайдер аутентификации.'
            ]);
        }

        try {
            return Socialite::driver($provider)->redirect();
        } catch (\Exception $e) {
            return redirect()->route('login')->withErrors([
                'oauth' => 'Ошибка при перенаправлении к провайдеру: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Handle OAuth callback.
     */
    public function callback(string $provider): RedirectResponse
    {
        if (!OAuthService::isValidProvider($provider)) {
            return redirect()->route('login')->withErrors([
                'provider' => 'Неподдерживаемый провайдер аутентификации.'
            ]);
        }

        try {
            $socialUser = Socialite::driver($provider)->user();
        } catch (InvalidStateException $e) {
            return redirect()->route('login')->withErrors([
                'oauth' => 'Недействительное состояние OAuth. Попробуйте снова.'
            ]);
        } catch (\Exception $e) {
            return redirect()->route('login')->withErrors([
                'oauth' => 'Ошибка при получении данных от провайдера: ' . $e->getMessage()
            ]);
        }

        // Проверяем, есть ли пользователь с таким email
        $existingUser = OAuthService::findUserByEmail($socialUser->getEmail());

        if ($existingUser) {
            // Если пользователь существует, обновляем OAuth данные
            $user = OAuthService::updateUserOAuth($existingUser, $provider, $socialUser);
        } else {
            // Создаем нового пользователя
            $user = OAuthService::createUserFromOAuth($provider, $socialUser);
        }

        // Авторизуем пользователя
        Auth::login($user, true);

        // Перенаправляем на дашборд
        return redirect()->intended(route('dashboard'));
    }



    /**
     * Unlink OAuth provider from user account.
     */
    public function unlink(Request $request, string $provider): RedirectResponse
    {
        $user = $request->user();

        if (!$user->isOAuthUser() || $user->provider !== $provider) {
            return back()->withErrors([
                'oauth' => 'Этот провайдер не привязан к вашему аккаунту.'
            ]);
        }

        // Проверяем, можно ли отвязать провайдера
        if (!OAuthService::canUnlinkProvider($user)) {
            return back()->withErrors([
                'oauth' => 'Нельзя отвязать единственный способ входа. Сначала установите пароль.'
            ]);
        }

        // Отвязываем провайдера
        OAuthService::unlinkProvider($user);

        return back()->with('success', 'Провайдер успешно отвязан от аккаунта.');
    }
}
