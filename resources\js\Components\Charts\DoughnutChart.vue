<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from "vue";
import {
    Chart,
    ArcElement,
    Too<PERSON><PERSON>,
    Legend,
} from "chart.js";

// Регистрируем компоненты Chart.js
Chart.register(Arc<PERSON><PERSON>, Toolt<PERSON>, Legend);

const props = defineProps({
    data: {
        type: Object,
        required: true,
    },
    options: {
        type: Object,
        default: () => ({}),
    },
    height: {
        type: Number,
        default: 300,
    },
    responsive: {
        type: Boolean,
        default: true,
    },
});

const chartRef = ref(null);
const chartInstance = ref(null);

// Дефолтные опции для круговой диаграммы
const defaultOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: "bottom",
            labels: {
                usePointStyle: true,
                padding: 20,
                font: {
                    size: 12,
                },
                generateLabels: function(chart) {
                    const data = chart.data;
                    if (data.labels.length && data.datasets.length) {
                        return data.labels.map((label, i) => {
                            const dataset = data.datasets[0];
                            const backgroundColor = dataset.backgroundColor[i];
                            const value = dataset.data[i];
                            const total = dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            
                            return {
                                text: `${label}: ${value} (${percentage}%)`,
                                fillStyle: backgroundColor,
                                strokeStyle: backgroundColor,
                                lineWidth: 0,
                                pointStyle: 'circle',
                                hidden: false,
                                index: i
                            };
                        });
                    }
                    return [];
                }
            },
            tooltip: {
                backgroundColor: "rgba(0, 0, 0, 0.8)",
                titleColor: "#fff",
                bodyColor: "#fff",
                borderColor: "rgba(255, 255, 255, 0.1)",
                borderWidth: 1,
                cornerRadius: 8,
                padding: 12,
                callbacks: {
                    label: function(context) {
                        const label = context.label || '';
                        const value = context.parsed;
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((value / total) * 100).toFixed(1);
                        return `${label}: ${value} (${percentage}%)`;
                    }
                }
            },
        },
    },
    cutout: "60%", // Делает диаграмму в виде кольца
    elements: {
        arc: {
            borderWidth: 2,
            borderColor: "#fff",
        },
    },
};

// Объединяем дефолтные опции с переданными
const mergedOptions = computed(() => {
    return {
        ...defaultOptions,
        ...props.options,
        plugins: {
            ...defaultOptions.plugins,
            ...props.options.plugins,
        },
    };
});

// Создание графика
const createChart = () => {
    if (!chartRef.value) return;

    // Уничтожаем предыдущий график если он существует
    if (chartInstance.value) {
        chartInstance.value.destroy();
    }

    chartInstance.value = new Chart(chartRef.value, {
        type: "doughnut",
        data: props.data,
        options: mergedOptions.value,
    });
};

// Обновление данных графика
const updateChart = () => {
    if (!chartInstance.value) return;

    chartInstance.value.data = props.data;
    chartInstance.value.update("none");
};

// Уничтожение графика
const destroyChart = () => {
    if (chartInstance.value) {
        chartInstance.value.destroy();
        chartInstance.value = null;
    }
};

// Lifecycle hooks
onMounted(async () => {
    await nextTick();
    createChart();
});

onUnmounted(() => {
    destroyChart();
});

// Watchers
watch(
    () => props.data,
    () => {
        if (chartInstance.value) {
            updateChart();
        } else {
            createChart();
        }
    },
    { deep: true }
);

watch(
    () => props.options,
    () => {
        createChart();
    },
    { deep: true }
);

// Expose methods for parent components
defineExpose({
    chart: chartInstance,
    updateChart,
    destroyChart,
});
</script>

<template>
    <div class="chart-container" :style="{ height: height + 'px' }">
        <canvas
            ref="chartRef"
            class="chart-canvas"
            :class="{ responsive: responsive }"
        ></canvas>
    </div>
</template>

<style lang="scss" scoped>
.chart-container {
    position: relative;
    width: 100%;
    
    .chart-canvas {
        width: 100% !important;
        height: 100% !important;
        
        &.responsive {
            max-width: 100%;
            max-height: 100%;
        }
    }
}
</style>
