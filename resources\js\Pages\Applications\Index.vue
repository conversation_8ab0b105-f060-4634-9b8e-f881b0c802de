<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { router, usePage } from "@inertiajs/vue3";
import ApplicationModal from "@/Components/Application/ApplicationModal.vue";
import { useNotificationStore } from "@/Stores/notifications";
import { $confirm } from "@/composables/useConfirm";
import {
    Plus,
    Search,
    Filter,
    Eye,
    Edit,
    Trash2,
    Clock,
    CheckCircle,
    X,
    User,
    Calendar,
    FileText,
    MoreVertical,
    Download,
} from "lucide-vue-next";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";

const props = defineProps({
    applications: {
        type: Object,
        required: true,
    },
    filters: {
        type: Object,
        required: true,
    },
});

const page = usePage();
const user = computed(() => page.props.auth.user);
const notificationStore = useNotificationStore();

// Состояние компонента
const showModal = ref(false);
const modalMode = ref("create");
const selectedApplication = ref(null);
const searchQuery = ref(props.filters.search || "");
const statusFilter = ref(props.filters.status || "all");
const typeFilter = ref(props.filters.type || "all");
const showFilters = ref(false);

// Конфигурация статусов
const statusConfig = {
    pending: { label: "На рассмотрении", color: "warning", icon: Clock },
    approved: { label: "Одобрена", color: "success", icon: CheckCircle },
    rejected: { label: "Отклонена", color: "danger", icon: X },
};

// Типы заявок
const applicationTypes = {
    promo: "Промо-кампания",
    collaboration: "Сотрудничество",
    release: "Релиз",
    custom: "Другое",
};

// Опции фильтров
const statusOptions = [
    { value: "all", label: "Все статусы" },
    { value: "pending", label: "На рассмотрении" },
    { value: "approved", label: "Одобрены" },
    { value: "rejected", label: "Отклонены" },
];

const typeOptions = [
    { value: "all", label: "Все типы" },
    { value: "promo", label: "Промо-кампания" },
    { value: "collaboration", label: "Сотрудничество" },
    { value: "release", label: "Релиз" },
    { value: "custom", label: "Другое" },
];

// Вычисляемые свойства
const canCreateApplications = computed(() => {
    return (
        user.value &&
        (user.value.role === "artist" || user.value.role === "manager")
    );
});

const canReviewApplications = computed(() => {
    return user.value && (user.value.is_admin || user.value.is_manager);
});

const hasFilters = computed(() => {
    return (
        searchQuery.value ||
        statusFilter.value !== "all" ||
        typeFilter.value !== "all"
    );
});

// Методы управления модальным окном
function openCreateModal() {
    selectedApplication.value = null;
    modalMode.value = "create";
    showModal.value = true;
}

function openViewModal(application) {
    selectedApplication.value = application;
    modalMode.value = "view";
    showModal.value = true;
}

function openEditModal(application) {
    selectedApplication.value = application;
    modalMode.value = "edit";
    showModal.value = true;
}

function openReviewModal(application) {
    selectedApplication.value = application;
    modalMode.value = "review";
    showModal.value = true;
}

function closeModal() {
    showModal.value = false;
    selectedApplication.value = null;
}

// Обработчики событий
function onApplicationSubmitted() {
    router.reload({ only: ["applications"] });
}

function onApplicationReviewed() {
    router.reload({ only: ["applications"] });
}

// Фильтрация и поиск
function applyFilters() {
    const params = {
        search: searchQuery.value || undefined,
        status: statusFilter.value !== "all" ? statusFilter.value : undefined,
        type: typeFilter.value !== "all" ? typeFilter.value : undefined,
    };

    router.get("/applications", params, {
        preserveState: true,
        replace: true,
    });
}

function clearFilters() {
    searchQuery.value = "";
    statusFilter.value = "all";
    typeFilter.value = "all";
    applyFilters();
}

// Удаление заявки
async function deleteApplication(application) {
    const confirmed = await $confirm.danger(
        `Заявка "${application.title}" будет удалена безвозвратно.`,
        "Удалить заявку?"
    );

    if (confirmed) {
        router.delete(`/applications/${application.id}`, {
            preserveScroll: true,
        });
    }
}

// Форматирование даты
function formatDate(date) {
    return new Date(date).toLocaleDateString("ru-RU", {
        year: "numeric",
        month: "short",
        day: "numeric",
    });
}

// Проверка прав доступа
function canEditApplication(application) {
    return (
        application.user_id === user.value?.id &&
        application.status === "pending"
    );
}

function canDeleteApplication(application) {
    return user.value?.is_admin || application.user_id === user.value?.id;
}

// Real-time обновления
onMounted(() => {
    if (window.Echo && user.value) {
        // Слушаем обновления заявок
        window.Echo.private(`App.Models.User.${user.value.id}`).listen(
            "ApplicationStatusUpdated",
            (e) => {
                console.log("Статус заявки обновлен:", e);
                // Перезагружаем список заявок
                router.reload({ only: ["applications"] });
            }
        );

        // Если пользователь админ/менеджер, слушаем новые заявки
        if (canReviewApplications.value) {
            window.Echo.private("admin.applications").listen(
                "ApplicationCreated",
                (e) => {
                    console.log("Новая заявка создана:", e);
                    // Перезагружаем список заявок
                    router.reload({ only: ["applications"] });
                }
            );
        }
    }
});

onUnmounted(() => {
    if (window.Echo) {
        window.Echo.leaveChannel(`App.Models.User.${user.value?.id}`);
        if (canReviewApplications.value) {
            window.Echo.leaveChannel("admin.applications");
        }
    }
});

// Watchers для автоматического применения фильтров
watch([searchQuery], () => {
    const timeoutId = setTimeout(() => {
        applyFilters();
    }, 500);

    return () => clearTimeout(timeoutId);
});

watch([statusFilter, typeFilter], () => {
    applyFilters();
});
</script>

<template>
    <DashboardLayout title="Заявки">
        <template #header>
            <div class="page-header">
                <div class="header-content">
                    <h1 class="page-title">Заявки</h1>
                    <p class="page-subtitle">
                        Управление заявками на сотрудничество и промо-кампании
                    </p>
                </div>
            </div>
        </template>

        <div class="applications-page">
            <!-- Фильтры и поиск -->
            <div class="filters-section">
                <div class="search-bar">
                    <div class="search-input-wrapper">
                        <Search class="search-icon" size="20" />
                        <input
                            v-model="searchQuery"
                            type="text"
                            class="search-input"
                            placeholder="Поиск по заголовку или описанию..."
                        />
                    </div>

                    <button
                        type="button"
                        class="filter-toggle"
                        :class="{ active: showFilters }"
                        @click="showFilters = !showFilters"
                    >
                        <Filter size="16" />
                        <span>Фильтры</span>
                    </button>

                    <button
                        v-if="canCreateApplications"
                        type="button"
                        class="btn btn-primary"
                        @click="openCreateModal"
                    >
                        <Plus class="btn-icon" size="16" />
                        <span>Новая заявка</span>
                    </button>
                </div>

                <!-- Расширенные фильтры -->
                <Transition name="filters">
                    <div v-if="showFilters" class="filters-panel">
                        <div class="filter-group">
                            <label class="filter-label">Статус</label>
                            <select
                                v-model="statusFilter"
                                class="filter-select"
                            >
                                <option
                                    v-for="option in statusOptions"
                                    :key="option.value"
                                    :value="option.value"
                                >
                                    {{ option.label }}
                                </option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">Тип</label>
                            <select v-model="typeFilter" class="filter-select">
                                <option
                                    v-for="option in typeOptions"
                                    :key="option.value"
                                    :value="option.value"
                                >
                                    {{ option.label }}
                                </option>
                            </select>
                        </div>

                        <div class="filter-actions">
                            <button
                                v-if="hasFilters"
                                type="button"
                                class="btn btn-outline btn-sm"
                                @click="clearFilters"
                            >
                                Сбросить
                            </button>
                        </div>
                    </div>
                </Transition>
            </div>

            <!-- Список заявок -->
            <div class="applications-list">
                <div v-if="applications.data.length === 0" class="empty-state">
                    <FileText class="empty-icon" size="48" />
                    <h3 class="empty-title">Заявок пока нет</h3>
                    <p class="empty-text">
                        {{
                            hasFilters
                                ? "По вашему запросу ничего не найдено. Попробуйте изменить фильтры."
                                : "Создайте первую заявку, чтобы начать сотрудничество с лейблом."
                        }}
                    </p>
                    <button
                        v-if="canCreateApplications && !hasFilters"
                        type="button"
                        class="btn btn-primary"
                        @click="openCreateModal"
                    >
                        <Plus class="btn-icon" size="16" />
                        <span>Создать заявку</span>
                    </button>
                </div>

                <div
                    v-for="application in applications.data"
                    :key="application.id"
                    class="application-card"
                >
                    <div class="card-header">
                        <div class="application-info">
                            <h3 class="application-title">
                                {{ application.title }}
                            </h3>

                            <div class="application-meta">
                                <span class="meta-item">
                                    <User size="14" class="meta-icon" />
                                    {{ application.user?.name || "Неизвестно" }}
                                </span>

                                <span class="meta-item">
                                    <Calendar size="14" class="meta-icon" />
                                    {{ formatDate(application.created_at) }}
                                </span>

                                <span class="meta-item type-badge">
                                    {{
                                        applicationTypes[application.type] ||
                                        application.type
                                    }}
                                </span>
                            </div>
                        </div>

                        <div class="card-actions">
                            <!-- Статус -->
                            <div
                                class="status-badge"
                                :class="statusConfig[application.status]?.color"
                            >
                                <component
                                    :is="statusConfig[application.status]?.icon"
                                    size="14"
                                    class="status-icon"
                                />
                                <span>{{
                                    statusConfig[application.status]?.label
                                }}</span>
                            </div>

                            <!-- Меню действий -->
                            <div class="dropdown">
                                <button type="button" class="dropdown-toggle">
                                    <MoreVertical size="16" />
                                </button>

                                <div class="dropdown-menu">
                                    <button
                                        type="button"
                                        class="dropdown-item"
                                        @click="openViewModal(application)"
                                    >
                                        <Eye size="14" />
                                        <span>Просмотр</span>
                                    </button>

                                    <button
                                        v-if="canEditApplication(application)"
                                        type="button"
                                        class="dropdown-item"
                                        @click="openEditModal(application)"
                                    >
                                        <Edit size="14" />
                                        <span>Редактировать</span>
                                    </button>

                                    <button
                                        v-if="
                                            canReviewApplications &&
                                            application.status === 'pending'
                                        "
                                        type="button"
                                        class="dropdown-item"
                                        @click="openReviewModal(application)"
                                    >
                                        <CheckCircle size="14" />
                                        <span>Рассмотреть</span>
                                    </button>

                                    <button
                                        v-if="canDeleteApplication(application)"
                                        type="button"
                                        class="dropdown-item danger"
                                        @click="deleteApplication(application)"
                                    >
                                        <Trash2 size="14" />
                                        <span>Удалить</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-content">
                        <p class="application-description">
                            {{ application.description }}
                        </p>

                        <!-- Вложения -->
                        <div
                            v-if="application.attachments?.length"
                            class="attachments-info"
                        >
                            <span class="attachments-count">
                                📎 {{ application.attachments.length }}
                                {{
                                    application.attachments.length === 1
                                        ? "вложение"
                                        : "вложений"
                                }}
                            </span>
                        </div>
                    </div>

                    <!-- Комментарий администратора -->
                    <div v-if="application.admin_notes" class="admin-notes">
                        <strong>Комментарий:</strong>
                        {{ application.admin_notes }}
                    </div>
                </div>
            </div>

            <!-- Пагинация -->
            <div v-if="applications.links.length > 3" class="pagination">
                <Link
                    v-for="link in applications.links"
                    :key="link.label"
                    :href="link.url"
                    class="pagination-link"
                    :class="{
                        active: link.active,
                        disabled: !link.url,
                    }"
                    v-html="link.label"
                />
            </div>
        </div>

        <!-- Модальное окно -->
        <ApplicationModal
            :show="showModal"
            :mode="modalMode"
            :application="selectedApplication"
            :can-review="canReviewApplications"
            @close="closeModal"
            @submitted="onApplicationSubmitted"
            @reviewed="onApplicationReviewed"
        />
    </DashboardLayout>
</template>

<style lang="scss" scoped>
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    margin-bottom: 32px;

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}

.header-content {
    flex: 1;
}

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px;
}

.page-subtitle {
    color: var(--text-secondary);
    margin: 0;
}

.applications-page {
    max-width: 1200px;
    margin: 0 auto;
}

.filters-section {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
    margin-bottom: 24px;
}

.search-bar {
    display: flex;
    gap: 16px;
    align-items: center;

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
    }
}

.search-input-wrapper {
    position: relative;
    flex: 1;
}

.search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }
}

.filter-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover,
    &.active {
        border-color: var(--primary-color);
        color: var(--primary-color);
    }
}

.filters-panel {
    display: flex;
    gap: 16px;
    align-items: end;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
    }
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 150px;
}

.filter-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: var(--bg-primary);

    &:focus {
        outline: none;
        border-color: var(--primary-color);
    }
}

.filter-actions {
    display: flex;
    gap: 8px;
}

.applications-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.empty-state {
    text-align: center;
    padding: 80px 20px;
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
}

.empty-icon {
    color: var(--text-secondary);
    margin-bottom: 16px;
}

.empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px;
}

.empty-text {
    color: var(--text-secondary);
    margin: 0 0 24px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.application-card {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
    transition: all 0.2s ease;

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
}

.card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 16px;
    margin-bottom: 16px;
}

.application-info {
    flex: 1;
    min-width: 0;
}

.application-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px;
    line-height: 1.4;
}

.application-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.meta-icon {
    flex-shrink: 0;
}

.type-badge {
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;

    &.warning {
        background: rgba(255, 193, 7, 0.1);
        color: #856404;
    }

    &.success {
        background: rgba(40, 167, 69, 0.1);
        color: #155724;
    }

    &.danger {
        background: rgba(220, 53, 69, 0.1);
        color: #721c24;
    }
}

.status-icon {
    flex-shrink: 0;
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.05);
        color: var(--text-primary);
    }
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 8px;
    min-width: 160px;
    z-index: 10;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 8px 12px;
    border: none;
    background: transparent;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.05);
    }

    &.danger {
        color: var(--danger-color);

        &:hover {
            background: rgba(220, 53, 69, 0.1);
        }
    }
}

.card-content {
    margin-bottom: 16px;
}

.application-description {
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0 0 12px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.attachments-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.attachments-count {
    font-size: 0.75rem;
    color: var(--text-secondary);
    background: rgba(0, 0, 0, 0.05);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
}

.admin-notes {
    background: rgba(var(--primary-rgb), 0.05);
    border: 1px solid rgba(var(--primary-rgb), 0.1);
    border-radius: var(--radius-md);
    padding: 12px;
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-top: 16px;
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 32px;
}

.pagination-link {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.2s ease;

    &:hover:not(.disabled) {
        background: rgba(0, 0, 0, 0.05);
    }

    &.active {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    text-decoration: none;

    &.btn-sm {
        padding: 8px 16px;
        font-size: 0.875rem;
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.btn-primary {
    background: var(--primary-color);
    color: white;

    &:hover:not(:disabled) {
        background: var(--primary-hover);
    }
}

.btn-outline {
    background: transparent;
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: var(--text-primary);

    &:hover:not(:disabled) {
        background: rgba(0, 0, 0, 0.05);
    }
}

.btn-icon {
    flex-shrink: 0;
}

// Transitions
.filters-enter-active,
.filters-leave-active {
    transition: all 0.3s ease;
}

.filters-enter-from,
.filters-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .card-actions {
        align-self: stretch;
        justify-content: space-between;
    }

    .application-meta {
        gap: 12px;
    }

    .dropdown-menu {
        right: auto;
        left: 0;
    }
}
</style>
