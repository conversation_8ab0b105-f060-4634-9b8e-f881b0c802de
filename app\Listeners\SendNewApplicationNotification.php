<?php

namespace App\Listeners;

use App\Events\ApplicationCreated;
use App\Models\User;
use App\Notifications\NewApplicationReceived;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendNewApplicationNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ApplicationCreated $event): void
    {
        // Получаем всех админов и менеджеров
        $adminsAndManagers = User::whereIn('role', ['admin', 'manager'])->get();

        // Отправляем уведомление каждому админу и менеджеру
        foreach ($adminsAndManagers as $user) {
            $user->notify(new NewApplicationReceived($event->application));
        }
    }
}
