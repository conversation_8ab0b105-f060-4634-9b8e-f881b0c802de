<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Artist;
use App\Models\Release;
use App\Models\Track;
use App\Models\Application;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class AnalyticsTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Создаем тестовых пользователей-исполнителей
        $artists = [];
        for ($i = 1; $i <= 5; $i++) {
            $user = User::create([
                'name' => "Artist $i",
                'email' => "artist$<EMAIL>",
                'password' => bcrypt('password'),
                'role' => 'artist',
                'email_verified_at' => now(),
            ]);

            $artist = Artist::create([
                'user_id' => $user->id,
                'stage_name' => "Stage Name $i",
                'bio' => "Bio for artist $i",
                'genre' => ['Electronic', 'Pop', 'Rock', 'Hip-Hop', 'Jazz'][$i - 1],
                'location' => "City $i",
            ]);

            $artists[] = $artist;
        }

        // Создаем релизы для каждого исполнителя
        foreach ($artists as $index => $artist) {
            $releasesCount = rand(1, 3);

            for ($r = 1; $r <= $releasesCount; $r++) {
                $release = Release::create([
                    'artist_id' => $artist->id,
                    'title' => "Release $r by {$artist->stage_name}",
                    'description' => "Description for release $r",
                    'type' => ['single', 'ep', 'album'][rand(0, 2)],
                    'status' => ['draft', 'pending', 'published'][rand(0, 2)],
                    'genre' => $artist->genre,
                    'language' => 'ru',
                    'is_explicit' => rand(0, 1),
                    'release_date' => Carbon::now()->subDays(rand(1, 30)),
                    'created_at' => Carbon::now()->subDays(rand(1, 30)),
                ]);

                // Создаем треки для каждого релиза
                $tracksCount = rand(1, 5);
                for ($t = 1; $t <= $tracksCount; $t++) {
                    Track::create([
                        'release_id' => $release->id,
                        'title' => "Track $t",
                        'file_path' => "tracks/track_$t.mp3",
                        'file_type' => 'audio/mpeg',
                        'duration_seconds' => rand(120, 300),
                        'track_number' => $t,
                        'is_explicit' => rand(0, 1),
                        'created_at' => $release->created_at,
                    ]);
                }
            }

            // Создаем заявки для каждого исполнителя
            $applicationsCount = rand(1, 2);
            for ($a = 1; $a <= $applicationsCount; $a++) {
                Application::create([
                    'user_id' => $artist->user_id,
                    'title' => "Application $a from {$artist->stage_name}",
                    'description' => "Description for application $a",
                    'type' => ['promo', 'collaboration', 'release', 'custom'][rand(0, 3)],
                    'status' => ['pending', 'approved', 'rejected'][rand(0, 2)],
                    'created_at' => Carbon::now()->subDays(rand(1, 30)),
                ]);
            }
        }

        $this->command->info('Analytics test data created successfully!');
    }
}
