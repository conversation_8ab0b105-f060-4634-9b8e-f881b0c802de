.main-content {
    margin-left: var(--sidebar-width);
    padding-top: var(--header-height);
    min-height: 100vh;
    transition: margin-left var(--transition-normal);
    width: 100%;

    // Когда сайдбар свернут
    .sidebar-collapsed ~ & {
        margin-left: var(--sidebar-collapsed-width);
    }

    // Контейнер для содержимого
    .content-container {
        padding: 2rem;
        max-width: 1600px;
        margin: 0 auto;
    }

    // Хлебные крошки
    .breadcrumbs {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;

        .breadcrumb-item {
            font-size: 0.875rem;
            color: var(--text-secondary);

            &:not(:last-child) {
                display: flex;
                align-items: center;

                &::after {
                    content: "/";
                    margin: 0 0.5rem;
                    color: var(--text-disabled);
                }
            }

            &:last-child {
                color: var(--text-primary);
                font-weight: 500;
            }

            a {
                color: var(--text-secondary);
                text-decoration: none;

                &:hover {
                    color: var(--primary-color);
                    text-decoration: none;
                }
            }
        }
    }

    // Заголовок страницы
    .page-header {
        margin-bottom: 2rem;

        .page-title {
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .page-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
    }

    // Сетка для дашборда
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(12, 1fr);
        gap: 1.5rem;
        margin-bottom: 2rem;

        .grid-col-12 {
            grid-column: span 12;
        }

        .grid-col-8 {
            grid-column: span 8;
        }

        .grid-col-6 {
            grid-column: span 6;
        }

        .grid-col-4 {
            grid-column: span 4;
        }

        .grid-col-3 {
            grid-column: span 3;
        }
    }

    // Секция контента
    .content-section {
        margin-bottom: 2rem;

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;

            .section-title {
                font-size: 1.25rem;
                font-weight: 600;
                color: var(--text-primary);
                margin: 0;
            }

            .section-actions {
                display: flex;
                gap: 0.5rem;
            }
        }
    }

    // Адаптивная таблица
    .table-responsive {
        width: 100%;
        overflow-x: auto;
        margin-bottom: 1.5rem;

        table {
            width: 100%;
            border-collapse: collapse;

            th,
            td {
                padding: 0.75rem 1rem;
                text-align: left;
            }

            th {
                background-color: var(--bg-secondary);
                font-weight: 500;
                color: var(--text-primary);
                font-size: 0.875rem;
                white-space: nowrap;
            }

            tr {
                border-bottom: 1px solid var(--bg-secondary);

                &:hover {
                    background-color: rgba(0, 0, 0, 0.02);
                }
            }

            td {
                font-size: 0.875rem;
                color: var(--text-primary);

                .table-actions {
                    display: flex;
                    gap: 0.5rem;
                    justify-content: flex-end;
                }
            }
        }
    }

    // Мобильная версия
    @media (max-width: 992px) {
        margin-left: 0;
    }
}

.page-container {
    padding: 24px;

    @media (max-width: 576px) {
        padding: 16px;
    }
}

// Общие стили для карточек
.card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: 24px;
    margin-bottom: 24px;

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }
    }

    .card-body {
        margin-bottom: 16px;
    }

    .card-footer {
        display: flex;
        justify-content: flex-end;
        padding-top: 16px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    @media (max-width: 576px) {
        padding: 16px;

        .card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;

            .card-actions {
                width: 100%;
                justify-content: flex-start;
            }
        }
    }
}

// Сетка для карточек
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 24px;

    @media (max-width: 576px) {
        grid-template-columns: 1fr;
        gap: 16px;
    }
}

// Анимация загрузки
.loading-spinner {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid rgba(var(--primary-color-rgb), 0.2);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

// Адаптивность
@media (max-width: 1200px) {
    .main-content {
        .dashboard-grid {
            .grid-col-3 {
                grid-column: span 6;
            }
        }
    }
}

@media (max-width: 992px) {
    .main-content {
        .dashboard-grid {
            .grid-col-4,
            .grid-col-8 {
                grid-column: span 12;
            }
        }
    }
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;

        .content-container {
            padding: 1.5rem 1rem;
        }

        .dashboard-grid {
            gap: 1rem;

            .grid-col-6 {
                grid-column: span 12;
            }
        }
    }
}
