/**
 * Этот файл загружается перед загрузкой Vue приложения
 * и включает настройки для библиотек и пользовательских
 * директив, которые нужны глобально.
 */

import axios from "axios";
import clickOutsideDirective from "./Directives/clickOutside";

// Конфигурация axios
window.axios = axios;
window.axios.defaults.headers.common["X-Requested-With"] = "XMLHttpRequest";

// CSRF токен для безопасности запросов
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    window.axios.defaults.headers.common["X-CSRF-TOKEN"] = token.content;
}

// Функция для регистрации всех директив
export function registerDirectives(app) {
    // Регистрация директивы click-outside
    app.use(clickOutsideDirective);
}

/**
 * Echo exposes an expressive API for subscribing to channels and listening
 * for events that are broadcast by Laravel. Echo and event broadcasting
 * allows your team to easily build robust real-time web applications.
 */

import Echo from "laravel-echo";

// Настройка Laravel Echo для real-time уведомлений
// Для разработки используем null driver (без real-time)
// В продакшене можно настроить Pusher или Socket.io
window.Echo = new Echo({
    broadcaster: "null",
});

// Альтернативная настройка для Pusher (когда будет настроен)
/*
import Pusher from "pusher-js";
window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: "pusher",
    key: import.meta.env.VITE_PUSHER_APP_KEY,
    cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER,
    wsHost: import.meta.env.VITE_PUSHER_HOST,
    wsPort: import.meta.env.VITE_PUSHER_PORT ?? 80,
    wssPort: import.meta.env.VITE_PUSHER_PORT ?? 443,
    forceTLS: (import.meta.env.VITE_PUSHER_SCHEME ?? "https") === "https",
    enabledTransports: ["ws", "wss"],
    authorizer: (channel, options) => {
        return {
            authorize: (socketId, callback) => {
                axios
                    .post("/api/broadcasting/auth", {
                        socket_id: socketId,
                        channel_name: channel.name,
                    })
                    .then((response) => {
                        callback(false, response.data);
                    })
                    .catch((error) => {
                        callback(true, error);
                    });
            },
        };
    },
});
*/
