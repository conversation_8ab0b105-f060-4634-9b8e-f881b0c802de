<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
        'settable_type',
        'settable_id',
    ];

    /**
     * Get the parent settable model.
     */
    public function settable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get a setting value by key.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValue(string $key, $default = null)
    {
        $setting = self::where('key', $key)
            ->whereNull('settable_type')
            ->whereNull('settable_id')
            ->first();

        return $setting ? $setting->value : $default;
    }

    /**
     * Set a global setting value.
     *
     * @param string $key
     * @param mixed $value
     * @return Setting
     */
    public static function setValue(string $key, $value): Setting
    {
        $setting = self::updateOrCreate(
            [
                'key' => $key,
                'settable_type' => null,
                'settable_id' => null
            ],
            ['value' => $value]
        );

        return $setting;
    }
}
