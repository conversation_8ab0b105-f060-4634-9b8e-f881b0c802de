<?php

use App\Http\Controllers\ApplicationController;
use App\Http\Controllers\Api\NotificationController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Broadcast;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Broadcasting авторизация
Broadcast::routes(['middleware' => ['auth:sanctum']]);

// API маршруты для заявок
Route::middleware(['auth:sanctum'])->group(function () {
    // Основные CRUD операции для заявок
    Route::apiResource('applications', ApplicationController::class);

    // Дополнительные маршруты для заявок
    Route::post('applications/{application}/review', [ApplicationController::class, 'review'])
        ->name('applications.review');

    Route::get('applications/{application}/attachments/{attachmentIndex}', [ApplicationController::class, 'downloadAttachment'])
        ->name('applications.download-attachment');

    // Маршруты для уведомлений
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [NotificationController::class, 'index'])->name('index');
        Route::post('/{notification}/read', [NotificationController::class, 'markAsRead'])->name('mark-as-read');
        Route::post('/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
        Route::delete('/{notification}', [NotificationController::class, 'destroy'])->name('destroy');
        Route::post('/test', [NotificationController::class, 'createTest'])->name('test');
    });
});
