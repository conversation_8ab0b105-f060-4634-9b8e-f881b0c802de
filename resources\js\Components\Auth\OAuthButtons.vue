<script setup>
import { computed } from 'vue';

const props = defineProps({
    providers: {
        type: Object,
        default: () => ({})
    },
    showDivider: {
        type: Boolean,
        default: true
    }
});

const enabledProviders = computed(() => {
    return Object.entries(props.providers).filter(([key, provider]) => provider.enabled);
});

const hasProviders = computed(() => {
    return enabledProviders.value.length > 0;
});

const getProviderIcon = (provider) => {
    const icons = {
        google: `<svg class="w-5 h-5" viewBox="0 0 24 24">
            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>`,
        vkontakte: `<svg class="w-5 h-5" viewBox="0 0 24 24">
            <path fill="currentColor" d="M15.684 0H8.316C1.592 0 0 1.592 0 8.316v7.368C0 22.408 1.592 24 8.316 24h7.368C22.408 24 24 22.408 24 15.684V8.316C24 1.592 22.408 0 15.684 0zm3.692 17.123h-1.744c-.66 0-.864-.525-2.05-1.727-1.033-1.01-1.49-.9-1.49.402v1.15c0 .3-.096.483-.943.483-1.946 0-4.1-1.173-5.617-3.358-2.289-3.063-2.913-5.371-2.913-5.846 0-.33.134-.477.44-.477h1.743c.33 0 .453.134.58.447.59 1.5 1.57 2.82 1.97 1.84.32-.783.32-2.54.32-2.54s.007-.8-.25-1.157c-.2-.278-.58-.36-.742-.38.15-.24.38-.43.688-.54.48-.17 1.67-.16 2.93-.16.96 0 1.23.07 **********.*********** 1.78-.04.64-.08 1.54-.08 2.19 0 .46.02 1.08.32 **********.65-.02 1.44-.83 1.04-1.08 1.78-2.42 1.78-2.42.13-.26.33-.37.66-.37h1.74c.4 0 .*********-.17.55-.8 1.27-1.45 2.05-.54.64-1.07 1.26-1.07 1.63 0 .36.4.72 1.03 ********** 1.38 1.66 1.54 **********-.09.78-.59.78z"/>
        </svg>`
    };
    return icons[provider] || '';
};

const handleOAuthLogin = (provider) => {
    window.location.href = route('oauth.redirect', provider);
};
</script>

<template>
    <div v-if="hasProviders" class="oauth-section">
        <!-- Разделитель -->
        <div v-if="showDivider" class="oauth-divider">
            <div class="divider-line"></div>
            <span class="divider-text">или</span>
            <div class="divider-line"></div>
        </div>

        <!-- OAuth кнопки -->
        <div class="oauth-buttons">
            <button
                v-for="[key, provider] in enabledProviders"
                :key="key"
                type="button"
                @click="handleOAuthLogin(key)"
                :class="[
                    'oauth-button',
                    provider.color || 'bg-gray-600 hover:bg-gray-700'
                ]"
            >
                <span 
                    class="oauth-icon"
                    v-html="getProviderIcon(key)"
                ></span>
                <span class="oauth-text">
                    Войти через {{ provider.name }}
                </span>
            </button>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.oauth-section {
    margin-top: 1.5rem;
}

.oauth-divider {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
    
    .divider-line {
        flex: 1;
        height: 1px;
        background-color: var(--border-color);
    }
    
    .divider-text {
        margin: 0 1rem;
        font-size: 0.875rem;
        color: var(--text-muted);
        font-weight: 500;
    }
}

.oauth-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.oauth-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    &:active {
        transform: translateY(0);
    }
    
    &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
}

.oauth-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    
    :deep(svg) {
        width: 1.25rem;
        height: 1.25rem;
    }
}

.oauth-text {
    font-weight: 500;
}

// Специфичные цвета для провайдеров
.oauth-button {
    &.bg-red-500 {
        background-color: #ef4444;
        
        &:hover {
            background-color: #dc2626;
        }
    }
    
    &.bg-blue-500 {
        background-color: #3b82f6;
        
        &:hover {
            background-color: #2563eb;
        }
    }
}

// Темная тема
[data-theme="dark"] {
    .oauth-button {
        &:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }
    }
}
</style>
