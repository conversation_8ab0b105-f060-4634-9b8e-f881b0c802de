<?php

namespace Tests\Feature;

use App\Models\Application;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ApplicationApiTest extends TestCase
{
    use RefreshDatabase;

    private User $artist;
    private User $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->artist = User::factory()->create(['role' => 'artist']);
        $this->admin = User::factory()->create(['role' => 'admin']);
        
        Storage::fake('public');
    }

    /** @test */
    public function artist_can_create_application()
    {
        $this->actingAs($this->artist);
        
        $data = [
            'type' => 'promo',
            'title' => 'Промо заявка',
            'description' => 'Описание промо заявки для тестирования API'
        ];
        
        $response = $this->postJson('/api/applications', $data);
        
        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'message',
                     'application' => [
                         'id',
                         'type',
                         'title',
                         'description',
                         'status',
                         'user_id'
                     ]
                 ]);
        
        $this->assertDatabaseHas('applications', [
            'user_id' => $this->artist->id,
            'type' => 'promo',
            'title' => 'Промо заявка',
            'status' => 'pending'
        ]);
    }

    /** @test */
    public function application_creation_validates_required_fields()
    {
        $this->actingAs($this->artist);
        
        $response = $this->postJson('/api/applications', []);
        
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['type', 'title', 'description']);
    }

    /** @test */
    public function application_creation_validates_type_field()
    {
        $this->actingAs($this->artist);
        
        $data = [
            'type' => 'invalid_type',
            'title' => 'Test Title',
            'description' => 'Valid description that is long enough'
        ];
        
        $response = $this->postJson('/api/applications', $data);
        
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['type']);
    }

    /** @test */
    public function artist_can_view_their_applications()
    {
        $this->actingAs($this->artist);
        
        $application = Application::factory()->create([
            'user_id' => $this->artist->id
        ]);
        
        $response = $this->getJson('/api/applications');
        
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id',
                             'type',
                             'title',
                             'description',
                             'status'
                         ]
                     ]
                 ]);
    }

    /** @test */
    public function artist_can_update_pending_application()
    {
        $this->actingAs($this->artist);
        
        $application = Application::factory()->create([
            'user_id' => $this->artist->id,
            'status' => 'pending'
        ]);
        
        $updateData = [
            'title' => 'Обновленный заголовок',
            'description' => 'Обновленное описание заявки для тестирования'
        ];
        
        $response = $this->putJson("/api/applications/{$application->id}", $updateData);
        
        $response->assertStatus(200);
        
        $this->assertDatabaseHas('applications', [
            'id' => $application->id,
            'title' => 'Обновленный заголовок'
        ]);
    }

    /** @test */
    public function artist_cannot_update_reviewed_application()
    {
        $this->actingAs($this->artist);
        
        $application = Application::factory()->create([
            'user_id' => $this->artist->id,
            'status' => 'approved'
        ]);
        
        $updateData = [
            'title' => 'Попытка обновления'
        ];
        
        $response = $this->putJson("/api/applications/{$application->id}", $updateData);
        
        $response->assertStatus(403);
    }

    /** @test */
    public function admin_can_review_application()
    {
        $this->actingAs($this->admin);
        
        $application = Application::factory()->create([
            'status' => 'pending'
        ]);
        
        $reviewData = [
            'status' => 'approved',
            'admin_notes' => 'Заявка одобрена'
        ];
        
        $response = $this->postJson("/api/applications/{$application->id}/review", $reviewData);
        
        $response->assertStatus(200);
        
        $this->assertDatabaseHas('applications', [
            'id' => $application->id,
            'status' => 'approved',
            'admin_notes' => 'Заявка одобрена',
            'reviewed_by' => $this->admin->id
        ]);
    }

    /** @test */
    public function artist_cannot_review_application()
    {
        $this->actingAs($this->artist);
        
        $application = Application::factory()->create([
            'status' => 'pending'
        ]);
        
        $reviewData = [
            'status' => 'approved'
        ];
        
        $response = $this->postJson("/api/applications/{$application->id}/review", $reviewData);
        
        $response->assertStatus(403);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_applications()
    {
        $response = $this->getJson('/api/applications');
        
        $response->assertStatus(401);
    }

    /** @test */
    public function artist_can_create_application_with_attachments()
    {
        $this->actingAs($this->artist);
        
        $file = UploadedFile::fake()->create('document.pdf', 1024);
        
        $data = [
            'type' => 'promo',
            'title' => 'Заявка с файлом',
            'description' => 'Описание заявки с прикрепленным файлом',
            'attachments' => [$file]
        ];
        
        $response = $this->postJson('/api/applications', $data);
        
        $response->assertStatus(201);
        
        $application = Application::latest()->first();
        $this->assertNotNull($application->attachments);
        $this->assertCount(1, $application->attachments);
        $this->assertEquals('document.pdf', $application->attachments[0]['original_name']);
    }
}
