<?php

namespace App\Http\Requests;

use App\Models\Application;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateApplicationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $application = $this->route('application');
        
        // Проверяем, что заявка существует и принадлежит текущему пользователю
        if (!$application instanceof Application) {
            return false;
        }

        // Только владелец заявки может её редактировать
        // И только если заявка ещё не рассмотрена
        return $application->user_id === auth()->id() && 
               $application->status === 'pending';
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => [
                'sometimes',
                'required',
                'string',
                Rule::in(['promo', 'collaboration', 'release', 'custom'])
            ],
            'title' => [
                'sometimes',
                'required',
                'string',
                'min:3',
                'max:255'
            ],
            'description' => [
                'sometimes',
                'required',
                'string',
                'min:10',
                'max:5000'
            ],
            'attachments' => [
                'sometimes',
                'nullable',
                'array',
                'max:10'
            ],
            'attachments.*' => [
                'file',
                'max:10240',
                'mimes:pdf,doc,docx,txt,jpg,jpeg,png,gif,mp3,wav,flac,zip,rar'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'type.required' => 'Тип заявки обязателен для заполнения.',
            'type.in' => 'Выбранный тип заявки недопустим.',
            'title.required' => 'Заголовок заявки обязателен для заполнения.',
            'title.min' => 'Заголовок должен содержать минимум :min символов.',
            'title.max' => 'Заголовок не может превышать :max символов.',
            'description.required' => 'Описание заявки обязательно для заполнения.',
            'description.min' => 'Описание должно содержать минимум :min символов.',
            'description.max' => 'Описание не может превышать :max символов.',
            'attachments.max' => 'Можно прикрепить максимум :max файлов.',
            'attachments.*.file' => 'Каждое вложение должно быть файлом.',
            'attachments.*.max' => 'Размер файла не должен превышать 10MB.',
            'attachments.*.mimes' => 'Недопустимый формат файла. Разрешены: PDF, DOC, DOCX, TXT, JPG, JPEG, PNG, GIF, MP3, WAV, FLAC, ZIP, RAR.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'type' => 'тип заявки',
            'title' => 'заголовок',
            'description' => 'описание',
            'attachments' => 'вложения'
        ];
    }
}
