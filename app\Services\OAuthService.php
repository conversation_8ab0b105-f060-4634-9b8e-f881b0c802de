<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class OAuthService
{
    /**
     * Get available OAuth providers.
     */
    public static function getAvailableProviders(): array
    {
        return [
            'google' => [
                'name' => 'Google',
                'icon' => 'google',
                'color' => 'bg-red-500 hover:bg-red-600',
                'enabled' => config('services.google.client_id') && config('services.google.client_secret'),
            ],
            'vkontakte' => [
                'name' => 'ВКонтакте',
                'icon' => 'vk',
                'color' => 'bg-blue-500 hover:bg-blue-600',
                'enabled' => config('services.vkontakte.client_id') && config('services.vkontakte.client_secret'),
            ],
        ];
    }

    /**
     * Get enabled OAuth providers.
     */
    public static function getEnabledProviders(): array
    {
        return array_filter(self::getAvailableProviders(), fn($provider) => $provider['enabled']);
    }

    /**
     * Check if OAuth is properly configured.
     */
    public static function isConfigured(): bool
    {
        return count(self::getEnabledProviders()) > 0;
    }

    /**
     * Find user by OAuth provider data.
     */
    public static function findUserByProvider(string $provider, string $providerId): ?User
    {
        return User::where('provider', $provider)
                   ->where('provider_id', $providerId)
                   ->first();
    }

    /**
     * Find user by email.
     */
    public static function findUserByEmail(string $email): ?User
    {
        return User::where('email', $email)->first();
    }

    /**
     * Create user from OAuth data.
     */
    public static function createUserFromOAuth(string $provider, $socialUser): User
    {
        $userData = [
            'name' => self::extractName($socialUser),
            'email' => $socialUser->getEmail(),
            'provider' => $provider,
            'provider_id' => $socialUser->getId(),
            'provider_token' => $socialUser->token,
            'provider_refresh_token' => $socialUser->refreshToken,
            'provider_data' => [
                'avatar' => $socialUser->getAvatar(),
                'nickname' => $socialUser->getNickname(),
                'raw' => $socialUser->getRaw(),
            ],
            'email_verified_at' => now(),
            'role' => 'artist',
            'password' => null,
        ];

        $user = User::create($userData);

        // Загружаем аватар асинхронно
        if ($socialUser->getAvatar()) {
            self::downloadAvatar($user, $socialUser->getAvatar());
        }

        return $user;
    }

    /**
     * Update existing user with OAuth data.
     */
    public static function updateUserOAuth(User $user, string $provider, $socialUser): User
    {
        $user->update([
            'provider' => $provider,
            'provider_id' => $socialUser->getId(),
            'provider_token' => $socialUser->token,
            'provider_refresh_token' => $socialUser->refreshToken,
            'provider_data' => [
                'avatar' => $socialUser->getAvatar(),
                'nickname' => $socialUser->getNickname(),
                'raw' => $socialUser->getRaw(),
            ],
            'email_verified_at' => $user->email_verified_at ?? now(),
        ]);

        // Обновляем аватар, если его нет
        if (!$user->avatar && $socialUser->getAvatar()) {
            self::downloadAvatar($user, $socialUser->getAvatar());
        }

        return $user;
    }

    /**
     * Extract name from social user data.
     */
    private static function extractName($socialUser): string
    {
        $name = $socialUser->getName();
        
        if (empty($name)) {
            $name = $socialUser->getNickname();
        }
        
        if (empty($name)) {
            $name = 'Пользователь';
        }

        return $name;
    }

    /**
     * Download and save user avatar from URL.
     */
    private static function downloadAvatar(User $user, string $avatarUrl): void
    {
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'Madd Label App/1.0'
                ]
            ]);

            $avatarContent = file_get_contents($avatarUrl, false, $context);
            
            if ($avatarContent) {
                $extension = self::getImageExtension($avatarContent);
                $filename = 'avatars/' . $user->id . '_' . Str::random(10) . '.' . $extension;
                
                Storage::disk('public')->put($filename, $avatarContent);
                $user->update(['avatar' => $filename]);
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to download OAuth avatar', [
                'user_id' => $user->id,
                'avatar_url' => $avatarUrl,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Determine image extension from content.
     */
    private static function getImageExtension(string $content): string
    {
        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($content);

        return match($mimeType) {
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            default => 'jpg',
        };
    }

    /**
     * Validate OAuth provider.
     */
    public static function isValidProvider(string $provider): bool
    {
        $enabledProviders = array_keys(self::getEnabledProviders());
        return in_array($provider, $enabledProviders);
    }

    /**
     * Get provider display name.
     */
    public static function getProviderDisplayName(string $provider): string
    {
        $providers = self::getAvailableProviders();
        return $providers[$provider]['name'] ?? ucfirst($provider);
    }

    /**
     * Check if user can unlink OAuth provider.
     */
    public static function canUnlinkProvider(User $user): bool
    {
        // Пользователь может отвязать OAuth только если у него есть пароль
        return $user->hasPassword();
    }

    /**
     * Unlink OAuth provider from user.
     */
    public static function unlinkProvider(User $user): void
    {
        $user->update([
            'provider' => null,
            'provider_id' => null,
            'provider_token' => null,
            'provider_refresh_token' => null,
            'provider_data' => null,
        ]);
    }
}
