<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// Канал для личных уведомлений пользователя
Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Канал для уведомлений администраторов и менеджеров
Broadcast::channel('admin.applications', function ($user) {
    return in_array($user->role, ['admin', 'manager']);
});

// Канал для уведомлений конкретного пользователя
Broadcast::channel('user.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// Канал для общих уведомлений администраторов
Broadcast::channel('admin.notifications', function ($user) {
    return in_array($user->role, ['admin', 'manager']);
});
