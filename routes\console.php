<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use App\Console\Commands\StartQueueWorkers;
use App\Console\Commands\MonitorQueues;
use App\Console\Commands\CleanupTempFiles;
use App\Services\FileManagementService;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Регистрация команд для управления очередями
Artisan::command('queue:start-workers {--stop : Stop all queue workers}', function () {
    $command = new StartQueueWorkers();
    $command->setLaravel($this->getLaravel());
    return $command->handle();
})->purpose('Start or stop Redis queue workers for different queues');

Artisan::command('queue:monitor {--refresh=5 : Refresh interval in seconds}', function () {
    $command = new MonitorQueues();
    $command->setLaravel($this->getLaravel());
    return $command->handle();
})->purpose('Monitor Redis queue status and statistics');

Artisan::command('files:cleanup-temp {--hours=24 : Files older than this many hours will be deleted}', function () {
    $command = new CleanupTempFiles();
    $command->setLaravel($this->getLaravel());
    return $command->handle(app(FileManagementService::class));
})->purpose('Clean up old temporary files');
