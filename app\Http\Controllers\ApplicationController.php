<?php

namespace App\Http\Controllers;

use App\Events\ApplicationCreated;
use App\Events\ApplicationStatusUpdated;
use App\Http\Requests\IndexApplicationRequest;
use App\Http\Requests\ReviewApplicationRequest;
use App\Http\Requests\StoreApplicationRequest;
use App\Http\Requests\UpdateApplicationRequest;
use App\Models\Application;
use App\Services\FileManagementService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class ApplicationController extends Controller
{
    /**
     * Display a listing of applications.
     */
    public function index(IndexApplicationRequest $request): Response|JsonResponse
    {
        $filters = $request->validatedWithDefaults();
        $user = auth()->user();
        
        $query = Application::with(['user', 'reviewer']);
        
        // Ограничения доступа в зависимости от роли
        if (!$user->isAdmin() && !$user->isManager()) {
            // Обычные пользователи видят только свои заявки
            $query->where('user_id', $user->id);
        }
        
        // Применяем фильтры
        if ($filters['status'] !== 'all') {
            $query->where('status', $filters['status']);
        }
        
        if ($filters['type'] !== 'all') {
            $query->where('type', $filters['type']);
        }
        
        if (isset($filters['user_id']) && ($user->isAdmin() || $user->isManager())) {
            $query->where('user_id', $filters['user_id']);
        }
        
        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('title', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }
        
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }
        
        // Сортировка
        $query->orderBy($filters['sort_by'], $filters['sort_direction']);
        
        $applications = $query->paginate($filters['per_page']);
        
        if ($request->wantsJson()) {
            return response()->json($applications);
        }
        
        return Inertia::render('Applications/Index', [
            'applications' => $applications,
            'filters' => $filters
        ]);
    }

    /**
     * Show the form for creating a new application.
     */
    public function create(): Response
    {
        return Inertia::render('Applications/Create');
    }

    /**
     * Store a newly created application.
     */
    public function store(StoreApplicationRequest $request, FileManagementService $fileService): JsonResponse
    {
        $validated = $request->validatedWithDefaults();

        // Обработка файлов вложений через FileManagementService
        if ($request->hasFile('attachments')) {
            $attachments = [];
            $files = $request->file('attachments');

            foreach ($files as $file) {
                // Валидация файла
                if (!$fileService->validateFile(
                    $file,
                    $fileService->getAllowedFileTypes('attachment'),
                    $fileService->getMaxFileSize('attachment')
                )) {
                    return response()->json([
                        'message' => 'Один или несколько файлов не прошли валидацию.',
                        'errors' => ['attachments' => ['Неподдерживаемый тип файла или размер превышает лимит.']]
                    ], 422);
                }

                // Асинхронная загрузка файла
                $tempPath = $fileService->uploadFileAsync($file, 'applications', [
                    'application_type' => $validated['type'],
                    'user_id' => auth()->id()
                ]);

                $attachments[] = [
                    'original_name' => $file->getClientOriginalName(),
                    'path' => $tempPath, // Временный путь, будет обновлен после обработки
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType()
                ];
            }
            $validated['attachments'] = $attachments;
        }

        $application = Application::create($validated);
        $application->load(['user', 'reviewer']);

        // Отправляем событие о создании заявки
        event(new ApplicationCreated($application));

        return response()->json([
            'message' => 'Заявка успешно создана.',
            'application' => $application
        ], 201);
    }

    /**
     * Display the specified application.
     */
    public function show(Application $application): Response|JsonResponse
    {
        $user = auth()->user();
        
        // Проверка доступа
        if (!$user->isAdmin() && !$user->isManager() && $application->user_id !== $user->id) {
            abort(403, 'У вас нет доступа к этой заявке.');
        }
        
        $application->load(['user', 'reviewer']);
        
        if (request()->wantsJson()) {
            return response()->json($application);
        }
        
        return Inertia::render('Applications/Show', [
            'application' => $application
        ]);
    }

    /**
     * Show the form for editing the specified application.
     */
    public function edit(Application $application): Response
    {
        $user = auth()->user();
        
        // Проверка доступа
        if ($application->user_id !== $user->id || $application->status !== 'pending') {
            abort(403, 'Вы не можете редактировать эту заявку.');
        }
        
        return Inertia::render('Applications/Edit', [
            'application' => $application
        ]);
    }

    /**
     * Update the specified application.
     */
    public function update(UpdateApplicationRequest $request, Application $application): JsonResponse
    {
        $validated = $request->validated();
        
        // Обработка новых файлов вложений
        if ($request->hasFile('attachments')) {
            // Удаляем старые файлы
            if ($application->attachments) {
                foreach ($application->attachments as $attachment) {
                    Storage::disk('public')->delete($attachment['path']);
                }
            }
            
            $attachments = [];
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('applications/attachments', 'public');
                $attachments[] = [
                    'original_name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType()
                ];
            }
            $validated['attachments'] = $attachments;
        }
        
        $application->update($validated);
        $application->load(['user', 'reviewer']);
        
        return response()->json([
            'message' => 'Заявка успешно обновлена.',
            'application' => $application
        ]);
    }

    /**
     * Remove the specified application.
     */
    public function destroy(Application $application): JsonResponse
    {
        $user = auth()->user();
        
        // Проверка доступа
        if (!$user->isAdmin() && $application->user_id !== $user->id) {
            abort(403, 'У вас нет прав для удаления этой заявки.');
        }
        
        // Удаляем файлы вложений
        if ($application->attachments) {
            foreach ($application->attachments as $attachment) {
                Storage::disk('public')->delete($attachment['path']);
            }
        }
        
        $application->delete();
        
        return response()->json([
            'message' => 'Заявка успешно удалена.'
        ]);
    }

    /**
     * Review an application (approve or reject).
     */
    public function review(ReviewApplicationRequest $request, Application $application): JsonResponse
    {
        $validated = $request->validatedWithDefaults();
        
        // Проверяем, что заявка ещё не рассмотрена
        if ($application->status !== 'pending') {
            return response()->json([
                'message' => 'Эта заявка уже была рассмотрена.'
            ], 422);
        }

        $oldStatus = $application->status;
        $application->update($validated);
        $application->load(['user', 'reviewer']);

        // Отправляем событие об изменении статуса
        event(new ApplicationStatusUpdated($application, $oldStatus, $validated['status']));

        $statusText = $validated['status'] === 'approved' ? 'одобрена' : 'отклонена';

        return response()->json([
            'message' => "Заявка успешно {$statusText}.",
            'application' => $application
        ]);
    }

    /**
     * Download an attachment file.
     */
    public function downloadAttachment(Application $application, int $attachmentIndex): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $user = auth()->user();
        
        // Проверка доступа
        if (!$user->isAdmin() && !$user->isManager() && $application->user_id !== $user->id) {
            abort(403, 'У вас нет доступа к этому файлу.');
        }
        
        if (!$application->attachments || !isset($application->attachments[$attachmentIndex])) {
            abort(404, 'Файл не найден.');
        }
        
        $attachment = $application->attachments[$attachmentIndex];
        $filePath = storage_path('app/public/' . $attachment['path']);
        
        if (!file_exists($filePath)) {
            abort(404, 'Файл не найден на сервере.');
        }
        
        return response()->download($filePath, $attachment['original_name']);
    }
}
