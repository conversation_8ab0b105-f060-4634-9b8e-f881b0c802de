<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('analytics', function (Blueprint $table) {
            $table->id();
            $table->morphs('analyzable'); // Для релизов или треков
            $table->enum('metric_type', ['play', 'like', 'download', 'share']);
            $table->integer('count')->default(1);
            $table->string('source')->nullable(); // Платформа или источник (например, Spotify, Apple Music)
            $table->json('meta_data')->nullable(); // Любые дополнительные данные
            $table->string('ip_address', 45)->nullable();
            $table->string('user_agent')->nullable();
            $table->foreignId('user_id')->nullable()->constrained();
            $table->timestamps();

            // Индексы
            $table->index(['analyzable_type', 'analyzable_id', 'metric_type']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('analytics');
    }
};
