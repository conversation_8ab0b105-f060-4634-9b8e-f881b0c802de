<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Get user notifications.
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $notifications = Notification::forUser($user->id)
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'data' => $notification->data,
                    'is_read' => $notification->isRead(),
                    'read_at' => $notification->read_at,
                    'created_at' => $notification->created_at,
                ];
            });

        $unreadCount = Notification::forUser($user->id)
            ->unread()
            ->count();

        return response()->json([
            'notifications' => $notifications,
            'unreadCount' => $unreadCount,
        ]);
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(Request $request, Notification $notification): JsonResponse
    {
        $user = Auth::user();

        if (!$user || $notification->notifiable_id !== $user->id) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $notification->markAsRead();

        return response()->json([
            'message' => 'Уведомление отмечено как прочитанное',
            'notification' => [
                'id' => $notification->id,
                'is_read' => $notification->isRead(),
                'read_at' => $notification->read_at,
            ],
        ]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(Request $request): JsonResponse
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        Notification::forUser($user->id)
            ->unread()
            ->update([
                'read_at' => now(),
            ]);

        return response()->json([
            'message' => 'Все уведомления отмечены как прочитанные',
        ]);
    }

    /**
     * Delete notification.
     */
    public function destroy(Request $request, Notification $notification): JsonResponse
    {
        $user = Auth::user();

        if (!$user || $notification->notifiable_id !== $user->id) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $notification->delete();

        return response()->json([
            'message' => 'Уведомление удалено',
        ]);
    }

    /**
     * Create test notification (for development).
     */
    public function createTest(Request $request): JsonResponse
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $notification = Notification::createForUser(
            $user->id,
            'application_approved',
            'Заявка одобрена!',
            'Ваша заявка "Тестовая заявка" была одобрена администратором.',
            ['application_id' => 1]
        );

        return response()->json([
            'message' => 'Тестовое уведомление создано',
            'notification' => $notification,
        ]);
    }
}
