<script setup>
import { Head } from "@inertiajs/vue3";
import DashboardLayout from "../Layouts/DashboardLayout.vue";
import StatCard from "./Dashboard/Components/StatCard.vue";
import ReleasesTable from "./Dashboard/Components/ReleasesTable.vue";
import { Music, Play, Download, Users } from "lucide-vue-next";

const props = defineProps({
    stats: {
        type: Array,
        required: true,
    },
    releases: {
        type: Array,
        required: true,
    },
});

// Иконки для статистики
const getIconForStat = (title) => {
    if (title.includes("релиз")) return Music;
    if (title.includes("артист")) return Users;
    if (title.includes("трек")) return Play;
    if (title.includes("заявк")) return Download;
    return Music;
};

// Хлебные крошки для страницы
const breadcrumbs = [{ name: "Дашборд" }];
</script>

<template>
    <DashboardLayout title="Дашборд" :breadcrumbs="breadcrumbs">
        <Head title="Дашборд" />

        <!-- Приветствие -->
        <div class="page-header">
            <h1 class="page-title">Добро пожаловать в madd label!</h1>
            <p class="page-description">
                Вот обзор вашей музыкальной активности.
            </p>
        </div>

        <!-- Карточки со статистикой -->
        <div class="dashboard-grid">
            <div v-for="(stat, index) in stats" :key="index" class="grid-col-3">
                <StatCard
                    :title="stat.title"
                    :value="stat.value"
                    :icon="getIconForStat(stat.title)"
                    :change="stat.change"
                    :changeType="stat.changeType"
                />
            </div>
        </div>

        <!-- Последние релизы -->
        <div class="content-section">
            <ReleasesTable :releases="releases" />
        </div>
    </DashboardLayout>
</template>
