.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: opacity var(--transition-normal);

    &.v-enter-from,
    &.v-leave-to {
        opacity: 0;
    }

    &.v-enter-active,
    &.v-leave-active {
        transition: opacity var(--transition-normal);
    }
}

.modal {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 500px;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
    transition: transform var(--transition-normal),
        opacity var(--transition-normal);

    &.v-enter-from,
    &.v-leave-to {
        opacity: 0;
        transform: scale(0.9);
    }

    &.v-enter-active,
    &.v-leave-active {
        transition: transform var(--transition-normal),
            opacity var(--transition-normal);
    }

    // Размеры модального окна
    &.modal-sm {
        max-width: 300px;
    }

    &.modal-lg {
        max-width: 800px;
    }

    &.modal-xl {
        max-width: 1140px;
    }

    .modal-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--bg-secondary);
        display: flex;
        align-items: center;
        justify-content: space-between;

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        .close-button {
            background: none;
            border: none;
            padding: 0.25rem;
            cursor: pointer;
            color: var(--text-secondary);
            transition: color var(--transition-fast);

            &:hover {
                color: var(--text-primary);
            }
        }
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        padding: 1rem 1.5rem;
        border-top: 1px solid var(--bg-secondary);
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
    }
}

// Диалоговое окно подтверждения
.confirm-dialog {
    .modal-body {
        padding: 1.5rem;
        text-align: center;

        .icon {
            font-size: 3rem;
            margin-bottom: 1rem;

            &.icon-warning {
                color: var(--warning-color);
            }

            &.icon-danger {
                color: var(--danger-color);
            }

            &.icon-success {
                color: var(--success-color);
            }
        }

        .confirm-message {
            font-size: 1.125rem;
            margin-bottom: 0.5rem;
        }

        .confirm-description {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
        }
    }
}
