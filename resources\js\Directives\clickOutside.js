/**
 * Директива для отслеживания кликов вне указанного элемента
 * Пример использования: v-click-outside="onClickOutside"
 */
export const clickOutside = {
    mounted(el, binding) {
        el.__clickOutsideHandler__ = (event) => {
            // Проверяем, что клик был не на элементе и не на его дочерних элементах
            if (!(el === event.target || el.contains(event.target))) {
                // Вызываем функцию, переданную в директиву
                binding.value(event);
            }
        };

        // Добавляем обработчик на document
        document.addEventListener("click", el.__clickOutsideHandler__);
        document.addEventListener("touchstart", el.__clickOutsideHandler__);
    },

    beforeUnmount(el) {
        // Удаляем обработчик при уничтожении компонента
        document.removeEventListener("click", el.__clickOutsideHandler__);
        document.removeEventListener("touchstart", el.__clickOutsideHandler__);

        // Удаляем ссылку на обработчик
        delete el.__clickOutsideHandler__;
    },
};

// Регистрация директивы для использования с app.directive
export default {
    install(app) {
        app.directive("click-outside", clickOutside);
    },
};
