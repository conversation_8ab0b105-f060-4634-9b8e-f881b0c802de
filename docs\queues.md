# Система очередей Redis - Madd Label

## Обзор

Система очередей обеспечивает асинхронную обработку файлов, отправку уведомлений и другие фоновые задачи в приложении Madd Label.

## Архитектура

### Очереди

1. **default** - основная очередь для общих задач
2. **files** - обработка файлов (загрузка, удаление, создание архивов)
3. **notifications** - отправка уведомлений

### Jobs (Задачи)

#### ProcessFileUpload
Асинхронная обработка загруженных файлов:
- Перемещение из временного хранилища
- Создание миниатюр для изображений
- Извлечение метаданных из аудио файлов
- Генерация уникальных имен файлов

#### ProcessFileCleanup
Очистка файлов с возможностью отложенного выполнения:
- Удаление исходных файлов после одобрения релиза
- Очистка файлов отклоненных заявок
- Немедленная очистка временных файлов

#### CreateReleaseArchive
Создание архивов релизов:
- Упаковка всех файлов релиза в ZIP
- Добавление метаданных и README
- Сохранение информации об архиве

## Настройка

### Конфигурация Redis

В `.env` файле:
```env
QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
```

### Запуск воркеров

```bash
# Запуск всех воркеров
php artisan queue:start-workers

# Остановка всех воркеров
php artisan queue:start-workers --stop

# Мониторинг очередей
php artisan queue:monitor

# Мониторинг с интервалом 10 секунд
php artisan queue:monitor --refresh=10
```

### Планировщик задач

Для автоматической очистки добавьте в cron:
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

## Использование

### FileManagementService

```php
use App\Services\FileManagementService;

$fileService = new FileManagementService();

// Асинхронная загрузка файла
$tempPath = $fileService->uploadFileAsync($file, 'covers');

// Загрузка нескольких файлов
$tempPaths = $fileService->uploadMultipleFilesAsync($files, 'tracks');

// Планирование очистки после одобрения (через 24 часа)
$fileService->scheduleCleanupAfterApproval($filePaths, 24);

// Планирование очистки после отклонения (через 72 часа)
$fileService->scheduleCleanupAfterRejection($filePaths, 72);

// Создание архива релиза
$fileService->createReleaseArchive($releaseId, $userId);
```

### Валидация файлов

```php
// Проверка типа и размера файла
$isValid = $fileService->validateFile(
    $file, 
    $fileService->getAllowedFileTypes('cover'), 
    $fileService->getMaxFileSize('cover')
);

// Получение разрешенных типов
$coverTypes = $fileService->getAllowedFileTypes('cover');
$trackTypes = $fileService->getAllowedFileTypes('track');
$attachmentTypes = $fileService->getAllowedFileTypes('attachment');
```

## Мониторинг

### Команды мониторинга

```bash
# Статистика очередей
php artisan queue:monitor

# Просмотр неудачных задач
php artisan queue:failed

# Повтор всех неудачных задач
php artisan queue:retry all

# Повтор конкретной задачи
php artisan queue:retry {id}

# Очистка всех очередей
php artisan queue:flush

# Очистка неудачных задач
php artisan queue:flush-failed
```

### Статистика хранилища

```php
$stats = $fileService->getStorageStatistics();
// Возвращает информацию о размере и количестве файлов
```

## Обработка ошибок

### Повторные попытки

- **ProcessFileUpload**: 3 попытки, таймаут 5 минут
- **ProcessFileCleanup**: 3 попытки, таймаут 2 минуты
- **CreateReleaseArchive**: 2 попытки, таймаут 10 минут

### Логирование

Все операции логируются в `storage/logs/laravel.log`:
- Успешная обработка файлов
- Ошибки обработки
- Статистика очистки
- Создание архивов

## Производительность

### Рекомендуемые настройки воркеров

- **default**: 2 процесса
- **files**: 3 процесса (файловые операции могут быть ресурсоемкими)
- **notifications**: 2 процесса

### Оптимизация

1. Используйте SSD для временного хранилища
2. Настройте Redis с достаточным объемом памяти
3. Мониторьте использование дискового пространства
4. Регулярно очищайте старые архивы и временные файлы

## Безопасность

1. Валидация типов файлов на уровне MIME
2. Ограничения размера файлов
3. Изоляция временных файлов
4. Автоматическая очистка после обработки
5. Генерация уникальных имен файлов

## Расширение

### Добавление новых типов файлов

1. Обновите `getAllowedFileTypes()` в `FileManagementService`
2. Добавьте соответствующие ограничения размера
3. При необходимости создайте специализированные Job классы

### Добавление новых очередей

1. Добавьте конфигурацию в `config/queue.php`
2. Обновите команду `StartQueueWorkers`
3. Добавьте мониторинг в `MonitorQueues`

## Troubleshooting

### Частые проблемы

1. **Воркеры не запускаются**: Проверьте подключение к Redis
2. **Файлы не обрабатываются**: Убедитесь, что воркеры запущены
3. **Переполнение очереди**: Увеличьте количество воркеров
4. **Ошибки памяти**: Увеличьте лимит памяти PHP

### Диагностика

```bash
# Проверка подключения к Redis
redis-cli ping

# Проверка активных воркеров
ps aux | grep "queue:work"

# Проверка логов
tail -f storage/logs/laravel.log
```
