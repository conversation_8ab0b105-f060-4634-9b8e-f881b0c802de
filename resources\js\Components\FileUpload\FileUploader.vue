<script setup>
import { ref, computed, watch } from "vue";
import {
    Upload,
    X,
    File,
    Image,
    Music,
    FileText,
    Archive,
    AlertCircle,
    CheckCircle,
    Loader2,
    Eye,
    Download,
} from "lucide-vue-next";

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => [],
    },
    accept: {
        type: String,
        default: "*/*",
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    maxFiles: {
        type: Number,
        default: 10,
    },
    maxSize: {
        type: Number,
        default: 10 * 1024 * 1024, // 10MB
    },
    allowedTypes: {
        type: Array,
        default: () => [],
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    showPreview: {
        type: Boolean,
        default: true,
    },
    uploadText: {
        type: String,
        default: "Перетащите файлы сюда или нажмите для выбора",
    },
    hintText: {
        type: String,
        default: "",
    },
});

const emit = defineEmits([
    "update:modelValue",
    "filesAdded",
    "fileRemoved",
    "error",
]);

// Состояние компонента
const isDragOver = ref(false);
const fileInput = ref(null);
const files = ref([...props.modelValue]);

// Вычисляемые свойства
const canAddFiles = computed(() => {
    return !props.disabled && files.value.length < props.maxFiles;
});

const availableSlots = computed(() => {
    return props.maxFiles - files.value.length;
});

// Методы обработки файлов
function handleFileSelect(event) {
    const selectedFiles = Array.from(event.target.files);
    processFiles(selectedFiles);

    // Очищаем input для возможности повторного выбора того же файла
    if (fileInput.value) {
        fileInput.value.value = "";
    }
}

function handleDrop(event) {
    event.preventDefault();
    isDragOver.value = false;

    if (props.disabled) return;

    const droppedFiles = Array.from(event.dataTransfer.files);
    processFiles(droppedFiles);
}

function handleDragOver(event) {
    event.preventDefault();
    if (!props.disabled) {
        isDragOver.value = true;
    }
}

function handleDragLeave() {
    isDragOver.value = false;
}

function processFiles(newFiles) {
    if (!canAddFiles.value) {
        emit("error", `Можно загрузить максимум ${props.maxFiles} файлов`);
        return;
    }

    const filesToAdd = newFiles.slice(0, availableSlots.value);
    const validFiles = [];
    const errors = [];

    filesToAdd.forEach((file) => {
        const validation = validateFile(file);
        if (validation.valid) {
            const fileData = createFileData(file);
            validFiles.push(fileData);
        } else {
            errors.push(`${file.name}: ${validation.error}`);
        }
    });

    if (errors.length > 0) {
        emit("error", errors.join("; "));
    }

    if (validFiles.length > 0) {
        files.value.push(...validFiles);
        updateModelValue();
        emit("filesAdded", validFiles);
    }
}

function validateFile(file) {
    // Проверка размера
    if (file.size > props.maxSize) {
        return {
            valid: false,
            error: `размер превышает ${formatFileSize(props.maxSize)}`,
        };
    }

    // Проверка типа файла
    if (
        props.allowedTypes.length > 0 &&
        !props.allowedTypes.includes(file.type)
    ) {
        return {
            valid: false,
            error: "неподдерживаемый тип файла",
        };
    }

    return { valid: true };
}

function createFileData(file) {
    return {
        id: Date.now() + Math.random(),
        file: file,
        name: file.name,
        size: file.size,
        type: file.type,
        status: "ready", // ready, uploading, uploaded, error
        progress: 0,
        preview: null,
        error: null,
    };
}

function removeFile(fileId) {
    const index = files.value.findIndex((f) => f.id === fileId);
    if (index !== -1) {
        const removedFile = files.value.splice(index, 1)[0];
        updateModelValue();
        emit("fileRemoved", removedFile);
    }
}

function updateModelValue() {
    emit("update:modelValue", [...files.value]);
}

// Утилиты
function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function getFileIcon(type) {
    if (!type || typeof type !== "string") return File;
    if (type.startsWith("image/")) return Image;
    if (type.startsWith("audio/")) return Music;
    if (type.startsWith("text/") || type.includes("document")) return FileText;
    if (type.includes("zip") || type.includes("rar")) return Archive;
    return File;
}

function getFileTypeLabel(type) {
    if (!type || typeof type !== "string") return "FILE";

    const typeMap = {
        "image/jpeg": "JPEG",
        "image/png": "PNG",
        "image/gif": "GIF",
        "image/webp": "WebP",
        "audio/mpeg": "MP3",
        "audio/wav": "WAV",
        "audio/flac": "FLAC",
        "application/pdf": "PDF",
        "text/plain": "TXT",
        "application/zip": "ZIP",
        "application/x-rar-compressed": "RAR",
    };

    return typeMap[type] || type.split("/")[1]?.toUpperCase() || "FILE";
}

// Watchers
watch(
    () => props.modelValue,
    (newValue) => {
        files.value = [...newValue];
    },
    { deep: true }
);

// Открытие диалога выбора файлов
function openFileDialog() {
    if (!props.disabled && canAddFiles.value) {
        fileInput.value?.click();
    }
}
</script>

<template>
    <div class="file-uploader">
        <!-- Область загрузки -->
        <div
            v-if="canAddFiles"
            class="upload-area"
            :class="{
                'drag-over': isDragOver,
                disabled: disabled,
            }"
            @drop="handleDrop"
            @dragover="handleDragOver"
            @dragleave="handleDragLeave"
            @click="openFileDialog"
        >
            <div class="upload-content">
                <Upload class="upload-icon" size="48" />
                <p class="upload-text">{{ uploadText }}</p>
                <p v-if="hintText" class="upload-hint">{{ hintText }}</p>
                <p v-else class="upload-hint">
                    {{ multiple ? `До ${availableSlots} файлов` : "1 файл" }} •
                    Макс. {{ formatFileSize(maxSize) }}
                </p>
            </div>

            <input
                ref="fileInput"
                type="file"
                class="file-input"
                :accept="accept"
                :multiple="multiple"
                :disabled="disabled"
                @change="handleFileSelect"
            />
        </div>

        <!-- Список файлов -->
        <div v-if="files.length > 0 && showPreview" class="files-list">
            <div
                v-for="fileData in files"
                :key="fileData.id"
                class="file-item"
                :class="{ 'has-error': fileData.status === 'error' }"
            >
                <div class="file-info">
                    <div class="file-icon-wrapper">
                        <component
                            :is="getFileIcon(fileData.type)"
                            class="file-icon"
                            size="20"
                        />
                        <span class="file-type">{{
                            getFileTypeLabel(fileData.type)
                        }}</span>
                    </div>

                    <div class="file-details">
                        <span class="file-name" :title="fileData.name">
                            {{ fileData.name }}
                        </span>
                        <span class="file-size">{{
                            formatFileSize(fileData.size)
                        }}</span>
                    </div>
                </div>

                <!-- Статус файла -->
                <div class="file-status">
                    <div
                        v-if="fileData.status === 'uploading'"
                        class="status-uploading"
                    >
                        <Loader2 class="status-icon spin" size="16" />
                        <span class="status-text"
                            >{{ fileData.progress }}%</span
                        >
                    </div>

                    <div
                        v-else-if="fileData.status === 'uploaded'"
                        class="status-uploaded"
                    >
                        <CheckCircle class="status-icon success" size="16" />
                        <span class="status-text">Загружено</span>
                    </div>

                    <div
                        v-else-if="fileData.status === 'error'"
                        class="status-error"
                    >
                        <AlertCircle class="status-icon error" size="16" />
                        <span class="status-text">{{
                            fileData.error || "Ошибка"
                        }}</span>
                    </div>

                    <div v-else class="status-ready">
                        <span class="status-text">Готов к загрузке</span>
                    </div>
                </div>

                <!-- Действия -->
                <div class="file-actions">
                    <button
                        type="button"
                        class="action-btn remove-btn"
                        @click="removeFile(fileData.id)"
                        title="Удалить файл"
                    >
                        <X size="16" />
                    </button>
                </div>
            </div>
        </div>

        <!-- Информация о лимитах -->
        <div v-if="!canAddFiles && files.length >= maxFiles" class="limit-info">
            <AlertCircle class="limit-icon" size="16" />
            <span>Достигнут лимит файлов ({{ maxFiles }})</span>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.file-uploader {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.upload-area {
    border: 2px dashed rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-lg);
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--bg-primary);

    &:hover:not(.disabled) {
        border-color: var(--primary-color);
        background: rgba(var(--primary-rgb), 0.05);
    }

    &.drag-over {
        border-color: var(--primary-color);
        background: rgba(var(--primary-rgb), 0.1);
        transform: scale(1.02);
    }

    &.disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: var(--bg-secondary);
    }
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.upload-icon {
    color: var(--text-secondary);
    transition: color 0.2s ease;
}

.upload-area:hover:not(.disabled) .upload-icon {
    color: var(--primary-color);
}

.upload-text {
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.upload-hint {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

.file-input {
    display: none;
}

.files-list {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background: rgba(0, 0, 0, 0.02);
    }

    &.has-error {
        background: rgba(220, 53, 69, 0.05);
        border-color: rgba(220, 53, 69, 0.2);
    }
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.file-icon-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    min-width: 40px;
}

.file-icon {
    color: var(--primary-color);
}

.file-type {
    font-size: 0.625rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.file-status {
    display: flex;
    align-items: center;
    min-width: 120px;
}

.status-uploading,
.status-uploaded,
.status-error,
.status-ready {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-icon {
    flex-shrink: 0;

    &.success {
        color: var(--success-color);
    }

    &.error {
        color: var(--danger-color);
    }

    &.spin {
        animation: spin 1s linear infinite;
    }
}

.status-text {
    font-size: 0.75rem;
    font-weight: 500;
}

.status-uploading .status-text {
    color: var(--primary-color);
}

.status-uploaded .status-text {
    color: var(--success-color);
}

.status-error .status-text {
    color: var(--danger-color);
}

.status-ready .status-text {
    color: var(--text-secondary);
}

.file-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &.remove-btn {
        background: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);

        &:hover {
            background: rgba(220, 53, 69, 0.2);
        }
    }
}

.limit-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: var(--radius-md);
    color: #856404;
    font-size: 0.875rem;
}

.limit-icon {
    flex-shrink: 0;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@media (max-width: 768px) {
    .upload-area {
        padding: 30px 15px;
    }

    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .file-info {
        width: 100%;
    }

    .file-status {
        min-width: auto;
        width: 100%;
        justify-content: space-between;
    }

    .file-actions {
        width: 100%;
        justify-content: flex-end;
    }
}
</style>
