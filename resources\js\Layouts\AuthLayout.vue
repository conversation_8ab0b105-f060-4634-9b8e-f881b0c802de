<script setup>
import { Head } from "@inertiajs/vue3";

defineProps({
    title: String,
});
</script>

<template>
    <div class="auth-layout">
        <Head :title="title" />

        <!-- Левая часть с фоном и логотипом -->
        <div class="auth-banner">
            <div class="banner-content">
                <h2 class="banner-title">Платформа для артистов и лейблов</h2>
                <p class="banner-text">
                    Управляйте релизами, треками и аналитикой в одном месте.
                    Загружайте музыку, отслеживайте статистику и развивайте свой
                    бренд.
                </p>
            </div>

            <div class="banner-footer">
                <p>
                    &copy; {{ new Date().getFullYear() }} madd label. Все права
                    защищены.
                </p>
            </div>
        </div>

        <!-- Правая часть с формой -->
        <div class="auth-form-container">
            <div class="auth-form-content">
                <slot />
            </div>
        </div>
    </div>
</template>

<style lang="scss">
.auth-layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-primary);

    @media (max-width: 768px) {
        flex-direction: column;
    }
}

.auth-banner {
    flex: 1;
    background: var(--primary-color);
    color: white;
    display: flex;
    flex-direction: column;
    padding: 40px;
    position: relative;
    overflow: hidden;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url("/images/auth-bg-pattern.svg");
        opacity: 0.1;
        pointer-events: none;
    }

    @media (max-width: 768px) {
        padding: 20px;
        min-height: 300px;
    }
}

.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 1;

    @media (max-width: 768px) {
        margin-bottom: 20px;
    }
}

.auth-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 16px;

    @media (max-width: 768px) {
        width: 60px;
        height: 60px;
    }
}

.brand-name {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 8px;
    text-align: center;

    @media (max-width: 768px) {
        font-size: 1.5rem;
    }
}

.brand-slogan {
    font-size: 1rem;
    font-weight: 400;
    margin: 0;
    opacity: 0.9;
    text-align: center;
}

.banner-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.banner-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 24px;
    line-height: 1.2;
    max-width: 500px;

    @media (max-width: 768px) {
        font-size: 1.75rem;
        margin-bottom: 16px;
    }
}

.banner-text {
    font-size: 1.125rem;
    line-height: 1.6;
    margin: 0;
    max-width: 500px;
    opacity: 0.9;

    @media (max-width: 768px) {
        font-size: 1rem;
    }
}

.banner-footer {
    font-size: 0.875rem;
    opacity: 0.7;
    margin-top: 40px;
    position: relative;
    z-index: 1;

    @media (max-width: 768px) {
        margin-top: 20px;
    }
}

.auth-form-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;

    @media (max-width: 768px) {
        padding: 20px;
    }
}

.auth-form-content {
    width: 100%;
    max-width: 450px;
}
</style>
