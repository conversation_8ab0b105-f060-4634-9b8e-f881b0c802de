<script setup>
import { ref, watch, computed } from "vue";
import { Head, Link, router, usePage } from "@inertiajs/vue3";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import {
    Music,
    Plus,
    Filter,
    Search,
    ChevronDown,
    ChevronUp,
    X,
    ListFilter,
    Clock,
    Calendar,
    User,
    Disc,
    MoreVertical,
    ExternalLink,
    Edit,
    Trash2,
} from "lucide-vue-next";

const props = defineProps({
    releases: Object,
    filters: Object,
});

const user = computed(() => usePage().props.auth.user);

// Проверка, есть ли у пользователя профиль артиста
const hasArtistProfile = computed(() => {
    return user.value.artist !== null;
});

// Состояние фильтра
const showFilters = ref(false);

// Опции для фильтров
const statusOptions = [
    { value: "all", label: "Все статусы" },
    { value: "draft", label: "Черновик" },
    { value: "pending", label: "На модерации" },
    { value: "approved", label: "Одобрено" },
    { value: "published", label: "Опубликовано" },
    { value: "rejected", label: "Отклонено" },
];

const typeOptions = [
    { value: "all", label: "Все типы" },
    { value: "album", label: "Альбом" },
    { value: "ep", label: "EP" },
    { value: "single", label: "Сингл" },
];

const sortOptions = [
    {
        field: "created_at",
        direction: "desc",
        label: "Дата создания (сначала новые)",
    },
    {
        field: "created_at",
        direction: "asc",
        label: "Дата создания (сначала старые)",
    },
    {
        field: "release_date",
        direction: "desc",
        label: "Дата релиза (сначала новые)",
    },
    {
        field: "release_date",
        direction: "asc",
        label: "Дата релиза (сначала старые)",
    },
    { field: "title", direction: "asc", label: "Название (А-Я)" },
    { field: "title", direction: "desc", label: "Название (Я-А)" },
];

// Активные фильтры
const search = ref("");
const status = ref(props.filters.status || "all");
const type = ref(props.filters.type || "all");
const sort = ref({
    field: props.filters.sort || "created_at",
    direction: props.filters.direction || "desc",
});

// Хлебные крошки
const breadcrumbs = [{ name: "Релизы" }];

// Применение фильтров
function applyFilters() {
    showFilters.value = false;

    router.get(
        route("releases.index"),
        {
            search: search.value,
            status: status.value,
            type: type.value,
            sort: sort.value.field,
            direction: sort.value.direction,
        },
        {
            preserveState: true,
            replace: true,
        }
    );
}

// Сброс фильтров
function resetFilters() {
    search.value = "";
    status.value = "all";
    type.value = "all";
    sort.value = {
        field: "created_at",
        direction: "desc",
    };

    applyFilters();
}

// Применение сортировки
function applySort(field, direction) {
    sort.value = { field, direction };
    applyFilters();
}

// Вычисление текущей выбранной сортировки
function getSortLabel() {
    const option = sortOptions.find(
        (opt) =>
            opt.field === sort.value.field &&
            opt.direction === sort.value.direction
    );
    return option ? option.label : "Сортировка";
}

// Получение цвета статуса
function getStatusColor(status) {
    switch (status) {
        case "draft":
            return "var(--info-color)";
        case "pending":
            return "var(--warning-color)";
        case "approved":
            return "var(--success-color)";
        case "rejected":
            return "var(--danger-color)";
        default:
            return "var(--text-secondary)";
    }
}

// Получение метки статуса
function getStatusLabel(status) {
    switch (status) {
        case "draft":
            return "Черновик";
        case "pending":
            return "На модерации";
        case "approved":
            return "Одобрен";
        case "published":
            return "Опубликован";
        case "rejected":
            return "Отклонен";
        default:
            return "Неизвестный";
    }
}

// Получение иконки для типа релиза
function getReleaseTypeIcon(type) {
    switch (type) {
        case "album":
            return Disc;
        case "ep":
        case "single":
            return Music;
        default:
            return Music;
    }
}

// Получение метки для типа релиза
function getReleaseTypeLabel(type) {
    switch (type) {
        case "album":
            return "Альбом";
        case "ep":
            return "EP";
        case "single":
            return "Сингл";
        default:
            return type;
    }
}

// Открытие меню действий
const activeReleaseId = ref(null);

function toggleReleaseMenu(releaseId, event) {
    event.stopPropagation();
    activeReleaseId.value =
        activeReleaseId.value === releaseId ? null : releaseId;
}

// Закрытие меню при клике вне
function closeMenu(event) {
    if (activeReleaseId.value !== null) {
        activeReleaseId.value = null;
    }
}

// Форматирование даты
function formatDate(dateString) {
    const options = { day: "2-digit", month: "2-digit", year: "numeric" };
    return new Date(dateString).toLocaleDateString("ru-RU", options);
}

// Обработка поискового запроса
let searchTimeout;

watch(search, (value) => {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        applyFilters();
    }, 500);
});
</script>

<template>
    <DashboardLayout title="Релизы" :breadcrumbs="breadcrumbs">
        <Head title="Релизы" />

        <div @click="closeMenu" class="content-container">
            <!-- Верхняя панель с поиском и фильтрами -->
            <div class="toolbar">
                <!-- Поиск -->
                <div class="search-input-container">
                    <Search class="search-icon" size="18" />
                    <input
                        v-model="search"
                        type="text"
                        class="search-input"
                        placeholder="Поиск релизов..."
                    />
                    <button
                        v-if="search"
                        class="clear-search-btn"
                        @click="search = ''"
                    >
                        <X size="16" />
                    </button>
                </div>

                <!-- Фильтры -->
                <div class="filter-btns">
                    <button
                        class="filter-btn"
                        :class="{ active: showFilters }"
                        @click="showFilters = !showFilters"
                    >
                        <Filter class="filter-icon" size="16" />
                        <span>Фильтры</span>
                        <ChevronDown
                            v-if="!showFilters"
                            class="chevron-icon"
                            size="16"
                        />
                        <ChevronUp v-else class="chevron-icon" size="16" />
                    </button>

                    <div class="sort-dropdown">
                        <button class="filter-btn">
                            <ListFilter class="filter-icon" size="16" />
                            <span>{{ getSortLabel() }}</span>
                            <ChevronDown class="chevron-icon" size="16" />
                        </button>

                        <div class="sort-dropdown-menu">
                            <button
                                v-for="option in sortOptions"
                                :key="`${option.field}-${option.direction}`"
                                class="sort-option"
                                :class="{
                                    active:
                                        sort.field === option.field &&
                                        sort.direction === option.direction,
                                }"
                                @click="
                                    applySort(option.field, option.direction)
                                "
                            >
                                {{ option.label }}
                            </button>
                        </div>
                    </div>

                    <!-- Для пользователей с профилем артиста -->
                    <Link
                        :href="route('releases.create')"
                        class="create-btn"
                        v-if="hasArtistProfile"
                    >
                        <Plus class="btn-icon" size="18" />
                        <span>Создать релиз</span>
                    </Link>

                    <!-- Для пользователей без профиля артиста -->
                    <Link
                        :href="route('profile.show')"
                        class="create-btn warning-btn"
                        v-else
                    >
                        <User class="btn-icon" size="18" />
                        <span>Создать профиль артиста</span>
                    </Link>
                </div>
            </div>

            <!-- Панель фильтров -->
            <div class="filter-panel" v-if="showFilters">
                <div class="filter-grid">
                    <!-- Фильтр по статусу -->
                    <div class="filter-group">
                        <label class="filter-label">Статус</label>
                        <select v-model="status" class="filter-select">
                            <option
                                v-for="option in statusOptions"
                                :key="option.value"
                                :value="option.value"
                            >
                                {{ option.label }}
                            </option>
                        </select>
                    </div>

                    <!-- Фильтр по типу релиза -->
                    <div class="filter-group">
                        <label class="filter-label">Тип релиза</label>
                        <select v-model="type" class="filter-select">
                            <option
                                v-for="option in typeOptions"
                                :key="option.value"
                                :value="option.value"
                            >
                                {{ option.label }}
                            </option>
                        </select>
                    </div>
                </div>

                <div class="filter-actions">
                    <button class="action-btn" @click="resetFilters">
                        Сбросить
                    </button>
                    <button class="action-btn primary" @click="applyFilters">
                        Применить
                    </button>
                </div>
            </div>

            <!-- Список релизов -->
            <div v-if="props.releases.data.length > 0" class="releases-grid">
                <div
                    v-for="release in props.releases.data"
                    :key="release.id"
                    class="release-card"
                >
                    <Link
                        :href="route('releases.show', release.id)"
                        class="release-link"
                    >
                        <!-- Обложка релиза -->
                        <div class="release-cover">
                            <img
                                :src="release.cover_image_url"
                                :alt="release.title"
                                class="cover-image"
                            />
                        </div>

                        <!-- Информация о релизе -->
                        <div class="release-info">
                            <h3 class="release-title">{{ release.title }}</h3>
                            <p class="release-artist">
                                {{ release.artist.display_name }}
                            </p>

                            <!-- Метаданные релиза -->
                            <div class="release-meta">
                                <div class="meta-item">
                                    <Clock class="meta-icon" size="14" />
                                    <span>{{
                                        formatDate(release.created_at)
                                    }}</span>
                                </div>

                                <div
                                    v-if="release.release_date"
                                    class="meta-item"
                                >
                                    <Calendar class="meta-icon" size="14" />
                                    <span>{{
                                        formatDate(release.release_date)
                                    }}</span>
                                </div>
                            </div>

                            <!-- Метка типа релиза и статус -->
                            <div class="release-badges">
                                <span class="release-type-badge">
                                    <component
                                        :is="getReleaseTypeIcon(release.type)"
                                        class="badge-icon"
                                        size="14"
                                    />
                                    <span>{{
                                        getReleaseTypeLabel(release.type)
                                    }}</span>
                                </span>

                                <span
                                    class="release-status-badge"
                                    :style="{
                                        backgroundColor: getStatusColor(
                                            release.status
                                        ),
                                    }"
                                >
                                    {{ getStatusLabel(release.status) }}
                                </span>
                            </div>

                            <!-- Кнопка действий -->
                            <button
                                class="release-actions-btn"
                                @click="toggleReleaseMenu(release.id, $event)"
                            >
                                <MoreVertical size="20" />
                            </button>

                            <!-- Выпадающее меню действий -->
                            <div
                                v-if="activeReleaseId === release.id"
                                class="release-actions-menu"
                            >
                                <Link
                                    :href="route('releases.show', release.id)"
                                    class="menu-item"
                                >
                                    <ExternalLink size="14" class="menu-icon" />
                                    <span>Просмотр</span>
                                </Link>
                                <Link
                                    v-if="
                                        release.status === 'draft' ||
                                        user.role === 'admin' ||
                                        user.role === 'manager'
                                    "
                                    :href="route('releases.edit', release.id)"
                                    class="menu-item"
                                >
                                    <Edit size="14" class="menu-icon" />
                                    <span>Редактировать</span>
                                </Link>
                                <button
                                    v-if="
                                        release.status === 'draft' ||
                                        user.role === 'admin' ||
                                        user.role === 'manager'
                                    "
                                    class="menu-item delete"
                                    @click.prevent="
                                        confirmDeleteRelease(release)
                                    "
                                >
                                    <Trash2 size="14" class="menu-icon" />
                                    <span>Удалить</span>
                                </button>
                            </div>
                        </div>
                    </Link>
                </div>
            </div>

            <!-- Сообщение, если нет релизов -->
            <div v-else class="no-data-message">
                <h3>Релизов не найдено</h3>
                <p>
                    На данный момент нет релизов, соответствующих вашим
                    критериям. Попробуйте изменить фильтры или создайте свой
                    первый релиз.
                </p>

                <!-- Для пользователей с профилем артиста -->
                <Link
                    :href="route('releases.create')"
                    class="create-btn"
                    v-if="hasArtistProfile"
                >
                    <Plus class="btn-icon" size="18" />
                    <span>Создать релиз</span>
                </Link>

                <!-- Для пользователей без профиля артиста -->
                <Link
                    :href="route('profile.show')"
                    class="create-btn warning-btn"
                    v-else
                >
                    <User class="btn-icon" size="18" />
                    <span>Создать профиль артиста</span>
                </Link>
            </div>

            <!-- Пагинация -->
            <div v-if="props.releases.data.length > 0" class="pagination">
                <!-- Тут можно добавить компонент пагинации -->
            </div>
        </div>
    </DashboardLayout>
</template>

<style lang="scss">
.content-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    gap: 16px;
    flex-wrap: wrap;
}

.search-input-container {
    position: relative;
    flex-grow: 1;
    max-width: 400px;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-input {
    width: 100%;
    padding: 10px 36px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-primary);
    background-color: var(--bg-primary);

    &:focus {
        outline: none;
        border-color: var(--primary-color);
    }
}

.clear-search-btn {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;

    &:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }
}

.filter-btns {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-btn,
.create-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    white-space: nowrap;
}

.filter-btn {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);

    &:hover,
    &.active {
        background-color: rgba(var(--primary-color-rgb), 0.05);
    }

    .filter-icon,
    .chevron-icon {
        color: var(--text-secondary);
    }
}

.create-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    text-decoration: none;

    &:hover {
        background-color: var(--secondary-color);
    }
}

.sort-dropdown {
    position: relative;

    .sort-dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        z-index: 100;
        width: 100%;
        min-width: 240px;
        background-color: var(--bg-primary);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-md);
        padding: 8px 0;
        margin-top: 8px;
        display: none;

        &::before {
            content: "";
            position: absolute;
            top: -4px;
            right: 16px;
            width: 8px;
            height: 8px;
            background-color: var(--bg-primary);
            transform: rotate(45deg);
        }
    }

    &:hover .sort-dropdown-menu {
        display: block;
    }
}

.sort-option {
    display: block;
    width: 100%;
    text-align: left;
    padding: 8px 16px;
    border: none;
    background: none;
    font-size: 0.875rem;
    color: var(--text-primary);
    cursor: pointer;

    &:hover {
        background-color: var(--bg-secondary);
    }

    &.active {
        background-color: rgba(var(--primary-color-rgb), 0.05);
        color: var(--primary-color);
        font-weight: 500;
    }
}

.filter-panel {
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    padding: 16px;
    margin-bottom: 24px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.2s ease-out;
}

.filter-grid {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
    margin-bottom: 16px;
}

.filter-group {
    flex-grow: 1;
    min-width: 200px;
}

.filter-label {
    display: block;
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-secondary);
}

.filter-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    font-size: 0.875rem;
    color: var(--text-primary);

    &:focus {
        outline: none;
        border-color: var(--primary-color);
    }
}

.filter-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.action-btn {
    padding: 8px 16px;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    border: none;
}

.action-btn-primary {
    background-color: var(--primary-color);
    color: white;

    &:hover {
        background-color: var(--secondary-color);
    }
}

.releases-container {
    padding-bottom: 40px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px 16px;
    text-align: center;

    .empty-icon {
        color: var(--text-disabled);
        margin-bottom: 16px;
    }

    .empty-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px;
    }

    .empty-text {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin: 0 0 24px;
        max-width: 400px;
    }
}

.releases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 24px;
}

.release-card {
    border-radius: var(--radius-md);
    overflow: hidden;
    background-color: var(--bg-primary);
    box-shadow: var(--shadow-sm);
    transition: transform 0.2s, box-shadow 0.2s;

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-md);
    }
}

.release-cover-wrapper {
    position: relative;
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
}

.release-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.release-cover-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-secondary);
    color: var(--text-disabled);
}

.release-badges {
    position: absolute;
    top: 12px;
    left: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    pointer-events: none;
}

.release-type-badge {
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;

    .badge-icon {
        opacity: 0.8;
    }
}

.release-status-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
}

.release-actions-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.4);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &:hover {
        background-color: rgba(0, 0, 0, 0.6);
    }
}

.release-actions-menu {
    position: absolute;
    top: 52px;
    right: 12px;
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    min-width: 180px;
    z-index: 10;

    &::before {
        content: "";
        position: absolute;
        top: -4px;
        right: 16px;
        width: 8px;
        height: 8px;
        background-color: var(--bg-primary);
        transform: rotate(45deg);
    }

    .menu-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 10px 16px;
        font-size: 0.875rem;
        color: var(--text-primary);
        text-decoration: none;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        cursor: pointer;

        &:hover {
            background-color: var(--bg-secondary);
        }

        &.delete {
            color: var(--danger-color);

            .menu-icon {
                color: var(--danger-color);
            }
        }

        .menu-icon {
            color: var(--text-secondary);
            flex-shrink: 0;
        }
    }
}

.release-info {
    padding: 16px;
}

.release-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    a {
        color: var(--text-primary);
        text-decoration: none;

        &:hover {
            color: var(--primary-color);
        }
    }
}

.release-artist {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0 0 8px;
}

.release-meta {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.8125rem;
        color: var(--text-secondary);

        .meta-icon {
            flex-shrink: 0;
            opacity: 0.7;
        }
    }
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-top: 32px;
}

.pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    padding: 0 6px;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-decoration: none;

    &:hover:not(.disabled) {
        background-color: var(--bg-secondary);
    }

    &.active {
        background-color: var(--primary-color);
        color: white;
    }

    &.disabled {
        opacity: 0.5;
        pointer-events: none;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
