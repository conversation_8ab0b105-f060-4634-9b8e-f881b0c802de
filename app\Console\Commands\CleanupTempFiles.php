<?php

namespace App\Console\Commands;

use App\Services\FileManagementService;
use Illuminate\Console\Command;

class CleanupTempFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'files:cleanup-temp {--hours=24 : Files older than this many hours will be deleted}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old temporary files';

    /**
     * Execute the console command.
     */
    public function handle(FileManagementService $fileService)
    {
        $hours = (int) $this->option('hours');
        
        $this->info("Очистка временных файлов старше {$hours} часов...");
        
        $fileService->cleanupOldTemporaryFiles($hours);
        
        $this->info('✓ Очистка временных файлов завершена');
        
        return 0;
    }
}
