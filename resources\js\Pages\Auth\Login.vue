<script setup>
import { Link, useForm } from "@inertiajs/vue3";
import AuthLayout from "@/Layouts/AuthLayout.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import TextInput from "@/Components/TextInput.vue";
import Checkbox from "@/Components/Checkbox.vue";
import OAuthButtons from "@/Components/Auth/OAuthButtons.vue";

defineProps({
    canResetPassword: Boolean,
    status: String,
    oauthProviders: {
        type: Object,
        default: () => ({}),
    },
});

const form = useForm({
    email: "",
    password: "",
    remember: false,
});

const submit = () => {
    form.transform((data) => ({
        ...data,
        remember: form.remember ? "on" : "",
    })).post(route("login"), {
        onFinish: () => form.reset("password"),
    });
};
</script>

<template>
    <AuthLayout title="Вход в систему">
        <div class="auth-form">
            <h2 class="form-title">Вход в систему</h2>
            <p class="form-subtitle">
                Введите свои данные для входа в личный кабинет
            </p>

            <div v-if="status" class="status-message">
                {{ status }}
            </div>

            <form @submit.prevent="submit">
                <div class="form-group">
                    <InputLabel for="email" value="Email" />
                    <TextInput
                        id="email"
                        v-model="form.email"
                        type="email"
                        class="form-control"
                        required
                        autofocus
                        autocomplete="username"
                    />
                    <InputError
                        class="error-text"
                        :message="form.errors.email"
                    />
                </div>

                <div class="form-group">
                    <InputLabel for="password" value="Пароль" />
                    <TextInput
                        id="password"
                        v-model="form.password"
                        type="password"
                        class="form-control"
                        required
                        autocomplete="current-password"
                    />
                    <InputError
                        class="error-text"
                        :message="form.errors.password"
                    />
                </div>

                <div class="form-options">
                    <label class="checkbox-container">
                        <Checkbox
                            v-model:checked="form.remember"
                            name="remember"
                        />
                        <span class="checkbox-label">Запомнить меня</span>
                    </label>

                    <Link
                        v-if="canResetPassword"
                        :href="route('password.request')"
                        class="forgot-link"
                    >
                        Забыли пароль?
                    </Link>
                </div>

                <div class="form-actions">
                    <PrimaryButton
                        class="login-btn"
                        :class="{ disabled: form.processing }"
                        :disabled="form.processing"
                    >
                        {{ form.processing ? "Вход..." : "Войти" }}
                    </PrimaryButton>
                </div>

                <div class="form-footer">
                    <span>Нет аккаунта?</span>
                    <Link :href="route('register')" class="register-link">
                        Зарегистрироваться
                    </Link>
                </div>
            </form>

            <!-- OAuth кнопки -->
            <OAuthButtons :providers="oauthProviders" />
        </div>
    </AuthLayout>
</template>

<style lang="scss">
.auth-form {
    width: 100%;

    .form-title {
        font-size: 1.75rem;
        font-weight: 700;
        margin: 0 0 8px;
        color: var(--text-primary);
    }

    .form-subtitle {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin: 0 0 32px;
    }

    .status-message {
        background-color: rgba(var(--success-color-rgb), 0.1);
        border-left: 3px solid var(--success-color);
        color: var(--success-color);
        padding: 12px 16px;
        border-radius: var(--radius-md);
        margin-bottom: 24px;
        font-size: 0.875rem;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: var(--radius-md);
        font-size: 0.9375rem;
        margin-top: 6px;
        transition: border-color 0.2s;

        &:focus {
            border-color: var(--primary-color);
            outline: none;
        }
    }

    .error-text {
        color: var(--danger-color);
        font-size: 0.75rem;
        margin-top: 6px;
    }

    .form-options {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24px;
    }

    .checkbox-container {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
    }

    .checkbox-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .forgot-link {
        font-size: 0.875rem;
        color: var(--primary-color);
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }

    .form-actions {
        margin-bottom: 24px;
    }

    .login-btn {
        width: 100%;
        padding: 12px;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: var(--radius-md);
        font-size: 0.9375rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
            background-color: var(--secondary-color);
        }

        &.disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
    }

    .form-footer {
        text-align: center;
        font-size: 0.875rem;
        color: var(--text-secondary);

        .register-link {
            color: var(--primary-color);
            text-decoration: none;
            margin-left: 6px;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}
</style>
