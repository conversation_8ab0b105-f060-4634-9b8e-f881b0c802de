<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tracks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('release_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->string('file_path', 2048);
            $table->string('file_type')->default('mp3');
            $table->integer('duration_seconds')->nullable();
            $table->integer('track_number');
            $table->boolean('is_explicit')->default(false);
            $table->text('lyrics')->nullable();
            $table->json('meta_info')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tracks');
    }
};
