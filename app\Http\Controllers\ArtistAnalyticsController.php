<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Release;
use App\Models\Track;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ArtistAnalyticsController extends Controller
{
    /**
     * Display artist analytics dashboard.
     */
    public function index(Request $request): Response
    {
        $user = auth()->user();
        $artist = $user->artist;

        if (!$artist) {
            abort(404, 'Профиль артиста не найден');
        }

        // Получаем период для анализа (по умолчанию последние 30 дней)
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays((int)$period);
        $endDate = Carbon::now();

        // Общая статистика артиста
        $totalStats = $this->getArtistTotalStats($artist);
        
        // Статистика за период
        $periodStats = $this->getArtistPeriodStats($artist, $startDate, $endDate);
        
        // Данные для графиков
        $chartsData = $this->getArtistChartsData($artist, $startDate, $endDate, $period);
        
        // Последние релизы
        $recentReleases = $this->getRecentReleases($artist);
        
        // Последние заявки
        $recentApplications = $this->getRecentApplications($user);

        return Inertia::render('Artist/Analytics', [
            'totalStats' => $totalStats,
            'periodStats' => $periodStats,
            'chartsData' => $chartsData,
            'recentReleases' => $recentReleases,
            'recentApplications' => $recentApplications,
            'period' => $period,
            'artist' => $artist,
        ]);
    }

    /**
     * Получить общую статистику артиста.
     */
    private function getArtistTotalStats($artist): array
    {
        return [
            'releases' => [
                'total' => Release::where('artist_id', $artist->id)->count(),
                'published' => Release::where('artist_id', $artist->id)->where('status', 'published')->count(),
                'pending' => Release::where('artist_id', $artist->id)->where('status', 'pending')->count(),
                'draft' => Release::where('artist_id', $artist->id)->where('status', 'draft')->count(),
            ],
            'tracks' => [
                'total' => Track::whereHas('release', function($query) use ($artist) {
                    $query->where('artist_id', $artist->id);
                })->count(),
            ],
            'applications' => [
                'total' => Application::where('user_id', $artist->user_id)->count(),
                'pending' => Application::where('user_id', $artist->user_id)->where('status', 'pending')->count(),
                'approved' => Application::where('user_id', $artist->user_id)->where('status', 'approved')->count(),
                'rejected' => Application::where('user_id', $artist->user_id)->where('status', 'rejected')->count(),
            ],
        ];
    }

    /**
     * Получить статистику артиста за период.
     */
    private function getArtistPeriodStats($artist, Carbon $startDate, Carbon $endDate): array
    {
        return [
            'releases' => [
                'new' => Release::where('artist_id', $artist->id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count(),
                'published' => Release::where('artist_id', $artist->id)
                    ->where('status', 'published')
                    ->whereBetween('updated_at', [$startDate, $endDate])
                    ->count(),
            ],
            'tracks' => [
                'new' => Track::whereHas('release', function($query) use ($artist) {
                    $query->where('artist_id', $artist->id);
                })->whereBetween('created_at', [$startDate, $endDate])->count(),
            ],
            'applications' => [
                'new' => Application::where('user_id', $artist->user_id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count(),
                'processed' => Application::where('user_id', $artist->user_id)
                    ->whereIn('status', ['approved', 'rejected'])
                    ->whereBetween('updated_at', [$startDate, $endDate])
                    ->count(),
            ],
        ];
    }

    /**
     * Получить данные для графиков артиста.
     */
    private function getArtistChartsData($artist, Carbon $startDate, Carbon $endDate, string $period): array
    {
        $days = (int)$period;
        $dates = [];
        $releasesData = [];
        $tracksData = [];
        $applicationsData = [];

        // Генерируем даты для оси X
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dates[] = $date->format('Y-m-d');
            
            // Подсчитываем данные для каждого дня
            $dayStart = $date->startOfDay();
            $dayEnd = $date->endOfDay();
            
            $releasesData[] = Release::where('artist_id', $artist->id)
                ->whereBetween('created_at', [$dayStart, $dayEnd])
                ->count();
                
            $tracksData[] = Track::whereHas('release', function($query) use ($artist) {
                $query->where('artist_id', $artist->id);
            })->whereBetween('created_at', [$dayStart, $dayEnd])->count();
            
            $applicationsData[] = Application::where('user_id', $artist->user_id)
                ->whereBetween('created_at', [$dayStart, $dayEnd])
                ->count();
        }

        return [
            'dates' => $dates,
            'releases' => $releasesData,
            'tracks' => $tracksData,
            'applications' => $applicationsData,
        ];
    }

    /**
     * Получить последние релизы артиста.
     */
    private function getRecentReleases($artist): array
    {
        return Release::where('artist_id', $artist->id)
            ->with(['tracks'])
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($release) {
                return [
                    'id' => $release->id,
                    'title' => $release->title,
                    'status' => $release->status,
                    'type' => $release->type,
                    'tracks_count' => $release->tracks->count(),
                    'created_at' => $release->created_at,
                    'release_date' => $release->release_date,
                ];
            })
            ->toArray();
    }

    /**
     * Получить последние заявки артиста.
     */
    private function getRecentApplications($user): array
    {
        return Application::where('user_id', $user->id)
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($application) {
                return [
                    'id' => $application->id,
                    'title' => $application->title,
                    'type' => $application->type,
                    'status' => $application->status,
                    'created_at' => $application->created_at,
                    'reviewed_at' => $application->reviewed_at,
                ];
            })
            ->toArray();
    }

    /**
     * Get performance metrics for artist.
     */
    public function performance(Request $request): Response
    {
        $user = auth()->user();
        $artist = $user->artist;

        if (!$artist) {
            abort(404, 'Профиль артиста не найден');
        }

        // Здесь можно добавить метрики производительности:
        // - Количество прослушиваний
        // - Рейтинг треков
        // - Популярность релизов
        // - Статистика скачиваний
        
        $performanceData = [
            'total_plays' => 0, // Заглушка для будущей реализации
            'total_downloads' => 0,
            'average_rating' => 0,
            'top_tracks' => [],
            'monthly_growth' => 0,
        ];

        return Inertia::render('Artist/Performance', [
            'performanceData' => $performanceData,
            'artist' => $artist,
        ]);
    }
}
