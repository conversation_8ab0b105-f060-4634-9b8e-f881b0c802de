// Современная система форм 2025
.form-group {
    margin-bottom: 24px;
    position: relative;
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: 8px;
    transition: color var(--transition-fast);

    &.required::after {
        content: '*';
        color: var(--danger-color);
        margin-left: 4px;
    }
}

.form-control {
    width: 100%;
    padding: 14px 16px;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    outline: none;
    position: relative;

    &::placeholder {
        color: var(--text-tertiary);
        font-weight: var(--font-weight-normal);
    }

    &:focus {
        border-color: var(--primary-color);
        box-shadow: var(--shadow-glow);
        background: var(--bg-secondary);

        + .form-label {
            color: var(--primary-color);
        }
    }

    &:hover:not(:focus) {
        border-color: var(--border-medium);
        background: var(--bg-secondary);
    }

    &.is-valid {
        border-color: var(--success-color);

        &:focus {
            box-shadow: 0 0 0 3px rgba(var(--success-color-rgb), 0.2);
        }
    }

    &.is-invalid {
        border-color: var(--danger-color);

        &:focus {
            box-shadow: 0 0 0 3px rgba(var(--danger-color-rgb), 0.2);
        }
    }

    &:disabled {
        background: var(--bg-disabled);
        color: var(--text-disabled);
        cursor: not-allowed;
        opacity: 0.6;
    }

    // Размеры
    &.form-control-sm {
        padding: 10px 12px;
        font-size: var(--font-size-xs);
        border-radius: var(--radius-md);
    }

    &.form-control-lg {
        padding: 18px 20px;
        font-size: var(--font-size-lg);
        border-radius: var(--radius-xl);
    }
}

// Textarea
textarea.form-control {
    min-height: 120px;
    resize: vertical;

    &.textarea-sm {
        min-height: 80px;
    }

    &.textarea-lg {
        min-height: 160px;
    }
}

.form-error {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--danger-color);
}

.form-hint {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

// Чекбоксы и радио-кнопки
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    input[type="checkbox"],
    input[type="radio"] {
        margin-right: 0.5rem;
        cursor: pointer;
    }

    .form-check-label {
        font-size: 0.875rem;
        cursor: pointer;
    }
}

// Загрузка файлов
.file-upload {
    position: relative;
    display: flex;
    flex-direction: column;

    .file-upload-input {
        position: absolute;
        width: 0.1px;
        height: 0.1px;
        opacity: 0;
        overflow: hidden;
        z-index: -1;
    }

    .file-upload-label {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        background-color: var(--bg-secondary);
        color: var(--text-primary);
        border: 1px dashed var(--text-disabled);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all var(--transition-fast);
        margin-bottom: 0.5rem;

        &:hover {
            background-color: var(--bg-primary);
        }

        .icon {
            margin-right: 0.5rem;
        }
    }

    .file-preview {
        margin-top: 0.5rem;

        &.image-preview {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: var(--radius-sm);
        }
    }

    .file-name {
        margin-top: 0.5rem;
        font-size: 0.75rem;
        color: var(--text-secondary);
    }
}

// Прогресс-бар
.progress {
    height: 0.5rem;
    background-color: var(--bg-secondary);
    border-radius: 1rem;
    overflow: hidden;
    margin-top: 0.5rem;

    .progress-bar {
        height: 100%;
        background-color: var(--primary-color);
        border-radius: 1rem;
        transition: width var(--transition-normal);
    }
}
