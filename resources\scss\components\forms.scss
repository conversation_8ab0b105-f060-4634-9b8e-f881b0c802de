.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--text-disabled);
    border-radius: var(--radius-sm);
    transition: border-color var(--transition-fast);

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);
    }

    &:disabled {
        background-color: var(--bg-secondary);
        cursor: not-allowed;
        opacity: 0.7;
    }

    &.is-invalid {
        border-color: var(--danger-color);

        &:focus {
            box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
        }
    }
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

.form-error {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--danger-color);
}

.form-hint {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

// Чекбоксы и радио-кнопки
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    input[type="checkbox"],
    input[type="radio"] {
        margin-right: 0.5rem;
        cursor: pointer;
    }

    .form-check-label {
        font-size: 0.875rem;
        cursor: pointer;
    }
}

// Загрузка файлов
.file-upload {
    position: relative;
    display: flex;
    flex-direction: column;

    .file-upload-input {
        position: absolute;
        width: 0.1px;
        height: 0.1px;
        opacity: 0;
        overflow: hidden;
        z-index: -1;
    }

    .file-upload-label {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        background-color: var(--bg-secondary);
        color: var(--text-primary);
        border: 1px dashed var(--text-disabled);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all var(--transition-fast);
        margin-bottom: 0.5rem;

        &:hover {
            background-color: var(--bg-primary);
        }

        .icon {
            margin-right: 0.5rem;
        }
    }

    .file-preview {
        margin-top: 0.5rem;

        &.image-preview {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: var(--radius-sm);
        }
    }

    .file-name {
        margin-top: 0.5rem;
        font-size: 0.75rem;
        color: var(--text-secondary);
    }
}

// Прогресс-бар
.progress {
    height: 0.5rem;
    background-color: var(--bg-secondary);
    border-radius: 1rem;
    overflow: hidden;
    margin-top: 0.5rem;

    .progress-bar {
        height: 100%;
        background-color: var(--primary-color);
        border-radius: 1rem;
        transition: width var(--transition-normal);
    }
}
