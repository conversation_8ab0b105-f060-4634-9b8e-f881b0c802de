.footer {
    margin-left: var(--sidebar-width);
    padding: 1.5rem 2rem;
    background-color: var(--bg-primary);
    border-top: 1px solid var(--bg-secondary);
    transition: margin-left var(--transition-normal);

    // Когда сайдбар свернут
    &.footer-expanded {
        margin-left: var(--sidebar-collapsed-width);
    }

    .footer-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 1600px;
        margin: 0 auto;

        .footer-copyright {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .footer-links {
            display: flex;
            gap: 1rem;

            a {
                font-size: 0.75rem;
                color: var(--text-secondary);
                text-decoration: none;

                &:hover {
                    color: var(--primary-color);
                }
            }
        }
    }
}

// Адаптивность
@media (max-width: 768px) {
    .footer {
        margin-left: 0;
        padding: 1rem;

        .footer-content {
            flex-direction: column;
            align-items: center;
            gap: 1rem;

            .footer-copyright {
                order: 2;
            }

            .footer-links {
                order: 1;
            }
        }
    }
}

.main-footer {
    padding: 16px 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    background-color: var(--bg-primary);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.copyright {
    font-size: 0.8125rem;
}

.footer-links {
    display: flex;
    gap: 16px;
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.8125rem;

    &:hover {
        color: var(--primary-color);
        text-decoration: underline;
    }
}

/* Адаптивность */
@media (max-width: 576px) {
    .footer-content {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .footer-links {
        gap: 12px;
    }
}
