@tailwind base;
@tailwind components;
@tailwind utilities;

[x-cloak] {
    display: none;
}

/* Стиль для предупреждающей кнопки */
.warning-btn {
    background-color: var(--warning-color) !important;
    color: #333 !important;
}

.warning-btn:hover {
    background-color: #f7b83e !important;
}

:root {
    --primary-color: #5e72e4;
    --secondary-color: #8392ab;
    --success-color: #2dce89;
    --info-color: #11cdef;
    --warning-color: #fb6340;
    --danger-color: #f5365c;

    /* Фоновые цвета для уведомлений */
    --success-bg: rgba(45, 206, 137, 0.1);
    --info-bg: rgba(17, 205, 239, 0.1);
    --warning-bg: rgba(251, 99, 64, 0.1);
    --danger-bg: rgba(245, 54, 92, 0.1);

    /* Цвета интерфейса */
    --text-primary: #252f40;
    --text-secondary: #67748e;
    --text-tertiary: #97a6b8;
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-hover: #f2f2f2;
    --border-color: #e9ecef;
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --transition: all 0.2s ease;

    /* Тени */
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.1);

    /* Размеры элементов */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --header-height: 64px;
    --content-max-width: 1800px;
}

/* Конец файла */