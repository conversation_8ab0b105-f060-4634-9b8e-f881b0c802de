<script setup>
import { ref, computed, watch } from "vue";
import { router, useForm } from "@inertiajs/vue3";
import {
    Upload,
    Music,
    Plus,
    X,
    Clock,
    Disc,
    AlertTriangle,
    Loader2,
    CheckCircle,
    AlignJustify,
    Trash2,
    Edit,
} from "lucide-vue-next";

const props = defineProps({
    release: {
        type: Object,
        required: true,
    },
    tracks: {
        type: Array,
        required: true,
    },
    canEdit: {
        type: Boolean,
        default: false,
    },
});

// Форма для загрузки трека
const form = useForm({
    title: "",
    track_number: "",
    file: null,
    is_explicit: false,
    lyrics: "",
});

// Предпросмотр загружаемого файла
const filePreview = ref(null);
const fileError = ref("");

// Переключение режима добавления трека
const isAddingTrack = ref(false);
const isEditingTrack = ref(null);

// Список загруженных треков
const sortedTracks = computed(() => {
    return [...props.tracks].sort((a, b) => a.track_number - b.track_number);
});

// Вычисление следующего номера трека
const nextTrackNumber = computed(() => {
    if (!props.tracks || props.tracks.length === 0) return 1;

    // Находим максимальный номер трека и добавляем 1
    return Math.max(...props.tracks.map((t) => t.track_number)) + 1;
});

// Сброс формы
function resetForm() {
    form.reset();
    form.track_number = nextTrackNumber.value;
    filePreview.value = null;
    fileError.value = "";
    isAddingTrack.value = false;
    isEditingTrack.value = null;
}

// Инициализация формы для добавления нового трека
function initAddTrackForm() {
    resetForm();
    isAddingTrack.value = true;
    form.track_number = nextTrackNumber.value;
}

// Инициализация формы для редактирования трека
function initEditTrackForm(track) {
    resetForm();
    isEditingTrack.value = track.id;

    form.title = track.title;
    form.track_number = track.track_number;
    form.is_explicit = track.is_explicit;
    form.lyrics = track.lyrics || "";
}

// Отправка трека на сервер
function submitTrack() {
    if (isEditingTrack.value) {
        // Обновление существующего трека
        form.put(route("tracks.update", isEditingTrack.value), {
            onSuccess: () => {
                resetForm();
            },
            preserveScroll: true,
        });
    } else {
        // Создание нового трека
        form.post(route("tracks.store", props.release.id), {
            onSuccess: () => {
                resetForm();
            },
            preserveScroll: true,
        });
    }
}

// Удаление трека
function deleteTrack(track) {
    if (confirm("Вы уверены, что хотите удалить этот трек?")) {
        router.delete(route("tracks.destroy", track.id), {
            preserveScroll: true,
        });
    }
}

// Обработка загрузки файла
function handleFileUpload(event) {
    const file = event.target.files[0];
    fileError.value = "";

    // Проверка файла
    if (!file) {
        form.file = null;
        filePreview.value = null;
        return;
    }

    // Проверка типа файла
    const allowedTypes = [
        "audio/mpeg",
        "audio/mp3",
        "audio/wav",
        "audio/ogg",
        "audio/x-m4a",
    ];
    if (!allowedTypes.includes(file.type)) {
        fileError.value =
            "Неподдерживаемый формат аудио. Используйте MP3, WAV, OGG или M4A.";
        event.target.value = "";
        return;
    }

    // Проверка размера файла (макс. 20MB)
    if (file.size > 20 * 1024 * 1024) {
        fileError.value = "Размер файла не должен превышать 20MB.";
        event.target.value = "";
        return;
    }

    // Установка файла
    form.file = file;
    filePreview.value = file.name;

    // Если название трека не указано, используем имя файла
    if (!form.title) {
        // Удаляем расширение и заменяем подчеркивания/дефисы пробелами
        form.title = file.name
            .replace(/\.[^/.]+$/, "")
            .replace(/[_-]/g, " ")
            .replace(/\b\w/g, (l) => l.toUpperCase());
    }
}

// Удаление выбранного файла
function removeFile() {
    form.file = null;
    filePreview.value = null;
    const fileInput = document.getElementById("track_file");
    if (fileInput) fileInput.value = "";
}

// Форматирование длительности трека
function formatDuration(seconds) {
    if (!seconds) return "0:00";

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
}

// Отмена загрузки трека
function cancelTrackUpload() {
    resetForm();
}

// Проверка валидности формы
const isValid = computed(() => {
    return (
        form.title &&
        form.track_number &&
        (form.file || isEditingTrack.value) &&
        !fileError.value
    );
});
</script>

<template>
    <div class="track-uploader">
        <h3 class="section-title">Треки</h3>

        <!-- Список треков -->
        <div v-if="sortedTracks.length > 0" class="tracks-list">
            <div
                v-for="track in sortedTracks"
                :key="track.id"
                class="track-item"
            >
                <div class="track-number">
                    {{ track.track_number }}
                </div>

                <div class="track-info">
                    <div class="track-title">
                        {{ track.title }}
                        <span v-if="track.is_explicit" class="explicit-badge"
                            >E</span
                        >
                    </div>

                    <div class="track-meta">
                        <span class="duration">
                            <Clock size="14" class="meta-icon" />
                            {{ formatDuration(track.duration_seconds) }}
                        </span>
                        <span class="file-type">
                            <Disc size="14" class="meta-icon" />
                            {{ track.file_type?.toUpperCase() || "MP3" }}
                        </span>
                    </div>
                </div>

                <div v-if="canEdit" class="track-actions">
                    <button
                        type="button"
                        class="action-btn edit"
                        @click="initEditTrackForm(track)"
                        :disabled="isAddingTrack || isEditingTrack"
                    >
                        <Edit size="16" />
                    </button>

                    <button
                        type="button"
                        class="action-btn delete"
                        @click="deleteTrack(track)"
                        :disabled="isAddingTrack || isEditingTrack"
                    >
                        <Trash2 size="16" />
                    </button>
                </div>
            </div>
        </div>

        <!-- Сообщение, если треков нет -->
        <div v-else class="no-tracks">
            <Music size="32" class="empty-icon" />
            <p class="empty-text">Треки еще не добавлены</p>
        </div>

        <!-- Форма добавления/редактирования трека -->
        <div v-if="isAddingTrack || isEditingTrack" class="track-form">
            <h4 class="form-title">
                {{
                    isEditingTrack ? "Редактирование трека" : "Добавление трека"
                }}
            </h4>

            <form @submit.prevent="submitTrack">
                <div class="form-grid">
                    <!-- Левая колонка: информация о треке -->
                    <div class="form-column">
                        <!-- Название трека -->
                        <div class="form-group">
                            <label for="track_title" class="form-label">
                                Название трека *
                            </label>
                            <input
                                id="track_title"
                                v-model="form.title"
                                type="text"
                                class="form-input"
                                :class="{ error: form.errors.title }"
                                placeholder="Введите название трека"
                                required
                                autofocus
                            />
                            <p v-if="form.errors.title" class="error-text">
                                {{ form.errors.title }}
                            </p>
                        </div>

                        <!-- Номер трека -->
                        <div class="form-group">
                            <label for="track_number" class="form-label">
                                Номер трека *
                            </label>
                            <input
                                id="track_number"
                                v-model="form.track_number"
                                type="number"
                                class="form-input"
                                :class="{ error: form.errors.track_number }"
                                placeholder="Номер"
                                min="1"
                                required
                            />
                            <p
                                v-if="form.errors.track_number"
                                class="error-text"
                            >
                                {{ form.errors.track_number }}
                            </p>
                        </div>

                        <!-- Возрастные ограничения -->
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input
                                    id="track_explicit"
                                    v-model="form.is_explicit"
                                    type="checkbox"
                                    class="form-checkbox"
                                />
                                <label
                                    for="track_explicit"
                                    class="checkbox-label"
                                >
                                    Трек содержит ненормативную лексику (18+)
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Правая колонка: загрузка файла -->
                    <div class="form-column">
                        <!-- Загрузка файла -->
                        <div
                            v-if="!isEditingTrack || !filePreview"
                            class="form-group"
                        >
                            <label class="form-label">
                                Аудиофайл *
                                <span
                                    v-if="isEditingTrack"
                                    class="optional-text"
                                >
                                    (загрузите новый файл только если хотите
                                    заменить существующий)
                                </span>
                            </label>

                            <div
                                class="file-upload-area"
                                :class="{
                                    'has-file': filePreview,
                                    error: form.errors.file || fileError,
                                }"
                            >
                                <div
                                    v-if="!filePreview"
                                    class="upload-placeholder"
                                >
                                    <Upload class="upload-icon" size="32" />
                                    <p class="upload-text">
                                        Перетащите аудиофайл сюда или нажмите,
                                        чтобы выбрать
                                    </p>
                                    <p class="upload-hint">
                                        Поддерживаемые форматы: MP3, WAV, OGG,
                                        M4A (макс. 20MB)
                                    </p>
                                    <input
                                        id="track_file"
                                        type="file"
                                        class="file-input"
                                        accept="audio/mpeg,audio/mp3,audio/wav,audio/ogg,audio/x-m4a"
                                        @change="handleFileUpload"
                                        :required="!isEditingTrack"
                                    />
                                </div>

                                <div v-else class="file-preview">
                                    <Music class="file-icon" size="20" />
                                    <span class="file-name">{{
                                        filePreview
                                    }}</span>
                                    <button
                                        type="button"
                                        @click="removeFile"
                                        class="remove-file-btn"
                                    >
                                        <X size="16" />
                                    </button>
                                </div>
                            </div>

                            <p v-if="form.errors.file" class="error-text">
                                {{ form.errors.file }}
                            </p>
                            <p v-else-if="fileError" class="error-text">
                                {{ fileError }}
                            </p>
                        </div>

                        <!-- Текст песни -->
                        <div class="form-group">
                            <label for="track_lyrics" class="form-label">
                                Текст песни
                                <span class="optional-text"
                                    >(необязательно)</span
                                >
                            </label>
                            <textarea
                                id="track_lyrics"
                                v-model="form.lyrics"
                                class="form-textarea"
                                :class="{ error: form.errors.lyrics }"
                                placeholder="Добавьте текст песни"
                                rows="6"
                            ></textarea>
                            <p v-if="form.errors.lyrics" class="error-text">
                                {{ form.errors.lyrics }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Кнопки формы -->
                <div class="form-actions">
                    <button
                        type="button"
                        class="btn btn-outline"
                        @click="cancelTrackUpload"
                    >
                        Отмена
                    </button>

                    <button
                        type="submit"
                        class="btn btn-primary"
                        :disabled="!isValid || form.processing"
                    >
                        <Loader2
                            v-if="form.processing"
                            class="btn-icon spin"
                            size="16"
                        />
                        <span v-if="form.processing">Загрузка...</span>
                        <span v-else>
                            {{
                                isEditingTrack
                                    ? "Сохранить изменения"
                                    : "Добавить трек"
                            }}
                        </span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Кнопка добавления трека -->
        <button
            v-else-if="canEdit"
            type="button"
            class="add-track-btn"
            @click="initAddTrackForm"
        >
            <Plus class="btn-icon" size="18" />
            <span>Добавить трек</span>
        </button>

        <!-- Уведомление для релизов без возможности редактирования -->
        <div
            v-else-if="!canEdit && sortedTracks.length === 0"
            class="locked-info"
        >
            <AlertTriangle class="info-icon" size="20" />
            <p class="info-text">
                Нельзя добавлять треки к этому релизу, так как он уже отправлен
                на модерацию.
            </p>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.track-uploader {
    margin-bottom: 24px;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 16px;
    color: var(--text-primary);
}

.tracks-list {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    margin-bottom: 16px;
    overflow: hidden;
}

.track-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s;

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background-color: rgba(var(--primary-color-rgb), 0.02);
    }
}

.track-number {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-sm);
    margin-right: 12px;
    flex-shrink: 0;
}

.track-info {
    flex-grow: 1;
    min-width: 0;
}

.track-title {
    font-weight: 500;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.explicit-badge {
    display: inline-block;
    width: 16px;
    height: 16px;
    line-height: 16px;
    background-color: var(--text-secondary);
    color: white;
    font-size: 0.6875rem;
    font-weight: 700;
    text-align: center;
    border-radius: 2px;
    margin-left: 8px;
    vertical-align: middle;
}

.track-meta {
    display: flex;
    gap: 12px;
    font-size: 0.75rem;
    color: var(--text-secondary);

    .meta-icon {
        vertical-align: middle;
        margin-right: 4px;
    }
}

.track-actions {
    display: flex;
    gap: 8px;
    margin-left: auto;
    flex-shrink: 0;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-md);
    border: 1px solid rgba(0, 0, 0, 0.1);
    background-color: transparent;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
        background-color: var(--bg-secondary);
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    &.edit:hover {
        color: var(--primary-color);
        border-color: var(--primary-color);
        background-color: rgba(var(--primary-color-rgb), 0.05);
    }

    &.delete:hover {
        color: var(--danger-color);
        border-color: var(--danger-color);
        background-color: rgba(var(--danger-color-rgb), 0.05);
    }
}

.no-tracks {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    text-align: center;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    margin-bottom: 16px;

    .empty-icon {
        color: var(--text-disabled);
        margin-bottom: 12px;
    }

    .empty-text {
        font-size: 0.9375rem;
        color: var(--text-secondary);
        margin: 0;
    }
}

.track-form {
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.form-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 16px;
    color: var(--text-primary);
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
    }
}

.form-column {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-group {
    margin-bottom: 8px;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 6px;
    color: var(--text-secondary);
}

.optional-text {
    font-size: 0.75rem;
    font-weight: 400;
    color: var(--text-disabled);
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: border-color 0.2s;

    &:focus {
        border-color: var(--primary-color);
        outline: none;
    }

    &.error {
        border-color: var(--danger-color);
    }
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-checkbox {
    width: 18px;
    height: 18px;
    margin: 0;
}

.checkbox-label {
    font-size: 0.875rem;
    color: var(--text-primary);
}

.file-upload-area {
    border: 2px dashed rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    overflow: hidden;
    position: relative;
    transition: all 0.2s;

    &:hover {
        border-color: var(--primary-color);
    }

    &.has-file {
        border-style: solid;
    }

    &.error {
        border-color: var(--danger-color);
    }
}

.upload-placeholder {
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;

    .upload-icon {
        color: var(--text-secondary);
        margin-bottom: 12px;
    }

    .upload-text {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
        margin: 0 0 6px;
    }

    .upload-hint {
        font-size: 0.75rem;
        color: var(--text-secondary);
        margin: 0;
    }
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-preview {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--bg-secondary);

    .file-icon {
        color: var(--text-secondary);
        flex-shrink: 0;
        margin-right: 12px;
    }

    .file-name {
        flex-grow: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 0.875rem;
    }

    .remove-file-btn {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.05);
        border: none;
        color: var(--text-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-left: 12px;
        flex-shrink: 0;

        &:hover {
            background-color: rgba(0, 0, 0, 0.1);
            color: var(--text-primary);
        }
    }
}

.error-text {
    color: var(--danger-color);
    font-size: 0.75rem;
    margin-top: 4px;
    margin-bottom: 0;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.btn {
    padding: 8px 16px;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border: none;
    transition: all 0.2s;

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.btn-outline {
    background-color: transparent;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: var(--text-primary);

    &:hover:not(:disabled) {
        background-color: var(--bg-secondary);
    }
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;

    &:hover:not(:disabled) {
        background-color: var(--secondary-color);
    }
}

.add-track-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 12px;
    border: 2px dashed rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
        background-color: rgba(var(--primary-color-rgb), 0.02);
    }

    .btn-icon {
        color: var(--text-secondary);
    }

    &:hover .btn-icon {
        color: var(--primary-color);
    }
}

.locked-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background-color: rgba(var(--warning-color-rgb), 0.05);
    border-left: 4px solid var(--warning-color);
    border-radius: var(--radius-md);

    .info-icon {
        color: var(--warning-color);
        flex-shrink: 0;
    }

    .info-text {
        margin: 0;
        font-size: 0.875rem;
        color: var(--text-secondary);
    }
}

.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
