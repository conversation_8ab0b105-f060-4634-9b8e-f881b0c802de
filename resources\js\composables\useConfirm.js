import { ref, createApp, h } from "vue";
import ConfirmDialog from "@/Components/UI/ConfirmDialog.vue";

// Глобальное состояние для диалогов
const dialogs = ref([]);

/**
 * Composable для работы с диалогами подтверждения
 */
export function useConfirm() {
    /**
     * Показать диалог подтверждения
     */
    function confirm(options = {}) {
        return new Promise((resolve) => {
            const defaultOptions = {
                title: "Подтверждение",
                message: "Вы уверены?",
                type: "warning",
                confirmText: "Подтвердить",
                cancelText: "Отмена",
                confirmButtonType: "primary",
            };

            const dialogOptions = { ...defaultOptions, ...options };
            const dialogId = Date.now() + Math.random();

            const dialog = {
                id: dialogId,
                ...dialogOptions,
                show: true,
                loading: false,
                resolve,
            };

            dialogs.value.push(dialog);
        });
    }

    /**
     * Показать диалог с опасным действием
     */
    function confirmDanger(message, title = "Внимание!") {
        return confirm({
            title,
            message,
            type: "danger",
            confirmText: "Удалить",
            confirmButtonType: "danger",
        });
    }

    /**
     * Показать информационный диалог
     */
    function alert(message, title = "Информация") {
        return confirm({
            title,
            message,
            type: "info",
            confirmText: "ОК",
            cancelText: null, // Скрываем кнопку отмены
        });
    }

    /**
     * Показать диалог успеха
     */
    function success(message, title = "Успешно!") {
        return confirm({
            title,
            message,
            type: "success",
            confirmText: "ОК",
            confirmButtonType: "success",
            cancelText: null,
        });
    }

    /**
     * Закрыть диалог
     */
    function closeDialog(dialogId, result = false) {
        const dialogIndex = dialogs.value.findIndex(d => d.id === dialogId);
        if (dialogIndex !== -1) {
            const dialog = dialogs.value[dialogIndex];
            dialog.resolve(result);
            dialogs.value.splice(dialogIndex, 1);
        }
    }

    /**
     * Установить состояние загрузки для диалога
     */
    function setDialogLoading(dialogId, loading = true) {
        const dialog = dialogs.value.find(d => d.id === dialogId);
        if (dialog) {
            dialog.loading = loading;
        }
    }

    return {
        dialogs,
        confirm,
        confirmDanger,
        alert,
        success,
        closeDialog,
        setDialogLoading,
    };
}

/**
 * Глобальные методы для быстрого доступа
 */
export const $confirm = {
    /**
     * Обычное подтверждение
     */
    show(message, title = "Подтверждение") {
        const { confirm } = useConfirm();
        return confirm({ message, title });
    },

    /**
     * Опасное действие
     */
    danger(message, title = "Внимание!") {
        const { confirmDanger } = useConfirm();
        return confirmDanger(message, title);
    },

    /**
     * Информационное сообщение
     */
    info(message, title = "Информация") {
        const { alert } = useConfirm();
        return alert(message, title);
    },

    /**
     * Сообщение об успехе
     */
    success(message, title = "Успешно!") {
        const { success } = useConfirm();
        return success(message, title);
    },

    /**
     * Замена стандартного confirm()
     */
    async ask(message) {
        const { confirm } = useConfirm();
        return await confirm({ message });
    },

    /**
     * Замена стандартного alert()
     */
    async tell(message) {
        const { alert } = useConfirm();
        return await alert(message);
    },
};

/**
 * Компонент-провайдер диалогов для добавления в корневой компонент
 */
export const ConfirmProvider = {
    name: "ConfirmProvider",
    setup() {
        const { dialogs, closeDialog } = useConfirm();

        function handleConfirm(dialog) {
            closeDialog(dialog.id, true);
        }

        function handleCancel(dialog) {
            closeDialog(dialog.id, false);
        }

        return () => {
            return dialogs.value.map(dialog => 
                h(ConfirmDialog, {
                    key: dialog.id,
                    show: dialog.show,
                    title: dialog.title,
                    message: dialog.message,
                    type: dialog.type,
                    confirmText: dialog.confirmText,
                    cancelText: dialog.cancelText,
                    confirmButtonType: dialog.confirmButtonType,
                    loading: dialog.loading,
                    onConfirm: () => handleConfirm(dialog),
                    onCancel: () => handleCancel(dialog),
                    onClose: () => handleCancel(dialog),
                })
            );
        };
    },
};

// Примеры использования:
/*
// В компоненте:
import { useConfirm, $confirm } from '@/composables/useConfirm';

// Способ 1: через composable
const { confirm, confirmDanger, alert, success } = useConfirm();

async function deleteItem() {
    const confirmed = await confirmDanger(
        'Это действие нельзя отменить. Продолжить?',
        'Удалить элемент?'
    );
    
    if (confirmed) {
        // Выполняем удаление
    }
}

// Способ 2: через глобальные методы
async function saveData() {
    try {
        await api.save();
        await $confirm.success('Данные успешно сохранены!');
    } catch (error) {
        await $confirm.info('Произошла ошибка при сохранении');
    }
}

// Способ 3: замена стандартных методов
async function oldStyleConfirm() {
    // Вместо: if (confirm('Удалить?'))
    if (await $confirm.ask('Удалить?')) {
        // Выполняем действие
    }
    
    // Вместо: alert('Готово!')
    await $confirm.tell('Готово!');
}
*/
