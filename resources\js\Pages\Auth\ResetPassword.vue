<script setup>
import { Link, useForm } from "@inertiajs/vue3";
import AuthLayout from "@/Layouts/AuthLayout.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import TextInput from "@/Components/TextInput.vue";

const props = defineProps({
    email: String,
    token: String,
});

const form = useForm({
    token: props.token,
    email: props.email,
    password: "",
    password_confirmation: "",
});

const submit = () => {
    form.post(route("password.update"), {
        onFinish: () => form.reset("password", "password_confirmation"),
    });
};
</script>

<template>
    <AuthLayout title="Сброс пароля">
        <div class="auth-form">
            <h2 class="form-title">Создание нового пароля</h2>
            <p class="form-subtitle">
                Пожалуйста, введите новый пароль для вашей учетной записи.
            </p>

            <form @submit.prevent="submit">
                <div class="form-group">
                    <InputLabel for="email" value="Email" />
                    <TextInput
                        id="email"
                        v-model="form.email"
                        type="email"
                        class="form-control"
                        required
                        readonly
                        autocomplete="username"
                    />
                    <InputError
                        class="error-text"
                        :message="form.errors.email"
                    />
                </div>

                <div class="form-group">
                    <InputLabel for="password" value="Новый пароль" />
                    <TextInput
                        id="password"
                        v-model="form.password"
                        type="password"
                        class="form-control"
                        required
                        autocomplete="new-password"
                    />
                    <InputError
                        class="error-text"
                        :message="form.errors.password"
                    />
                </div>

                <div class="form-group">
                    <InputLabel
                        for="password_confirmation"
                        value="Подтверждение пароля"
                    />
                    <TextInput
                        id="password_confirmation"
                        v-model="form.password_confirmation"
                        type="password"
                        class="form-control"
                        required
                        autocomplete="new-password"
                    />
                    <InputError
                        class="error-text"
                        :message="form.errors.password_confirmation"
                    />
                </div>

                <div class="form-actions">
                    <PrimaryButton
                        class="reset-btn"
                        :class="{ disabled: form.processing }"
                        :disabled="form.processing"
                    >
                        {{
                            form.processing
                                ? "Сохранение..."
                                : "Сохранить новый пароль"
                        }}
                    </PrimaryButton>
                </div>

                <div class="form-footer">
                    <Link :href="route('login')" class="back-link">
                        Вернуться на страницу входа
                    </Link>
                </div>
            </form>
        </div>
    </AuthLayout>
</template>

<style lang="scss">
.auth-form {
    width: 100%;

    .form-title {
        font-size: 1.75rem;
        font-weight: 700;
        margin: 0 0 8px;
        color: var(--text-primary);
    }

    .form-subtitle {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin: 0 0 32px;
        line-height: 1.6;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: var(--radius-md);
        font-size: 0.9375rem;
        margin-top: 6px;
        transition: border-color 0.2s;

        &:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        &[readonly] {
            background-color: var(--bg-secondary);
            opacity: 0.7;
        }
    }

    .error-text {
        color: var(--danger-color);
        font-size: 0.75rem;
        margin-top: 6px;
    }

    .form-actions {
        margin-bottom: 24px;
        margin-top: 32px;
    }

    .reset-btn {
        width: 100%;
        padding: 12px;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: var(--radius-md);
        font-size: 0.9375rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
            background-color: var(--secondary-color);
        }

        &.disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
    }

    .form-footer {
        text-align: center;
        font-size: 0.875rem;
        margin-top: 16px;

        .back-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}
</style>
