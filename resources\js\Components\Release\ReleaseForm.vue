<script setup>
import { ref, computed, watch } from "vue";
import { useForm } from "@inertiajs/vue3";
import {
    Upload,
    Music,
    Calendar,
    Globe,
    Info,
    X,
    Check,
    AlertCircle,
    Loader2,
    Image as ImageIcon,
} from "lucide-vue-next";

const props = defineProps({
    release: {
        type: Object,
        default: null
    },
    genres: {
        type: Object,
        required: true
    },
    languages: {
        type: Object,
        required: true
    },
    isEditing: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['submit', 'cancel']);

// Типы релизов
const releaseTypes = [
    { value: "album", label: "Альбом" },
    { value: "ep", label: "EP" },
    { value: "single", label: "Сингл" },
];

// Форма
const form = useForm({
    title: props.release?.title || "",
    description: props.release?.description || "",
    type: props.release?.type || "single",
    genre: props.release?.genre || "",
    language: props.release?.language || "ru",
    is_explicit: props.release?.is_explicit || false,
    release_date: props.release?.release_date || new Date().toISOString().substring(0, 10),
    cover_image: null,
});

// Состояние обложки
const coverPreview = ref(props.release?.cover_image ? `/storage/${props.release.cover_image}` : null);
const imageError = ref("");
const isDragOver = ref(false);

// Валидация формы
const isValid = computed(() => {
    return form.title && 
           form.genre && 
           form.language && 
           !imageError.value &&
           (props.isEditing || form.cover_image); // Обложка обязательна только при создании
});

// Обработка загрузки файла
function handleFileUpload(event) {
    const file = event.target.files[0];
    if (file) {
        processFile(file);
    }
}

// Обработка drag & drop
function handleDrop(event) {
    event.preventDefault();
    isDragOver.value = false;
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    isDragOver.value = true;
}

function handleDragLeave() {
    isDragOver.value = false;
}

// Обработка файла
function processFile(file) {
    imageError.value = "";

    // Проверка типа файла
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        imageError.value = "Поддерживаются только файлы JPEG, PNG и WebP";
        return;
    }

    // Проверка размера файла (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
        imageError.value = "Размер файла не должен превышать 5MB";
        return;
    }

    // Установка файла
    form.cover_image = file;

    // Создание превью
    const reader = new FileReader();
    reader.onload = (e) => {
        coverPreview.value = e.target.result;
    };
    reader.readAsDataURL(file);
}

// Удаление обложки
function removeCover() {
    form.cover_image = null;
    coverPreview.value = props.release?.cover_image ? `/storage/${props.release.cover_image}` : null;
    imageError.value = "";
    
    const fileInput = document.getElementById("cover_image");
    if (fileInput) fileInput.value = "";
}

// Отправка формы
function submitForm() {
    if (!isValid.value) return;
    
    emit('submit', form);
}

// Отмена
function cancelForm() {
    emit('cancel');
}

// Форматирование даты для отображения
const formattedDate = computed(() => {
    if (!form.release_date) return '';
    return new Date(form.release_date).toLocaleDateString('ru-RU');
});
</script>

<template>
    <form @submit.prevent="submitForm" class="release-form">
        <div class="form-grid">
            <!-- Левая колонка: детали релиза -->
            <div class="form-column">
                <h3 class="section-title">Информация о релизе</h3>

                <!-- Название релиза -->
                <div class="form-group">
                    <label for="title" class="form-label">
                        Название релиза *
                    </label>
                    <input
                        id="title"
                        v-model="form.title"
                        type="text"
                        class="form-input"
                        :class="{ error: form.errors.title }"
                        placeholder="Введите название релиза"
                        required
                    />
                    <p v-if="form.errors.title" class="error-text">
                        {{ form.errors.title }}
                    </p>
                </div>

                <!-- Описание -->
                <div class="form-group">
                    <label for="description" class="form-label">
                        Описание
                    </label>
                    <textarea
                        id="description"
                        v-model="form.description"
                        class="form-textarea"
                        :class="{ error: form.errors.description }"
                        placeholder="Расскажите о вашем релизе..."
                        rows="4"
                    ></textarea>
                    <p v-if="form.errors.description" class="error-text">
                        {{ form.errors.description }}
                    </p>
                </div>

                <!-- Тип релиза -->
                <div class="form-group">
                    <label for="type" class="form-label">
                        Тип релиза *
                    </label>
                    <select
                        id="type"
                        v-model="form.type"
                        class="form-select"
                        :class="{ error: form.errors.type }"
                        required
                    >
                        <option
                            v-for="type in releaseTypes"
                            :key="type.value"
                            :value="type.value"
                        >
                            {{ type.label }}
                        </option>
                    </select>
                    <p v-if="form.errors.type" class="error-text">
                        {{ form.errors.type }}
                    </p>
                </div>

                <!-- Жанр -->
                <div class="form-group">
                    <label for="genre" class="form-label">
                        Жанр *
                    </label>
                    <select
                        id="genre"
                        v-model="form.genre"
                        class="form-select"
                        :class="{ error: form.errors.genre }"
                        required
                    >
                        <option value="">Выберите жанр</option>
                        <option
                            v-for="(label, value) in genres"
                            :key="value"
                            :value="value"
                        >
                            {{ label }}
                        </option>
                    </select>
                    <p v-if="form.errors.genre" class="error-text">
                        {{ form.errors.genre }}
                    </p>
                </div>

                <!-- Язык -->
                <div class="form-group">
                    <label for="language" class="form-label">
                        Язык *
                    </label>
                    <select
                        id="language"
                        v-model="form.language"
                        class="form-select"
                        :class="{ error: form.errors.language }"
                        required
                    >
                        <option
                            v-for="(label, value) in languages"
                            :key="value"
                            :value="value"
                        >
                            {{ label }}
                        </option>
                    </select>
                    <p v-if="form.errors.language" class="error-text">
                        {{ form.errors.language }}
                    </p>
                </div>

                <!-- Дата релиза -->
                <div class="form-group">
                    <label for="release_date" class="form-label">
                        Дата релиза *
                    </label>
                    <input
                        id="release_date"
                        v-model="form.release_date"
                        type="date"
                        class="form-input"
                        :class="{ error: form.errors.release_date }"
                        required
                    />
                    <p v-if="form.errors.release_date" class="error-text">
                        {{ form.errors.release_date }}
                    </p>
                    <p v-else class="form-hint">
                        <Calendar size="14" class="hint-icon" />
                        Будет опубликован: {{ formattedDate }}
                    </p>
                </div>

                <!-- Explicit контент -->
                <div class="form-group">
                    <label class="checkbox-label">
                        <input
                            v-model="form.is_explicit"
                            type="checkbox"
                            class="checkbox-input"
                        />
                        <span class="checkbox-custom"></span>
                        <span class="checkbox-text">
                            Содержит нецензурную лексику
                        </span>
                    </label>
                    <p class="form-hint">
                        <Info size="14" class="hint-icon" />
                        Отметьте, если релиз содержит нецензурную лексику
                    </p>
                </div>
            </div>

            <!-- Правая колонка: обложка -->
            <div class="form-column">
                <h3 class="section-title">Обложка релиза</h3>

                <div class="cover-upload-section">
                    <!-- Превью обложки -->
                    <div v-if="coverPreview" class="cover-preview">
                        <img :src="coverPreview" alt="Обложка релиза" class="cover-image" />
                        <button
                            type="button"
                            class="remove-cover-btn"
                            @click="removeCover"
                            title="Удалить обложку"
                        >
                            <X size="16" />
                        </button>
                    </div>

                    <!-- Область загрузки -->
                    <div
                        v-else
                        class="cover-upload-area"
                        :class="{ 
                            'drag-over': isDragOver,
                            'error': form.errors.cover_image || imageError 
                        }"
                        @drop="handleDrop"
                        @dragover="handleDragOver"
                        @dragleave="handleDragLeave"
                        @click="$refs.coverInput.click()"
                    >
                        <div class="upload-content">
                            <ImageIcon class="upload-icon" size="48" />
                            <p class="upload-text">
                                Перетащите изображение сюда или нажмите для выбора
                            </p>
                            <p class="upload-hint">
                                JPEG, PNG, WebP • Макс. 5MB • Рекомендуется 1000×1000px
                            </p>
                        </div>
                        
                        <input
                            ref="coverInput"
                            id="cover_image"
                            type="file"
                            class="file-input"
                            accept="image/jpeg,image/png,image/webp"
                            @change="handleFileUpload"
                        />
                    </div>

                    <!-- Ошибки -->
                    <p v-if="form.errors.cover_image" class="error-text">
                        {{ form.errors.cover_image }}
                    </p>
                    <p v-else-if="imageError" class="error-text">
                        {{ imageError }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Кнопки формы -->
        <div class="form-actions">
            <button
                type="button"
                class="btn btn-outline"
                @click="cancelForm"
            >
                Отмена
            </button>
            <button
                type="submit"
                class="btn btn-primary"
                :disabled="!isValid || form.processing"
            >
                <Loader2
                    v-if="form.processing"
                    class="btn-icon spin"
                    size="16"
                />
                <Music
                    v-else
                    class="btn-icon"
                    size="16"
                />
                <span v-if="form.processing">
                    {{ isEditing ? 'Сохранение...' : 'Создание...' }}
                </span>
                <span v-else>
                    {{ isEditing ? 'Сохранить изменения' : 'Создать релиз' }}
                </span>
            </button>
        </div>
    </form>
</template>

<style lang="scss" scoped>
.release-form {
    max-width: 1200px;
    margin: 0 auto;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 40px;
    margin-bottom: 32px;

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 24px;
    }
}

.form-column {
    display: flex;
    flex-direction: column;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 24px;
    color: var(--text-primary);
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }

    &.error {
        border-color: var(--danger-color);
    }
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
}

.checkbox-input {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;

    &::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 6px;
        width: 6px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        opacity: 0;
        transition: opacity 0.2s ease;
    }
}

.checkbox-input:checked + .checkbox-custom {
    background-color: var(--primary-color);
    border-color: var(--primary-color);

    &::after {
        opacity: 1;
    }
}

.checkbox-text {
    font-size: 0.875rem;
    color: var(--text-primary);
}

.form-hint {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 4px;
}

.hint-icon {
    flex-shrink: 0;
}

.error-text {
    color: var(--danger-color);
    font-size: 0.75rem;
    margin-top: 4px;
}

.cover-upload-section {
    position: relative;
}

.cover-preview {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    aspect-ratio: 1;
    background: var(--bg-secondary);
}

.cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-cover-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.9);
    }
}

.cover-upload-area {
    border: 2px dashed rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-lg);
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover,
    &.drag-over {
        border-color: var(--primary-color);
        background: rgba(var(--primary-rgb), 0.05);
    }

    &.error {
        border-color: var(--danger-color);
        background: rgba(244, 67, 54, 0.05);
    }
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.upload-icon {
    color: var(--text-secondary);
}

.upload-text {
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.upload-hint {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

.file-input {
    display: none;
}

.form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    padding-top: 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.btn {
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    border: none;

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.btn-outline {
    background: transparent;
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: var(--text-primary);

    &:hover:not(:disabled) {
        background: rgba(0, 0, 0, 0.05);
    }
}

.btn-primary {
    background: var(--primary-color);
    color: white;

    &:hover:not(:disabled) {
        background: var(--primary-hover);
    }
}

.btn-icon {
    flex-shrink: 0;
}

.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
