<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class Application extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'type',
        'title',
        'description',
        'status',
        'admin_notes',
        'attachments',
        'reviewed_by',
        'reviewed_at',
        'is_read',
        'read_at',
        'read_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'attachments' => 'json',
        'reviewed_at' => 'datetime',
        'read_at' => 'datetime',
        'is_read' => 'boolean',
    ];

    /**
     * Get the user that owns the application.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the admin that reviewed the application.
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get the user who read this application.
     */
    public function reader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'read_by');
    }

    /**
     * Mark application as read by current user.
     */
    public function markAsRead(?int $userId = null): void
    {
        $this->update([
            'is_read' => true,
            'read_at' => now(),
            'read_by' => $userId ?? Auth::id(),
        ]);
    }

    /**
     * Check if application is unread.
     */
    public function isUnread(): bool
    {
        return !$this->is_read;
    }

    /**
     * Scope a query to only include applications with the given status.
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include applications of the given type.
     */
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Mark the application as reviewed.
     */
    public function markAsReviewed(User $admin, string $status, ?string $notes = null): self
    {
        $this->update([
            'status' => $status,
            'admin_notes' => $notes,
            'reviewed_at' => now(),
            'reviewed_by' => $admin->id,
        ]);

        return $this;
    }
}
