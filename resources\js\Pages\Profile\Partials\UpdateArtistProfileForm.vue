<script setup>
import { ref, computed } from "vue";
import { useForm, usePage } from "@inertiajs/vue3";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import InputLabel from "@/Components/InputLabel.vue";
import TextInput from "@/Components/TextInput.vue";
import InputError from "@/Components/InputError.vue";

const user = computed(() => usePage().props.auth.user);
const artist = computed(() => user.value.artist || null);

const form = useForm({
    stage_name: artist.value ? artist.value.stage_name : "",
    bio: artist.value ? artist.value.bio : "",
    genre: artist.value ? artist.value.genre : "",
    location: artist.value ? artist.value.location : "",
    website: artist.value ? artist.value.website : "",
    social_links:
        artist.value && artist.value.social_links
            ? artist.value.social_links
            : {
                  instagram: "",
                  twitter: "",
                  facebook: "",
                  spotify: "",
                  youtube: "",
              },
    cover_image: null,
});

const coverPreview = ref(
    artist.value && artist.value.cover_image_url
        ? artist.value.cover_image_url
        : null
);

const updateArtistProfile = () => {
    if (photoInput.value) {
        form.cover_image = photoInput.value.files[0];
    }

    form.post(route("artist-profile.update"), {
        preserveScroll: true,
        onSuccess: () => {
            photoInput.value = null;
        },
        onError: () => {
            if (photoInput.value) {
                photoInput.value.value = "";
            }
        },
    });
};

const photoInput = ref(null);
const hasExistingArtist = computed(() => artist.value !== null);

const selectNewCoverImage = () => {
    photoInput.value.click();
};

const updateCoverPreview = () => {
    const file = photoInput.value.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
        coverPreview.value = e.target.result;
    };
    reader.readAsDataURL(file);
};
</script>

<template>
    <div v-if="!hasExistingArtist" class="create-artist-message">
        <h3>Создание профиля артиста</h3>
        <p>
            У вас еще нет профиля артиста. Заполните форму ниже, чтобы создать
            свой профиль.
        </p>
    </div>

    <form @submit.prevent="updateArtistProfile">
        <div class="form-section">
            <div class="cover-image-section">
                <div class="cover-image-container">
                    <img
                        v-if="coverPreview"
                        :src="coverPreview"
                        alt="Обложка профиля"
                        class="cover-image-preview"
                    />
                    <div v-else class="cover-image-placeholder">
                        <span>Загрузите обложку</span>
                    </div>
                    <input
                        ref="photoInput"
                        type="file"
                        class="hidden-input"
                        @change="updateCoverPreview"
                    />
                    <button
                        type="button"
                        class="change-image-btn"
                        @click="selectNewCoverImage"
                    >
                        Изменить обложку
                    </button>
                </div>
            </div>

            <div class="form-group">
                <InputLabel for="stage_name" value="Сценическое имя" />
                <TextInput
                    id="stage_name"
                    v-model="form.stage_name"
                    type="text"
                    class="form-input"
                    autocomplete="stage_name"
                />
                <InputError
                    :message="form.errors.stage_name"
                    class="error-message"
                />
            </div>

            <div class="form-group">
                <InputLabel for="genre" value="Жанр" />
                <TextInput
                    id="genre"
                    v-model="form.genre"
                    type="text"
                    class="form-input"
                    autocomplete="genre"
                />
                <InputError
                    :message="form.errors.genre"
                    class="error-message"
                />
            </div>

            <div class="form-group">
                <InputLabel for="location" value="Местоположение" />
                <TextInput
                    id="location"
                    v-model="form.location"
                    type="text"
                    class="form-input"
                    autocomplete="location"
                />
                <InputError
                    :message="form.errors.location"
                    class="error-message"
                />
            </div>

            <div class="form-group">
                <InputLabel for="website" value="Веб-сайт" />
                <TextInput
                    id="website"
                    v-model="form.website"
                    type="text"
                    class="form-input"
                    autocomplete="website"
                />
                <InputError
                    :message="form.errors.website"
                    class="error-message"
                />
            </div>

            <div class="form-group full-width">
                <InputLabel for="bio" value="Биография" />
                <textarea
                    id="bio"
                    v-model="form.bio"
                    class="form-textarea"
                    rows="4"
                ></textarea>
                <InputError :message="form.errors.bio" class="error-message" />
            </div>

            <h3 class="section-title">Социальные сети</h3>

            <div class="form-group">
                <InputLabel for="instagram" value="Instagram" />
                <TextInput
                    id="instagram"
                    v-model="form.social_links.instagram"
                    type="text"
                    class="form-input"
                />
            </div>

            <div class="form-group">
                <InputLabel for="spotify" value="Spotify" />
                <TextInput
                    id="spotify"
                    v-model="form.social_links.spotify"
                    type="text"
                    class="form-input"
                />
            </div>

            <div class="form-group">
                <InputLabel for="youtube" value="YouTube" />
                <TextInput
                    id="youtube"
                    v-model="form.social_links.youtube"
                    type="text"
                    class="form-input"
                />
            </div>

            <div class="form-actions">
                <PrimaryButton
                    type="submit"
                    :class="{ 'opacity-25': form.processing }"
                    :disabled="form.processing"
                >
                    {{
                        hasExistingArtist
                            ? "Обновить профиль"
                            : "Создать профиль"
                    }}
                </PrimaryButton>
            </div>
        </div>
    </form>
</template>

<style scoped>
.create-artist-message {
    margin-bottom: 24px;
    padding: 16px;
    background-color: var(--info-bg);
    border-left: 3px solid var(--info-color);
    border-radius: var(--radius-md);
}

.create-artist-message h3 {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 1rem;
}

.create-artist-message p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.form-section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
}

.cover-image-section {
    grid-column: 1 / -1;
    margin-bottom: 16px;
}

.cover-image-container {
    position: relative;
    width: 100%;
    max-width: 300px;
    border-radius: var(--radius-md);
    overflow: hidden;
}

.cover-image-preview {
    width: 100%;
    height: auto;
    display: block;
}

.cover-image-placeholder {
    width: 100%;
    height: 200px;
    background-color: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-tertiary);
    border: 1px dashed var(--border-color);
}

.hidden-input {
    display: none;
}

.change-image-btn {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: none;
    border-radius: var(--radius-md);
    padding: 8px 16px;
    font-size: 0.875rem;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.form-group {
    margin-bottom: 16px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-top: 4px;
}

.form-textarea {
    resize: vertical;
}

.error-message {
    color: var(--danger-color);
    font-size: 0.75rem;
    margin-top: 4px;
}

.section-title {
    grid-column: 1 / -1;
    margin: 16px 0 8px;
    font-size: 1rem;
    font-weight: 600;
}

.form-actions {
    grid-column: 1 / -1;
    margin-top: 24px;
}
</style>
