<script setup>
import { Link, usePage } from "@inertiajs/vue3";
import {
    Bell,
    Menu,
    ChevronRight,
    PanelLeft,
    Moon,
    Sun,
    Search,
    Check,
    X,
    InfoIcon,
} from "lucide-vue-next";
import { useThemeStore } from "@/Stores/theme";
import NotificationDropdown from "@/Components/Notifications/NotificationDropdown.vue";
import { ref, computed, onMounted } from "vue";

const props = defineProps({
    title: {
        type: String,
        required: true,
    },
    breadcrumbs: {
        type: Array,
        default: () => [],
    },
    sidebarCollapsed: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(["toggleSidebar", "toggleMobileMenu"]);

// Хранилища
const themeStore = useThemeStore();

// Состояние для уведомлений
const showNotifications = ref(false);
const notifications = ref([]);
const unreadCount = ref(0);

// Получаем текущего пользователя
const user = computed(() => usePage().props.auth.user);

// Переключение темы
const toggleTheme = () => {
    themeStore.toggleTheme();
};

// Переключение сайдбара
const toggleSidebar = () => {
    emit("toggleSidebar");
};

// Открытие мобильного меню
const toggleMobileMenu = () => {
    emit("toggleMobileMenu");
};

// Переключение уведомлений
const toggleNotifications = () => {
    showNotifications.value = !showNotifications.value;
    if (showNotifications.value) {
        loadNotifications();
    }
};

// Закрытие уведомлений
const closeNotifications = () => {
    showNotifications.value = false;
};

// Загрузка уведомлений
const loadNotifications = async () => {
    try {
        const response = await fetch("/api/notifications");
        const data = await response.json();
        notifications.value = data.notifications || [];
        unreadCount.value = data.unreadCount || 0;
    } catch (error) {
        console.error("Ошибка загрузки уведомлений:", error);
    }
};

// Отметить как прочитанное
const markAsRead = async (notification) => {
    try {
        await fetch(`/api/notifications/${notification.id}/read`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                ).content,
            },
        });

        notification.is_read = true;
        notification.read_at = new Date().toISOString();
        unreadCount.value = Math.max(0, unreadCount.value - 1);
    } catch (error) {
        console.error("Ошибка отметки уведомления:", error);
    }
};

// Отметить все как прочитанные
const markAllAsRead = async () => {
    try {
        await fetch("/api/notifications/mark-all-read", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                ).content,
            },
        });

        notifications.value.forEach((n) => {
            n.is_read = true;
            n.read_at = new Date().toISOString();
        });
        unreadCount.value = 0;
    } catch (error) {
        console.error("Ошибка отметки всех уведомлений:", error);
    }
};

// Удалить уведомление
const deleteNotification = async (notification) => {
    try {
        await fetch(`/api/notifications/${notification.id}`, {
            method: "DELETE",
            headers: {
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                ).content,
            },
        });

        const index = notifications.value.findIndex(
            (n) => n.id === notification.id
        );
        if (index > -1) {
            if (!notification.is_read) {
                unreadCount.value = Math.max(0, unreadCount.value - 1);
            }
            notifications.value.splice(index, 1);
        }
    } catch (error) {
        console.error("Ошибка удаления уведомления:", error);
    }
};

// Получаем текущие flash-сообщения из страницы
const flash = computed(() => usePage().props.flash);
const showFlash = ref(false);
const flashType = ref("");
const flashMessage = ref("");

// Отображение flash-сообщений и инициализация уведомлений
onMounted(async () => {
    // Flash-сообщения
    if (flash.value && flash.value.success) {
        showFlash.value = true;
        flashType.value = "success";
        flashMessage.value = flash.value.success;
    } else if (flash.value && flash.value.error) {
        showFlash.value = true;
        flashType.value = "error";
        flashMessage.value = flash.value.error;
    } else if (flash.value && flash.value.info) {
        showFlash.value = true;
        flashType.value = "info";
        flashMessage.value = flash.value.info;
    }

    // Автоматическое закрытие через 5 секунд
    if (showFlash.value) {
        setTimeout(() => {
            showFlash.value = false;
        }, 5000);
    }

    // Инициализация уведомлений
    if (user.value) {
        // Получаем начальный счетчик из props
        const page = usePage();
        unreadCount.value = page.props.unreadApplicationsCount || 0;
    }
});

const closeFlash = () => {
    showFlash.value = false;
};

// Получаем текущую тему
const isDarkTheme = computed(() => usePage().props.theme === "dark");
</script>

<template>
    <header class="main-header">
        <!-- Flash-сообщения -->
        <div v-if="showFlash" class="flash-message" :class="flashType">
            <div class="flash-content">
                <InfoIcon
                    v-if="flashType === 'info'"
                    class="flash-icon"
                    size="18"
                />
                <Check
                    v-else-if="flashType === 'success'"
                    class="flash-icon"
                    size="18"
                />
                <X
                    v-else-if="flashType === 'error'"
                    class="flash-icon"
                    size="18"
                />
                <span>{{ flashMessage }}</span>
            </div>
            <button @click="closeFlash" class="flash-close">
                <X size="16" />
            </button>
        </div>

        <!-- Левая часть хедера -->
        <div class="header-left">
            <!-- Кнопка мобильного меню -->
            <button class="mobile-menu-button" @click="toggleMobileMenu">
                <Menu size="24" />
            </button>

            <!-- Кнопка сворачивания сайдбара -->
            <button class="sidebar-toggle" @click="toggleSidebar">
                <PanelLeft
                    size="20"
                    :class="{ 'icon-rotated': sidebarCollapsed }"
                />
            </button>

            <!-- Заголовок страницы и хлебные крошки -->
            <div class="header-title-section">
                <h1 class="header-title">{{ title }}</h1>

                <!-- Хлебные крошки -->
                <div class="breadcrumbs" v-if="breadcrumbs.length > 0">
                    <Link href="/dashboard" class="breadcrumb-item">
                        Главная
                    </Link>
                    <ChevronRight class="breadcrumb-separator" size="16" />

                    <template
                        v-for="(crumb, index) in breadcrumbs"
                        :key="index"
                    >
                        <Link
                            v-if="crumb.link && index < breadcrumbs.length - 1"
                            :href="crumb.link"
                            class="breadcrumb-item"
                        >
                            {{ crumb.name }}
                        </Link>
                        <span v-else class="breadcrumb-item current">
                            {{ crumb.name }}
                        </span>

                        <ChevronRight
                            v-if="index < breadcrumbs.length - 1"
                            class="breadcrumb-separator"
                            size="16"
                        />
                    </template>
                </div>
            </div>
        </div>

        <!-- Правая часть хедера -->
        <div class="header-right">
            <!-- Поиск -->
            <div class="search-container animate-fade-in animate-delay-200">
                <div class="search-input-wrapper">
                    <Search class="search-icon" size="18" />
                    <input
                        type="text"
                        placeholder="Поиск..."
                        class="search-input focus-glow"
                        @focus="$event.target.parentElement.classList.add('focused')"
                        @blur="$event.target.parentElement.classList.remove('focused')"
                    />
                    <kbd class="search-shortcut">⌘K</kbd>
                </div>
            </div>

            <!-- Переключатель темы -->
            <button class="action-button theme-toggle hover-scale animate-fade-in animate-delay-300" @click="toggleTheme" title="Переключить тему">
                <div class="theme-icon-wrapper">
                    <Moon v-if="themeStore.theme === 'light'" class="theme-icon" size="20" />
                    <Sun v-else class="theme-icon" size="20" />
                </div>
            </button>

            <!-- Уведомления -->
            <div class="notifications-container animate-fade-in animate-delay-500">
                <button
                    class="action-button notification-button hover-scale"
                    @click="toggleNotifications"
                    :class="{
                        'has-notifications': unreadCount > 0,
                    }"
                    title="Уведомления"
                >
                    <div class="notification-icon-wrapper">
                        <Bell class="notification-icon" size="20" />
                        <span v-if="unreadCount > 0" class="notification-badge animate-pulse">
                            {{ unreadCount > 99 ? "99+" : unreadCount }}
                        </span>
                    </div>
                </button>

                <!-- Dropdown уведомлений -->
                <NotificationDropdown
                    :show="showNotifications"
                    :notifications="notifications"
                    @close="closeNotifications"
                    @mark-as-read="markAsRead"
                    @mark-all-as-read="markAllAsRead"
                    @delete="deleteNotification"
                />
            </div>

            <!-- Профиль пользователя -->
            <div class="user-profile-container">
                <button class="user-profile-button" title="Профиль">
                    <div class="user-avatar-small">
                        <img :src="user.profile_photo_url" :alt="user.name" />
                    </div>
                    <div class="user-info-small">
                        <span class="user-name-small">{{ user.name }}</span>
                        <span class="user-role-small">
                            {{
                                user.role === "admin"
                                    ? "Администратор"
                                    : user.role === "artist"
                                    ? "Артист"
                                    : "Менеджер"
                            }}
                        </span>
                    </div>
                </button>
            </div>
        </div>
    </header>
</template>

<style lang="scss">
.main-header {
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;
    background: var(--bg-gradient-surface);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-sticky);
    position: sticky;
    top: 0;
    transition: all var(--transition-normal);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        z-index: -1;
    }
}

.header-left {
    display: flex;
    align-items: center;
    flex: 1;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

// Поиск - современный дизайн
.search-container {
    flex: 1;
    max-width: 400px;
    margin: 0 32px;

    @media (max-width: 768px) {
        display: none;
    }
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: 0 16px;
    transition: all var(--transition-normal);

    &:hover {
        border-color: var(--border-medium);
        box-shadow: var(--shadow-sm);
    }

    &.focused {
        border-color: var(--primary-color);
        box-shadow: var(--shadow-glow);
        background: var(--bg-primary);
    }
}

.search-icon {
    color: var(--text-tertiary);
    margin-right: 12px;
    transition: color var(--transition-fast);

    .search-input-wrapper.focused & {
        color: var(--primary-color);
    }
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 12px 0;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    outline: none;

    &::placeholder {
        color: var(--text-tertiary);
    }
}

.search-shortcut {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    padding: 2px 6px;
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    font-family: inherit;
    margin-left: 8px;
}

.mobile-menu-button {
    display: none;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    margin-right: 16px;
    padding: 5px;
    border-radius: var(--radius-sm);

    &:hover {
        background-color: var(--bg-secondary);
    }
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    margin-right: 16px;
    padding: 5px;
    border-radius: var(--radius-sm);

    &:hover {
        background-color: var(--bg-secondary);
    }
}

.icon-rotated {
    transform: rotate(180deg);
}

.header-title-section {
    display: flex;
    flex-direction: column;
}

.header-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.breadcrumbs {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 2px;
}

.breadcrumb-item {
    color: var(--text-secondary);
    text-decoration: none;

    &:hover:not(.current) {
        color: var(--primary-color);
        text-decoration: underline;
    }

    &.current {
        font-weight: 500;
        color: var(--text-primary);
    }
}

.breadcrumb-separator {
    margin: 0 4px;
    color: var(--text-disabled);
}

.action-button {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    cursor: pointer;
    border-radius: var(--radius-lg);
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all var(--transition-normal);
    min-width: 48px;
    min-height: 48px;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--primary-gradient);
        opacity: 0;
        border-radius: inherit;
        transition: opacity var(--transition-normal);
        z-index: -1;
    }

    &:hover {
        background: var(--bg-primary);
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);

        &::before {
            opacity: 0.1;
        }
    }

    &:active {
        transform: translateY(0);
    }
}

// Переключатель темы
.theme-toggle {
    .theme-icon-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .theme-icon {
        transition: all var(--transition-bounce);
    }

    &:hover .theme-icon {
        transform: rotate(180deg) scale(1.1);
        color: var(--primary-color);
    }
}

.notifications-container {
    position: relative;
}

.notification-button {
    position: relative;

    .notification-icon-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .notification-icon {
        transition: all var(--transition-bounce);
    }

    &:hover .notification-icon {
        transform: scale(1.1);
        color: var(--primary-color);
    }

    &.has-notifications {
        .notification-icon {
            animation: bellRing 2s infinite;
        }
    }
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--danger-gradient);
    color: var(--text-inverse);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    border: 2px solid var(--bg-primary);
    box-shadow: var(--shadow-md);
    animation: pulse 2s infinite;
}

@keyframes bellRing {
    0%, 50%, 100% {
        transform: rotate(0deg);
    }
    10%, 30% {
        transform: rotate(-10deg);
    }
    20%, 40% {
        transform: rotate(10deg);
    }
}

// Профиль пользователя в хедере
.user-profile-container {
    position: relative;
}

.user-profile-button {
    display: flex;
    align-items: center;
    gap: 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: 8px 16px 8px 8px;
    cursor: pointer;
    transition: all var(--transition-normal);

    &:hover {
        background: var(--bg-primary);
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    @media (max-width: 768px) {
        padding: 8px;

        .user-info-small {
            display: none;
        }
    }
}

.user-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    overflow: hidden;
    border: 2px solid var(--border-color);
    transition: all var(--transition-normal);

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .user-profile-button:hover & {
        border-color: var(--primary-color);
        transform: scale(1.05);
    }
}

.user-info-small {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
}

.user-name-small {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.user-role-small {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Адаптивность */
@media (max-width: 768px) {
    .mobile-menu-button {
        display: flex;
    }

    .sidebar-toggle {
        display: none;
    }
}

@media (max-width: 576px) {
    .header-title-section {
        max-width: 200px;
    }

    .header-title {
        font-size: 1rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .breadcrumbs {
        display: none;
    }
}

/* Стили для flash-сообщений */
.flash-message {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);

    &.success {
        background-color: var(--success-bg);
        border-bottom-color: var(--success-color);

        .flash-icon {
            color: var(--success-color);
        }
    }

    &.error {
        background-color: var(--danger-bg);
        border-bottom-color: var(--danger-color);

        .flash-icon {
            color: var(--danger-color);
        }
    }

    &.info {
        background-color: var(--info-bg);
        border-bottom-color: var(--info-color);

        .flash-icon {
            color: var(--info-color);
        }
    }
}

.flash-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
}

.flash-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;

    &:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }
}
</style>
