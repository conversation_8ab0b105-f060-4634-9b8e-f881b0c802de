<?php

namespace App\Http\Controllers;

use App\Models\Release;
use App\Models\Track;
use App\Services\FileManagementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ReleaseController extends Controller
{
    /**
     * Отображение списка релизов.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $query = Release::query()->with('artist');

        // Фильтрация по статусу, если указан
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Фильтрация по типу, если указан
        if ($request->has('type') && $request->type !== 'all') {
            $query->where('type', $request->type);
        }

        // Для обычных пользователей показываем только их релизы
        if (!$user->isAdmin() && !$user->isManager()) {
            $query->whereHas('artist', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            });
        } else {
            // Админы и менеджеры не видят черновики артистов
            $query->where('status', '!=', 'draft');
        }

        // Сортировка
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $releases = $query->paginate(10)->withQueryString();

        return Inertia::render('Releases/Index', [
            'releases' => $releases,
            'filters' => [
                'status' => $request->status ?? 'all',
                'type' => $request->type ?? 'all',
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
        ]);
    }

    /**
     * Отображение формы создания релиза.
     */
    public function create()
    {
        $user = auth()->user();
        
        return Inertia::render('Releases/Create', [
            'artist' => $user->artist,
            'genres' => $this->getGenres(),
            'languages' => $this->getLanguages(),
        ]);
    }

    /**
     * Сохранение нового релиза.
     */
    public function store(Request $request, FileManagementService $fileService)
    {
        $user = $request->user();

        // Валидация основных данных релиза
        $validatedData = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string|in:album,ep,single',
            'genre' => 'required|string|max:100',
            'language' => 'required|string|max:50',
            'is_explicit' => 'boolean',
            'release_date' => 'required|date',
            'cover_image' => 'required_if:status,pending|image|max:5120', // 5MB макс для обложек
            'status' => 'required|in:draft,pending',
        ]);

        // Валидация и сохранение обложки через FileManagementService
        if ($request->hasFile('cover_image')) {
            $coverFile = $request->file('cover_image');

            // Валидация файла
            if (!$fileService->validateFile(
                $coverFile,
                $fileService->getAllowedFileTypes('cover'),
                $fileService->getMaxFileSize('cover')
            )) {
                return redirect()->back()
                    ->withErrors(['cover_image' => 'Неподдерживаемый тип файла или размер превышает лимит.'])
                    ->withInput();
            }

            // Асинхронная загрузка обложки
            $tempPath = $fileService->uploadFileAsync($coverFile, 'images/covers', [
                'release_title' => $validatedData['title'],
                'user_id' => $user->id
            ]);

            // Временно сохраняем путь к временному файлу
            $validatedData['cover_image'] = $tempPath;
        }
        
        // Установка статуса и дополнительной информации
        $validatedData['status'] = 'draft';
        $validatedData['meta_info'] = json_encode([
            'created_by' => $user->name,
            'created_at' => now()->toDateTimeString(),
        ]);

        // Проверка наличия артиста у пользователя
        if (!$user->artist) {
            return redirect()->back()
                ->withErrors(['artist' => 'У вас нет профиля артиста. Создайте его сначала.']);
        }

        // Создание релиза
        $release = $user->artist->releases()->create($validatedData);

        return redirect()->route('releases.show', $release->id)
            ->with('success', 'Релиз создан успешно. Теперь вы можете добавить треки.');
    }

    /**
     * Отображение конкретного релиза.
     */
    public function show(Release $release)
    {
        $release->load(['artist.user', 'tracks' => function($query) {
            $query->orderBy('track_number', 'asc');
        }]);

        // Проверка прав доступа
        $this->authorizeView($release);

        return Inertia::render('Releases/Show', [
            'release' => $release,
            'canEdit' => $this->canEdit($release),
        ]);
    }

    /**
     * Отображение формы редактирования релиза.
     */
    public function edit(Release $release)
    {
        // Проверка прав доступа
        $this->authorizeEdit($release);

        $release->load(['artist.user', 'tracks' => function($query) {
            $query->orderBy('track_number', 'asc');
        }]);

        return Inertia::render('Releases/Edit', [
            'release' => $release,
            'genres' => $this->getGenres(),
            'languages' => $this->getLanguages(),
        ]);
    }

    /**
     * Обновление релиза.
     */
    public function update(Request $request, Release $release)
    {
        // Проверка прав доступа
        $this->authorizeEdit($release);

        // Валидация основных данных релиза
        $validatedData = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'genre' => 'required|string|max:100',
            'language' => 'required|string|max:50',
            'is_explicit' => 'boolean',
            'release_date' => 'required|date',
            'cover_image' => 'nullable|image|max:2048', // 2MB макс
            'status' => 'required|in:draft,pending',
        ]);

        // Обновление обложки, если загружена новая
        if ($request->hasFile('cover_image')) {
            // Удаляем старую обложку, если есть
            if ($release->cover_image) {
                Storage::disk('public')->delete($release->cover_image);
            }

            // Сохраняем новую обложку
            $coverPath = $request->file('cover_image')
                ->store('images/covers/' . $release->id, 'public');
            $validatedData['cover_image'] = $coverPath;
        }

        // Обновление метаданных
        $metaInfo = json_decode($release->meta_info, true) ?: [];
        $metaInfo['updated_at'] = now()->toDateTimeString();
        $metaInfo['updated_by'] = $request->user()->name;
        $validatedData['meta_info'] = json_encode($metaInfo);

        // Обновление релиза
        $release->update($validatedData);

        return redirect()->route('releases.show', $release->id)
            ->with('success', 'Релиз обновлен успешно.');
    }

    /**
     * Удаление релиза.
     */
    public function destroy(Release $release)
    {
        // Проверка прав доступа
        $this->authorizeEdit($release);

        // Удаление связанных файлов
        if ($release->cover_image) {
            Storage::delete('public/' . $release->cover_image);
        }

        // Удаление связанных треков и их файлов
        foreach ($release->tracks as $track) {
            if ($track->file_path) {
                Storage::delete('public/' . $track->file_path);
            }
            $track->delete();
        }

        // Удаление релиза
        $release->delete();

        return redirect()->route('releases.index')
            ->with('success', 'Релиз успешно удален.');
    }

    /**
     * Изменение статуса релиза (только для админов и менеджеров).
     */
    public function updateStatus(Request $request, Release $release)
    {
        // Проверка наличия прав администратора или менеджера
        if (!$request->user()->isAdmin() && !$request->user()->isManager()) {
            return redirect()->back()->with('error', 'Недостаточно прав для выполнения действия.');
        }

        // Валидация
        $validatedData = $request->validate([
            'status' => 'required|string|in:draft,pending,approved,rejected',
            'admin_notes' => 'nullable|string',
        ]);

        // Обновление статуса и заметок
        $release->status = $validatedData['status'];
        
        // Обновление метаданных
        $metaInfo = json_decode($release->meta_info, true) ?: [];
        $metaInfo['status_updated_at'] = now()->toDateTimeString();
        $metaInfo['status_updated_by'] = $request->user()->name;
        
        if (!empty($validatedData['admin_notes'])) {
            $metaInfo['admin_notes'] = $validatedData['admin_notes'];
        }
        
        $release->meta_info = json_encode($metaInfo);
        $release->save();

        return redirect()->back()->with('success', 'Статус релиза обновлен.');
    }

    /**
     * Список стандартных жанров для выбора.
     */
    private function getGenres()
    {
        return [
            'pop' => 'Поп',
            'rock' => 'Рок',
            'hip-hop' => 'Хип-хоп',
            'rnb' => 'R&B',
            'electronic' => 'Электронная',
            'dance' => 'Танцевальная',
            'indie' => 'Инди',
            'classical' => 'Классическая',
            'jazz' => 'Джаз',
            'folk' => 'Фолк',
            'reggae' => 'Регги',
            'country' => 'Кантри',
            'metal' => 'Метал',
            'blues' => 'Блюз',
            'punk' => 'Панк',
            'ambient' => 'Эмбиент',
            'soundtrack' => 'Саундтрек',
            'world' => 'Этническая музыка',
            'other' => 'Другое',
        ];
    }

    /**
     * Список языков для выбора.
     */
    private function getLanguages()
    {
        return [
            'ru' => 'Русский',
            'en' => 'Английский',
            'fr' => 'Французский',
            'de' => 'Немецкий',
            'es' => 'Испанский',
            'it' => 'Итальянский',
            'instrumental' => 'Инструментальная музыка',
            'other' => 'Другой',
        ];
    }

    /**
     * Проверка права на просмотр релиза.
     */
    private function authorizeView(Release $release)
    {
        $user = auth()->user();
        
        // Админы и менеджеры могут видеть все релизы
        if ($user->isAdmin() || $user->isManager()) {
            return true;
        }
        
        // Обычные пользователи могут видеть только свои релизы
        if ($release->artist->user_id !== $user->id) {
            abort(403, 'Доступ запрещен.');
        }
        
        return true;
    }

    /**
     * Проверка права на редактирование релиза.
     */
    private function authorizeEdit(Release $release)
    {
        $user = auth()->user();
        
        // Админы и менеджеры могут редактировать все релизы
        if ($user->isAdmin() || $user->isManager()) {
            return true;
        }
        
        // Обычные пользователи могут редактировать только свои релизы в статусе черновик
        if ($release->artist->user_id !== $user->id || $release->status !== 'draft') {
            abort(403, 'Доступ запрещен или релиз уже отправлен на модерацию.');
        }
        
        return true;
    }

    /**
     * Проверка возможности редактирования релиза для фронтенда.
     */
    private function canEdit(Release $release)
    {
        $user = auth()->user();
        
        // Админы и менеджеры могут редактировать все релизы
        if ($user->isAdmin() || $user->isManager()) {
            return true;
        }
        
        // Обычные пользователи могут редактировать только свои релизы в статусе черновик
        return $release->artist->user_id === $user->id && $release->status === 'draft';
    }
} 