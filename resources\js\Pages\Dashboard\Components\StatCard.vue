<script setup>
import { computed } from "vue";
import { ArrowUpRight, ArrowDownRight, Minus } from "lucide-vue-next";

const props = defineProps({
    title: {
        type: String,
        required: true,
    },
    value: {
        type: [String, Number],
        required: true,
    },
    icon: {
        type: Object,
        default: null,
    },
    change: {
        type: Number,
        default: null,
    },
    changeType: {
        type: String,
        default: "neutral", // 'positive', 'negative', 'neutral'
    },
});

// Определяем иконку для изменения
const changeIcon = computed(() => {
    switch (props.changeType) {
        case "positive":
            return ArrowUpRight;
        case "negative":
            return ArrowDownRight;
        default:
            return Minus;
    }
});

// Определяем цвет для изменения
const changeClass = computed(() => {
    switch (props.changeType) {
        case "positive":
            return "change-positive";
        case "negative":
            return "change-negative";
        default:
            return "change-neutral";
    }
});

// Форматируем значение изменения
const formattedChange = computed(() => {
    if (props.change === null) return null;

    const absChange = Math.abs(props.change);
    return props.changeType === "negative"
        ? `-${absChange}%`
        : props.changeType === "positive"
        ? `+${absChange}%`
        : `${absChange}%`;
});
</script>

<template>
    <div class="stat-card">
        <div class="stat-icon" v-if="icon">
            <component :is="icon" size="24" />
        </div>

        <div class="stat-content">
            <h3 class="stat-title">{{ title }}</h3>
            <div class="stat-value">{{ value }}</div>

            <div
                v-if="change !== null"
                class="stat-change"
                :class="changeClass"
            >
                <component :is="changeIcon" size="16" />
                <span>{{ formattedChange }}</span>
            </div>
        </div>
    </div>
</template>

<style lang="scss">
.stat-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    height: 100%;

    &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
}

.stat-icon {
    background-color: rgba(var(--primary-color-rgb, 63, 81, 181), 0.1);
    color: var(--primary-color);
    border-radius: var(--radius-md);
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-content {
    flex: 1;
}

.stat-title {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0 0 8px 0;
    font-weight: 500;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.875rem;
    font-weight: 500;

    &.change-positive {
        color: var(--success-color);
    }

    &.change-negative {
        color: var(--danger-color);
    }

    &.change-neutral {
        color: var(--text-disabled);
    }
}

/* Адаптивность */
@media (max-width: 576px) {
    .stat-value {
        font-size: 1.25rem;
    }

    .stat-card {
        padding: 16px;
    }

    .stat-icon {
        padding: 8px;
    }
}
</style>
