<?php

namespace App\Jobs;

use App\Models\Release;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use ZipArchive;

class CreateReleaseArchive implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 600; // 10 минут
    public $tries = 2;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $releaseId,
        public string $requestedBy,
        public ?string $format = 'zip'
    ) {
        $this->onQueue('files');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $release = Release::with(['tracks', 'artist'])->findOrFail($this->releaseId);
            
            Log::info("Создание архива для релиза: {$release->title} (ID: {$release->id})");

            $archivePath = $this->createArchive($release);
            
            // Сохраняем информацию об архиве в метаданных релиза
            $this->updateReleaseMetadata($release, $archivePath);

            Log::info("Архив успешно создан: {$archivePath}");

        } catch (\Exception $e) {
            Log::error("Ошибка создания архива для релиза {$this->releaseId}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create archive for the release.
     */
    private function createArchive(Release $release): string
    {
        $archiveName = $this->generateArchiveName($release);
        $archivePath = "archives/{$archiveName}";
        $fullArchivePath = Storage::disk('public')->path($archivePath);

        // Создаем директорию если не существует
        $archiveDir = dirname($fullArchivePath);
        if (!is_dir($archiveDir)) {
            mkdir($archiveDir, 0755, true);
        }

        $zip = new ZipArchive();
        $result = $zip->open($fullArchivePath, ZipArchive::CREATE | ZipArchive::OVERWRITE);

        if ($result !== TRUE) {
            throw new \Exception("Не удалось создать ZIP архив: {$result}");
        }

        try {
            // Добавляем обложку
            if ($release->cover_image) {
                $this->addFileToArchive($zip, $release->cover_image, 'cover.jpg');
            }

            // Добавляем треки
            foreach ($release->tracks as $index => $track) {
                if ($track->file_path) {
                    $trackNumber = str_pad($track->track_number, 2, '0', STR_PAD_LEFT);
                    $fileName = "{$trackNumber}. {$track->title}.mp3";
                    $this->addFileToArchive($zip, $track->file_path, $fileName);
                }
            }

            // Добавляем метаданные
            $this->addMetadataToArchive($zip, $release);

            $zip->close();

            return $archivePath;

        } catch (\Exception $e) {
            $zip->close();
            // Удаляем частично созданный архив
            if (file_exists($fullArchivePath)) {
                unlink($fullArchivePath);
            }
            throw $e;
        }
    }

    /**
     * Add file to archive.
     */
    private function addFileToArchive(ZipArchive $zip, string $filePath, string $archiveFileName): void
    {
        $fullPath = Storage::disk('public')->path($filePath);
        
        if (!file_exists($fullPath)) {
            Log::warning("Файл не найден для добавления в архив: {$filePath}");
            return;
        }

        $zip->addFile($fullPath, $archiveFileName);
    }

    /**
     * Add metadata to archive.
     */
    private function addMetadataToArchive(ZipArchive $zip, Release $release): void
    {
        $metadata = [
            'release' => [
                'title' => $release->title,
                'artist' => $release->artist->stage_name,
                'type' => $release->type,
                'genre' => $release->genre,
                'language' => $release->language,
                'release_date' => $release->release_date?->format('Y-m-d'),
                'is_explicit' => $release->is_explicit,
                'description' => $release->description,
            ],
            'tracks' => $release->tracks->map(function ($track) {
                return [
                    'track_number' => $track->track_number,
                    'title' => $track->title,
                    'duration' => $track->duration,
                    'is_explicit' => $track->is_explicit,
                ];
            })->toArray(),
            'archive_info' => [
                'created_at' => now()->toISOString(),
                'created_by' => $this->requestedBy,
                'format' => $this->format,
            ]
        ];

        $zip->addFromString('metadata.json', json_encode($metadata, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        // Добавляем README файл
        $readme = $this->generateReadme($release);
        $zip->addFromString('README.txt', $readme);
    }

    /**
     * Generate archive filename.
     */
    private function generateArchiveName(Release $release): string
    {
        $artistName = preg_replace('/[^a-zA-Z0-9\-_]/', '_', $release->artist->stage_name);
        $releaseTitle = preg_replace('/[^a-zA-Z0-9\-_]/', '_', $release->title);
        $timestamp = now()->format('Y-m-d_H-i-s');
        
        return "{$artistName}_{$releaseTitle}_{$timestamp}.zip";
    }

    /**
     * Generate README content.
     */
    private function generateReadme(Release $release): string
    {
        return "MADD LABEL - RELEASE ARCHIVE\n" .
               "============================\n\n" .
               "Artist: {$release->artist->stage_name}\n" .
               "Release: {$release->title}\n" .
               "Type: " . ucfirst($release->type) . "\n" .
               "Genre: {$release->genre}\n" .
               "Language: {$release->language}\n" .
               "Release Date: " . ($release->release_date?->format('Y-m-d') ?? 'TBD') . "\n" .
               "Explicit Content: " . ($release->is_explicit ? 'Yes' : 'No') . "\n\n" .
               "Description:\n" .
               ($release->description ?? 'No description provided') . "\n\n" .
               "Tracks:\n" .
               "-------\n" .
               $release->tracks->map(function ($track) {
                   return "{$track->track_number}. {$track->title}" . 
                          ($track->duration ? " ({$track->duration})" : '');
               })->implode("\n") . "\n\n" .
               "Archive created: " . now()->format('Y-m-d H:i:s') . "\n" .
               "Created by: {$this->requestedBy}\n\n" .
               "© Madd Label\n";
    }

    /**
     * Update release metadata with archive information.
     */
    private function updateReleaseMetadata(Release $release, string $archivePath): void
    {
        $metaInfo = json_decode($release->meta_info, true) ?: [];
        
        $metaInfo['archives'] = $metaInfo['archives'] ?? [];
        $metaInfo['archives'][] = [
            'path' => $archivePath,
            'format' => $this->format,
            'created_at' => now()->toISOString(),
            'created_by' => $this->requestedBy,
            'size' => Storage::disk('public')->size($archivePath),
        ];

        $release->update(['meta_info' => json_encode($metaInfo)]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Не удалось создать архив для релиза {$this->releaseId}: " . $exception->getMessage());
    }
}
