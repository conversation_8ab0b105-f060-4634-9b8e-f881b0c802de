<script setup>
import { computed } from "vue";
import { Link } from "@inertiajs/vue3";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import LineChart from "@/Components/Charts/LineChart.vue";
import DoughnutChart from "@/Components/Charts/DoughnutChart.vue";
import {
    Users,
    FileText,
    Music,
    TrendingUp,
    Clock,
    CheckCircle,
    AlertTriangle,
    Eye,
    Settings,
    BarChart3,
    UserCheck,
    Calendar,
} from "lucide-vue-next";

const props = defineProps({
    stats: {
        type: Object,
        required: true,
    },
    pendingApplications: {
        type: Array,
        required: true,
    },
    pendingReleases: {
        type: Array,
        required: true,
    },
    recentUsers: {
        type: Array,
        required: true,
    },
    chartsData: {
        type: Object,
        required: true,
    },
});

// Статистические карточки
const statsCards = computed(() => [
    {
        title: "Заявки на рассмотрении",
        value: props.stats.pending_applications,
        icon: Clock,
        color: "orange",
        link: "/admin/applications?status=pending",
        description: "Требуют внимания",
    },
    {
        title: "Релизы на рассмотрении",
        value: props.stats.pending_releases,
        icon: Music,
        color: "blue",
        link: "/admin/releases?status=pending",
        description: "Ожидают модерации",
    },
    {
        title: "Всего пользователей",
        value: props.stats.total_users,
        icon: Users,
        color: "green",
        link: "/admin/users",
        description: `+${props.stats.new_users_today} сегодня`,
    },
    {
        title: "Активные артисты",
        value: props.stats.active_artists,
        icon: UserCheck,
        color: "purple",
        link: "/admin/users?role=artist",
        description: `из ${props.stats.total_artists} всего`,
    },
]);

// Форматирование даты
function formatDate(date) {
    return new Date(date).toLocaleDateString("ru-RU", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
    });
}

// Получение статуса
function getStatusConfig(status) {
    const configs = {
        pending: { label: "На рассмотрении", color: "warning", icon: Clock },
        approved: { label: "Одобрена", color: "success", icon: CheckCircle },
        rejected: { label: "Отклонена", color: "danger", icon: AlertTriangle },
        published: {
            label: "Опубликован",
            color: "success",
            icon: CheckCircle,
        },
        draft: { label: "Черновик", color: "secondary", icon: FileText },
    };

    return (
        configs[status] || { label: status, color: "secondary", icon: FileText }
    );
}
</script>

<template>
    <DashboardLayout title="Админ-панель">
        <template #header>
            <div class="page-header">
                <div class="header-content">
                    <h1 class="page-title">Админ-панель</h1>
                    <p class="page-subtitle">
                        Управление платформой и модерация контента
                    </p>
                </div>

                <div class="header-actions">
                    <Link
                        :href="route('admin.analytics')"
                        class="btn btn-outline"
                    >
                        <BarChart3 size="16" />
                        <span>Аналитика</span>
                    </Link>
                </div>
            </div>
        </template>

        <div class="admin-dashboard">
            <!-- Статистические карточки -->
            <div class="stats-grid">
                <Link
                    v-for="card in statsCards"
                    :key="card.title"
                    :href="card.link"
                    class="stats-card"
                    :class="`stats-card--${card.color}`"
                >
                    <div class="stats-header">
                        <div class="stats-icon">
                            <component :is="card.icon" size="24" />
                        </div>
                        <div class="stats-info">
                            <h3 class="stats-title">{{ card.title }}</h3>
                            <div class="stats-value">{{ card.value }}</div>
                            <p class="stats-description">
                                {{ card.description }}
                            </p>
                        </div>
                    </div>
                </Link>
            </div>

            <!-- Графики и аналитика -->
            <div class="charts-section">
                <!-- График активности -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h2 class="chart-title">
                            <TrendingUp size="20" />
                            Активность за последние 30 дней
                        </h2>
                    </div>
                    <LineChart
                        :data="chartsData.activity"
                        :height="300"
                        :options="{
                            plugins: {
                                legend: {
                                    position: 'top',
                                },
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1,
                                    },
                                },
                            },
                        }"
                    />
                </div>

                <!-- Статистика по статусам -->
                <div class="status-charts">
                    <div class="status-chart">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <FileText size="18" />
                                Статусы заявок
                            </h3>
                        </div>
                        <DoughnutChart
                            :data="chartsData.applicationStatus"
                            :height="250"
                        />
                    </div>

                    <div class="status-chart">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <Music size="18" />
                                Статусы релизов
                            </h3>
                        </div>
                        <DoughnutChart
                            :data="chartsData.releaseStatus"
                            :height="250"
                        />
                    </div>
                </div>
            </div>

            <!-- Основной контент -->
            <div class="content-grid">
                <!-- Заявки на рассмотрении -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <Clock size="20" />
                            Заявки на рассмотрении
                        </h2>
                        <Link
                            :href="route('admin.applications')"
                            class="section-link"
                        >
                            Все заявки
                        </Link>
                    </div>

                    <div class="items-list">
                        <div
                            v-for="application in pendingApplications"
                            :key="application.id"
                            class="item-card"
                        >
                            <div class="item-info">
                                <h4 class="item-title">
                                    {{ application.title }}
                                </h4>
                                <p class="item-description">
                                    {{ application.description }}
                                </p>
                                <div class="item-meta">
                                    <span class="meta-item">
                                        <Users size="14" />
                                        {{ application.user?.name }}
                                    </span>
                                    <span class="meta-item">
                                        <Calendar size="14" />
                                        {{ formatDate(application.created_at) }}
                                    </span>
                                </div>
                            </div>

                            <div class="item-actions">
                                <Link
                                    :href="`/admin/applications/${application.id}`"
                                    class="action-btn"
                                >
                                    <Eye size="16" />
                                </Link>
                            </div>
                        </div>

                        <div
                            v-if="pendingApplications.length === 0"
                            class="empty-state"
                        >
                            <CheckCircle size="32" />
                            <p>Нет заявок на рассмотрении</p>
                        </div>
                    </div>
                </div>

                <!-- Релизы на рассмотрении -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <Music size="20" />
                            Релизы на рассмотрении
                        </h2>
                        <Link
                            :href="route('admin.releases')"
                            class="section-link"
                        >
                            Все релизы
                        </Link>
                    </div>

                    <div class="items-list">
                        <div
                            v-for="release in pendingReleases"
                            :key="release.id"
                            class="item-card"
                        >
                            <div class="item-info">
                                <h4 class="item-title">{{ release.title }}</h4>
                                <p class="item-description">
                                    {{ release.description }}
                                </p>
                                <div class="item-meta">
                                    <span class="meta-item">
                                        <Users size="14" />
                                        {{ release.artist?.user?.name }}
                                    </span>
                                    <span class="meta-item">
                                        <Calendar size="14" />
                                        {{ formatDate(release.created_at) }}
                                    </span>
                                </div>
                            </div>

                            <div class="item-actions">
                                <Link
                                    :href="`/admin/releases/${release.id}`"
                                    class="action-btn"
                                >
                                    <Eye size="16" />
                                </Link>
                            </div>
                        </div>

                        <div
                            v-if="pendingReleases.length === 0"
                            class="empty-state"
                        >
                            <CheckCircle size="32" />
                            <p>Нет релизов на рассмотрении</p>
                        </div>
                    </div>
                </div>

                <!-- Новые пользователи -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <Users size="20" />
                            Новые пользователи
                        </h2>
                        <Link :href="route('admin.users')" class="section-link">
                            Все пользователи
                        </Link>
                    </div>

                    <div class="items-list">
                        <div
                            v-for="user in recentUsers"
                            :key="user.id"
                            class="item-card"
                        >
                            <div class="item-info">
                                <h4 class="item-title">{{ user.name }}</h4>
                                <p class="item-description">{{ user.email }}</p>
                                <div class="item-meta">
                                    <span
                                        class="meta-item role-badge"
                                        :class="`role-${user.role}`"
                                    >
                                        {{ user.role }}
                                    </span>
                                    <span class="meta-item">
                                        <Calendar size="14" />
                                        {{ formatDate(user.created_at) }}
                                    </span>
                                </div>
                            </div>

                            <div class="item-actions">
                                <Link
                                    :href="`/admin/users/${user.id}`"
                                    class="action-btn"
                                >
                                    <Eye size="16" />
                                </Link>
                            </div>
                        </div>

                        <div
                            v-if="recentUsers.length === 0"
                            class="empty-state"
                        >
                            <Users size="32" />
                            <p>Нет новых пользователей</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </DashboardLayout>
</template>

<style lang="scss" scoped>
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    margin-bottom: 32px;

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}

.header-content {
    flex: 1;
}

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px;
}

.page-subtitle {
    color: var(--text-secondary);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 16px;
}

.admin-dashboard {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
}

.stats-card {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    &--orange {
        border-left: 4px solid #f97316;
    }

    &--blue {
        border-left: 4px solid #3b82f6;
    }

    &--green {
        border-left: 4px solid #22c55e;
    }

    &--purple {
        border-left: 4px solid #a855f7;
    }
}

.stats-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stats-card--orange .stats-icon {
    background: rgba(249, 115, 22, 0.1);
    color: #f97316;
}

.stats-card--blue .stats-icon {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.stats-card--green .stats-icon {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.stats-card--purple .stats-icon {
    background: rgba(168, 85, 247, 0.1);
    color: #a855f7;
}

.stats-info {
    flex: 1;
}

.stats-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin: 0 0 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.stats-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;

    @media (max-width: 1024px) {
        grid-template-columns: 1fr;
    }
}

.content-section {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-link {
    font-size: 0.875rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;

    &:hover {
        text-decoration: underline;
    }
}

.items-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.item-card {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 16px;
    padding: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.02);
        border-color: rgba(0, 0, 0, 0.1);
    }
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px;
    line-height: 1.4;
}

.item-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0 0 8px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.item-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.625rem;
    color: var(--text-secondary);

    &.role-badge {
        padding: 2px 6px;
        border-radius: var(--radius-sm);
        font-weight: 500;
        text-transform: uppercase;

        &.role-artist {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
        }

        &.role-manager {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        &.role-admin {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }
    }
}

.item-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    text-decoration: none;

    &:hover {
        background: rgba(var(--primary-rgb), 0.2);
    }
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);

    svg {
        margin-bottom: 12px;
        opacity: 0.5;
    }

    p {
        margin: 0;
        font-size: 0.875rem;
    }
}

.btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    text-decoration: none;
    font-size: 0.875rem;
}

.btn-outline {
    background: transparent;
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: var(--text-primary);

    &:hover {
        background: rgba(0, 0, 0, 0.05);
    }
}

// Стили для графиков
.charts-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.chart-container {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
}

.chart-header {
    margin-bottom: 20px;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;

    svg {
        color: var(--primary-color);
    }
}

.status-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.status-chart {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .item-card {
        flex-direction: column;
        align-items: flex-start;
    }

    .item-actions {
        align-self: flex-end;
    }

    .status-charts {
        grid-template-columns: 1fr;
    }
}
</style>
