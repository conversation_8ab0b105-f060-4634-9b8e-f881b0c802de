.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background-color: var(--bg-sidebar);
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    z-index: 1000;
    transition: width var(--transition-normal);
    display: flex;
    flex-direction: column;

    // Состояние свернутого сайдбара (только иконки)
    &.sidebar-collapsed {
        width: var(--sidebar-collapsed-width);

        .sidebar-logo {
            justify-content: center;

            .logo-icon {
                display: block;
            }

            .logo-full {
                display: none;
            }
        }

        .sidebar-nav {
            .nav-item {
                padding: 12px;
                justify-content: center;

                .item-icon {
                    margin-right: 0;
                }

                .item-label {
                    display: none;
                }

                .item-badge {
                    position: absolute;
                    top: 4px;
                    right: 4px;
                    padding: 0;
                    width: 16px;
                    height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }

        .sidebar-footer {
            .user-info {
                justify-content: center;

                .user-avatar {
                    margin-right: 0;
                }

                .user-details {
                    display: none;
                }

                .action-button {
                    display: none;
                }
            }
        }
    }

    // Логотип и заголовок
    .sidebar-logo {
        padding: 20px;
        height: var(--header-height);
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        .logo-icon {
            width: 40px;
            height: 40px;
            display: none;
        }

        .logo-full {
            height: 40px;
            width: auto;
        }
    }

    // Основное меню
    .sidebar-nav {
        flex: 1;
        padding: 20px 0;
        overflow-y: auto;

        .nav-group {
            margin-bottom: 24px;

            .group-title {
                padding: 0 20px;
                margin-bottom: 8px;
                font-size: 0.75rem;
                font-weight: 600;
                text-transform: uppercase;
                color: var(--text-disabled);
                letter-spacing: 0.05em;
            }
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.9375rem;
            position: relative;
            transition: all var(--transition-fast);

            &:hover {
                background-color: rgba(0, 0, 0, 0.03);
                color: var(--text-primary);
            }

            &.active {
                background-color: rgba(var(--primary-color-rgb), 0.1);
                color: var(--primary-color);

                &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 3px;
                    background-color: var(--primary-color);
                }
            }

            .item-icon {
                margin-right: 12px;
                flex-shrink: 0;
            }

            .item-label {
                flex: 1;
            }

            .item-badge {
                background-color: var(--danger-color);
                color: white;
                border-radius: 12px;
                padding: 2px 8px;
                font-size: 0.75rem;
                font-weight: 600;
            }
        }
    }

    // Футер сайдбара
    .sidebar-footer {
        padding: 12px 16px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);

        .user-info {
            display: flex;
            align-items: center;

            .user-avatar {
                width: 36px;
                height: 36px;
                border-radius: 50%;
                overflow: hidden;
                margin-right: 12px;
                flex-shrink: 0;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .user-details {
                flex: 1;
                min-width: 0;

                .user-name {
                    font-size: 0.875rem;
                    font-weight: 500;
                    color: var(--text-primary);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .user-role {
                    font-size: 0.75rem;
                    color: var(--text-secondary);
                }
            }

            .action-button {
                background: none;
                border: none;
                color: var(--text-secondary);
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                transition: all var(--transition-fast);

                &:hover {
                    background-color: rgba(0, 0, 0, 0.05);
                    color: var(--text-primary);
                }
            }
        }
    }

    // Мобильная версия
    @media (max-width: 992px) {
        transform: translateX(-100%);
        box-shadow: none;

        &.sidebar-mobile-open {
            transform: translateX(0);
            box-shadow: var(--shadow-lg);
        }
    }
}

// Overlay для мобильного меню
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 90;
    display: none;
    transition: opacity var(--transition-normal);

    &.active {
        display: block;
    }

    &.v-enter-from,
    &.v-leave-to {
        opacity: 0;
    }
}
