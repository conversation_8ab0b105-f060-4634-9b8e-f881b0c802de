.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--bg-gradient-surface);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--border-color);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-lg);

    // Современный стеклянный эффект
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        z-index: -1;
        border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
    }

    // Состояние свернутого сайдбара (только иконки) - улучшенная анимация
    &.sidebar-collapsed {
        width: var(--sidebar-collapsed-width);

        &::before {
            border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
        }

        .sidebar-logo {
            justify-content: center;
            padding: 20px 16px;

            .logo-icon {
                display: block;
                transform: scale(1);
                transition: transform var(--transition-bounce);
            }

            .logo-full {
                display: none;
            }
        }

        .sidebar-nav {
            .nav-group {
                .group-title {
                    opacity: 0;
                    transform: translateX(-10px);
                    transition: all var(--transition-fast);
                }
            }

            .nav-item {
                padding: 16px;
                justify-content: center;
                position: relative;
                border-radius: var(--radius-lg);
                margin: 4px 12px;

                .item-icon {
                    margin-right: 0;
                    transform: scale(1.1);
                    transition: transform var(--transition-bounce);
                }

                .item-label {
                    opacity: 0;
                    transform: translateX(-10px);
                    transition: all var(--transition-fast);
                    position: absolute;
                    left: 100%;
                    top: 50%;
                    transform: translateY(-50%);
                    background: var(--bg-elevated);
                    padding: 8px 12px;
                    border-radius: var(--radius-md);
                    box-shadow: var(--shadow-lg);
                    white-space: nowrap;
                    z-index: var(--z-tooltip);
                    pointer-events: none;
                    margin-left: 8px;

                    &::before {
                        content: '';
                        position: absolute;
                        left: -4px;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 0;
                        height: 0;
                        border-style: solid;
                        border-width: 4px 4px 4px 0;
                        border-color: transparent var(--bg-elevated) transparent transparent;
                    }
                }

                .item-badge {
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    padding: 0;
                    width: 18px;
                    height: 18px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: var(--font-size-xs);
                    border: 2px solid var(--bg-sidebar);
                    box-shadow: var(--shadow-md);
                }

                // Показываем tooltip при hover
                &:hover .item-label {
                    opacity: 1;
                    transform: translateY(-50%) translateX(0);
                    pointer-events: auto;
                }
            }
        }

        .sidebar-footer {
            .user-info {
                justify-content: center;
                padding: 16px;

                .user-avatar {
                    margin-right: 0;
                    width: 40px;
                    height: 40px;
                    border: 2px solid var(--border-color);
                    transition: all var(--transition-normal);

                    &:hover {
                        border-color: var(--primary-color);
                        transform: scale(1.05);
                    }
                }

                .user-details {
                    opacity: 0;
                    transform: translateX(-10px);
                    transition: all var(--transition-fast);
                }

                .action-button {
                    opacity: 0;
                    transform: translateX(-10px);
                    transition: all var(--transition-fast);
                }
            }
        }
    }

    // Логотип и заголовок - современный дизайн
    .sidebar-logo {
        padding: 24px;
        height: var(--header-height);
        display: flex;
        align-items: center;
        justify-content: flex-start;
        border-bottom: 1px solid var(--border-color);
        background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.05) 0%, transparent 100%);
        position: relative;

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 24px;
            right: 24px;
            height: 1px;
            background: var(--primary-gradient);
            opacity: 0.3;
        }

        .logo-icon {
            width: 44px;
            height: 44px;
            display: none;
            filter: drop-shadow(0 2px 4px rgba(var(--primary-color-rgb), 0.2));
            transition: all var(--transition-bounce);

            &:hover {
                transform: scale(1.05) rotate(5deg);
            }
        }

        .logo-full {
            height: 44px;
            width: auto;
            filter: drop-shadow(0 2px 4px rgba(var(--primary-color-rgb), 0.2));
            transition: all var(--transition-normal);

            &:hover {
                transform: scale(1.02);
            }
        }
    }

    // Основное меню - современный дизайн с улучшенными эффектами
    .sidebar-nav {
        flex: 1;
        padding: 24px 0;
        overflow-y: auto;
        overflow-x: hidden;

        // Кастомный скроллбар
        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background: var(--border-medium);
            border-radius: var(--radius-full);

            &:hover {
                background: var(--primary-color);
            }
        }

        .nav-group {
            margin-bottom: 32px;
            position: relative;

            .group-title {
                padding: 0 24px;
                margin-bottom: 12px;
                font-size: var(--font-size-xs);
                font-weight: var(--font-weight-semibold);
                text-transform: uppercase;
                color: var(--text-disabled);
                letter-spacing: 0.1em;
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -6px;
                    left: 24px;
                    width: 20px;
                    height: 2px;
                    background: var(--primary-gradient);
                    border-radius: var(--radius-full);
                    opacity: 0.6;
                }
            }
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 14px 20px;
            margin: 4px 16px;
            color: var(--text-secondary);
            text-decoration: none;
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            position: relative;
            border-radius: var(--radius-lg);
            transition: all var(--transition-normal);
            overflow: hidden;

            // Фоновый эффект при hover
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: var(--primary-gradient);
                opacity: 0;
                transition: all var(--transition-normal);
                z-index: -1;
            }

            &:hover {
                background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.08) 0%, rgba(var(--primary-color-rgb), 0.04) 100%);
                color: var(--primary-color);
                transform: translateX(4px);
                box-shadow: var(--shadow-md);

                &::before {
                    left: 0;
                    opacity: 0.1;
                }

                .item-icon {
                    transform: scale(1.1);
                    color: var(--primary-color);
                }
            }

            &.active {
                background: var(--primary-gradient);
                color: var(--text-inverse);
                box-shadow: var(--shadow-glow);
                transform: translateX(4px);

                &::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 4px;
                    height: 20px;
                    background: var(--text-inverse);
                    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
                    box-shadow: var(--shadow-md);
                }

                .item-icon {
                    color: var(--text-inverse);
                    transform: scale(1.05);
                }

                .item-label {
                    color: var(--text-inverse);
                    font-weight: var(--font-weight-semibold);
                }

                .item-badge {
                    background: var(--text-inverse);
                    color: var(--primary-color);
                    box-shadow: var(--shadow-sm);
                }
            }

            .item-icon {
                margin-right: 14px;
                flex-shrink: 0;
                transition: all var(--transition-bounce);
                width: 20px;
                height: 20px;
            }

            .item-label {
                flex: 1;
                transition: all var(--transition-fast);
            }

            .item-badge {
                background: var(--danger-gradient);
                color: var(--text-inverse);
                border-radius: var(--radius-full);
                padding: 4px 8px;
                font-size: var(--font-size-xs);
                font-weight: var(--font-weight-semibold);
                min-width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: var(--shadow-sm);
                transition: all var(--transition-normal);

                &:hover {
                    transform: scale(1.1);
                }
            }
        }
    }

    // Футер сайдбара - современный дизайн пользователя
    .sidebar-footer {
        padding: 20px 16px;
        border-top: 1px solid var(--border-color);
        background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.02) 0%, transparent 100%);
        position: relative;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 16px;
            right: 16px;
            height: 1px;
            background: var(--primary-gradient);
            opacity: 0.3;
        }

        .user-info {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: var(--radius-lg);
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: var(--primary-gradient);
                opacity: 0;
                transition: all var(--transition-normal);
                z-index: -1;
            }

            &:hover {
                background: rgba(var(--primary-color-rgb), 0.05);
                transform: translateY(-2px);
                box-shadow: var(--shadow-md);

                &::before {
                    left: 0;
                    opacity: 0.05;
                }

                .user-avatar {
                    transform: scale(1.05);
                    box-shadow: var(--shadow-glow);
                }

                .action-button {
                    opacity: 1;
                    transform: scale(1.1);
                }
            }

            .user-avatar {
                width: 42px;
                height: 42px;
                border-radius: var(--radius-full);
                overflow: hidden;
                margin-right: 14px;
                flex-shrink: 0;
                border: 2px solid var(--border-color);
                transition: all var(--transition-bounce);
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    top: -2px;
                    left: -2px;
                    right: -2px;
                    bottom: -2px;
                    border-radius: var(--radius-full);
                    background: var(--primary-gradient);
                    opacity: 0;
                    transition: opacity var(--transition-normal);
                    z-index: -1;
                }

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    transition: all var(--transition-normal);
                }

                &:hover::after {
                    opacity: 1;
                }
            }

            .user-details {
                flex: 1;
                min-width: 0;
                transition: all var(--transition-fast);

                .user-name {
                    font-size: var(--font-size-sm);
                    font-weight: var(--font-weight-semibold);
                    color: var(--text-primary);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin-bottom: 2px;
                    transition: color var(--transition-fast);
                }

                .user-role {
                    font-size: var(--font-size-xs);
                    color: var(--text-tertiary);
                    font-weight: var(--font-weight-medium);
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    transition: color var(--transition-fast);
                }
            }

            .action-button {
                background: none;
                border: none;
                color: var(--text-secondary);
                cursor: pointer;
                padding: 8px;
                border-radius: var(--radius-md);
                transition: all var(--transition-bounce);
                opacity: 0.7;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 0;
                    height: 0;
                    background: var(--danger-gradient);
                    border-radius: var(--radius-full);
                    transition: all var(--transition-normal);
                    z-index: -1;
                }

                &:hover {
                    background: var(--danger-light);
                    color: var(--danger-color);
                    transform: scale(1.1);

                    &::before {
                        width: 100%;
                        height: 100%;
                    }
                }

                &:active {
                    transform: scale(0.95);
                }
            }
        }
    }

    // Мобильная версия - улучшенная адаптивность
    @media (max-width: 992px) {
        transform: translateX(-100%);
        box-shadow: none;
        border-radius: 0;

        &::before {
            border-radius: 0;
        }

        &.sidebar-mobile-open {
            transform: translateX(0);
            box-shadow: var(--shadow-2xl);

            .sidebar-nav .nav-item {
                margin: 4px 20px;

                &:hover {
                    transform: translateX(8px);
                }
            }
        }

        // Улучшенная анимация появления на мобильных
        .sidebar-nav {
            .nav-item {
                opacity: 0;
                transform: translateX(-20px);
                animation: slideInLeft 0.3s ease forwards;

                @for $i from 1 through 10 {
                    &:nth-child(#{$i}) {
                        animation-delay: #{$i * 0.05}s;
                    }
                }
            }
        }

        &:not(.sidebar-mobile-open) .sidebar-nav .nav-item {
            animation: none;
            opacity: 1;
            transform: none;
        }
    }

    @media (max-width: 576px) {
        width: 100vw;

        .sidebar-logo {
            padding: 20px 24px;
        }

        .sidebar-nav {
            padding: 20px 0;

            .nav-item {
                padding: 16px 24px;
                font-size: var(--font-size-base);
            }
        }

        .sidebar-footer {
            padding: 20px 24px;
        }
    }
}

// Анимации
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

// Overlay для мобильного меню - улучшенный дизайн
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    backdrop-filter: blur(8px);
    z-index: calc(var(--z-fixed) - 10);
    display: none;
    transition: all var(--transition-normal);
    opacity: 0;

    &.active {
        display: block;
        opacity: 1;
    }

    &.v-enter-from,
    &.v-leave-to {
        opacity: 0;
        backdrop-filter: blur(0px);
    }

    &.v-enter-to,
    &.v-leave-from {
        opacity: 1;
        backdrop-filter: blur(8px);
    }
}

// Дополнительные эффекты для современного вида
.sidebar {
    // Эффект свечения для активных элементов
    .nav-item.active {
        position: relative;

        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary-gradient);
            opacity: 0.1;
            border-radius: inherit;
            z-index: -1;
            filter: blur(8px);
            transform: scale(1.1);
        }
    }

    // Пульсирующий эффект для уведомлений
    .item-badge {
        animation: pulse 2s infinite;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

// Эффект параллакса для фона
.sidebar::before {
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
}
