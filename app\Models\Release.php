<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Release extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'artist_id',
        'title',
        'description',
        'type',
        'status',
        'cover_image',
        'meta_info',
        'genre',
        'language',
        'is_explicit',
        'release_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'meta_info' => 'json',
        'is_explicit' => 'boolean',
        'release_date' => 'date',
    ];

    /**
     * Get the artist that owns the release.
     */
    public function artist(): BelongsTo
    {
        return $this->belongsTo(Artist::class);
    }

    /**
     * Get the tracks for the release.
     */
    public function tracks(): HasMany
    {
        return $this->hasMany(Track::class)->orderBy('track_number', 'asc');
    }

    /**
     * Get the analytics for the release.
     */
    public function analytics(): MorphMany
    {
        return $this->morphMany(Analytic::class, 'analyzable');
    }

    /**
     * Get the cover image URL.
     */
    public function getCoverImageUrlAttribute(): string
    {
        if ($this->cover_image) {
            return asset('storage/' . $this->cover_image);
        }

        return asset('images/default-release-cover.jpg');
    }

    /**
     * Scope a query to only include releases with the given status.
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Get the total tracks count.
     */
    public function getTracksCountAttribute(): int
    {
        return $this->tracks()->count();
    }

    /**
     * Get the total duration of the release in seconds.
     */
    public function getTotalDurationAttribute(): int
    {
        return $this->tracks()->sum('duration_seconds');
    }

    /**
     * Format the total duration as minutes:seconds.
     */
    public function getFormattedDurationAttribute(): string
    {
        $totalSeconds = $this->total_duration;
        $minutes = floor($totalSeconds / 60);
        $seconds = $totalSeconds % 60;

        return sprintf('%d:%02d', $minutes, $seconds);
    }
}
