<script setup>
import { ref, computed, watch } from "vue";
import {
    AlertTriangle,
    CheckCircle,
    Info,
    X,
    AlertCircle,
} from "lucide-vue-next";

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: "Подтверждение",
    },
    message: {
        type: String,
        required: true,
    },
    type: {
        type: String,
        default: "warning", // warning, danger, info, success
        validator: (value) => ["warning", "danger", "info", "success"].includes(value),
    },
    confirmText: {
        type: String,
        default: "Подтвердить",
    },
    cancelText: {
        type: String,
        default: "Отмена",
    },
    confirmButtonType: {
        type: String,
        default: "primary", // primary, danger, success
    },
    loading: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(["confirm", "cancel", "close"]);

// Состояние компонента
const isVisible = ref(false);

// Вычисляемые свойства
const dialogIcon = computed(() => {
    switch (props.type) {
        case "danger":
            return AlertTriangle;
        case "success":
            return CheckCircle;
        case "info":
            return Info;
        case "warning":
        default:
            return AlertCircle;
    }
});

const dialogClass = computed(() => {
    return `dialog-${props.type}`;
});

const confirmButtonClass = computed(() => {
    switch (props.confirmButtonType) {
        case "danger":
            return "btn-danger";
        case "success":
            return "btn-success";
        case "primary":
        default:
            return "btn-primary";
    }
});

// Методы
function handleConfirm() {
    if (props.loading) return;
    emit("confirm");
}

function handleCancel() {
    if (props.loading) return;
    emit("cancel");
    emit("close");
}

function handleBackdropClick() {
    if (props.loading) return;
    handleCancel();
}

// Watchers
watch(() => props.show, (newValue) => {
    isVisible.value = newValue;
});

watch(isVisible, (newValue) => {
    if (!newValue) {
        emit("close");
    }
});
</script>

<template>
    <Teleport to="body">
        <Transition name="modal-backdrop">
            <div
                v-if="show"
                class="modal-backdrop"
                @click="handleBackdropClick"
            ></div>
        </Transition>

        <Transition name="modal">
            <div v-if="show" class="modal-container">
                <div class="modal-content" :class="dialogClass" @click.stop>
                    <!-- Header -->
                    <div class="modal-header">
                        <div class="header-icon">
                            <component :is="dialogIcon" size="24" />
                        </div>
                        <div class="header-content">
                            <h3 class="modal-title">{{ title }}</h3>
                        </div>
                        <button
                            type="button"
                            class="close-btn"
                            @click="handleCancel"
                            :disabled="loading"
                        >
                            <X size="20" />
                        </button>
                    </div>

                    <!-- Body -->
                    <div class="modal-body">
                        <p class="modal-message">{{ message }}</p>
                    </div>

                    <!-- Footer -->
                    <div class="modal-footer">
                        <button
                            type="button"
                            class="btn btn-outline"
                            @click="handleCancel"
                            :disabled="loading"
                        >
                            {{ cancelText }}
                        </button>
                        
                        <button
                            type="button"
                            class="btn"
                            :class="confirmButtonClass"
                            @click="handleConfirm"
                            :disabled="loading"
                        >
                            <span v-if="loading" class="loading-spinner"></span>
                            <span>{{ confirmText }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </Transition>
    </Teleport>
</template>

<style lang="scss" scoped>
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 400px;
    overflow: hidden;

    &.dialog-warning {
        border-top: 4px solid #f59e0b;
    }

    &.dialog-danger {
        border-top: 4px solid #ef4444;
    }

    &.dialog-success {
        border-top: 4px solid #10b981;
    }

    &.dialog-info {
        border-top: 4px solid #3b82f6;
    }
}

.modal-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 24px 24px 0;
}

.header-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .dialog-warning & {
        background: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
    }

    .dialog-danger & {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
    }

    .dialog-success & {
        background: rgba(16, 185, 129, 0.1);
        color: #10b981;
    }

    .dialog-info & {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
    }
}

.header-content {
    flex: 1;
}

.modal-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
        background: rgba(0, 0, 0, 0.05);
        color: var(--text-primary);
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.modal-body {
    padding: 16px 24px 24px;
}

.modal-message {
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
}

.modal-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 0 24px 24px;
}

.btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    font-size: 0.875rem;

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.btn-outline {
    background: transparent;
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: var(--text-primary);

    &:hover:not(:disabled) {
        background: rgba(0, 0, 0, 0.05);
    }
}

.btn-primary {
    background: var(--primary-color);
    color: white;

    &:hover:not(:disabled) {
        background: var(--primary-hover);
    }
}

.btn-danger {
    background: #ef4444;
    color: white;

    &:hover:not(:disabled) {
        background: #dc2626;
    }
}

.btn-success {
    background: #10b981;
    color: white;

    &:hover:not(:disabled) {
        background: #059669;
    }
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

// Transitions
.modal-backdrop-enter-active,
.modal-backdrop-leave-active {
    transition: opacity 0.3s ease;
}

.modal-backdrop-enter-from,
.modal-backdrop-leave-to {
    opacity: 0;
}

.modal-enter-active,
.modal-leave-active {
    transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

@media (max-width: 768px) {
    .modal-container {
        padding: 10px;
    }

    .modal-content {
        max-width: 100%;
    }

    .modal-footer {
        flex-direction: column-reverse;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>
