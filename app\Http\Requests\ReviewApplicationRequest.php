<?php

namespace App\Http\Requests;

use App\Models\Application;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReviewApplicationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();
        
        // Только админы и менеджеры могут рассматривать заявки
        return $user && ($user->isAdmin() || $user->isManager());
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => [
                'required',
                'string',
                Rule::in(['approved', 'rejected'])
            ],
            'admin_notes' => [
                'nullable',
                'string',
                'max:2000'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'status.required' => 'Статус рассмотрения обязателен для заполнения.',
            'status.in' => 'Недопустимый статус. Выберите "одобрено" или "отклонено".',
            'admin_notes.max' => 'Комментарий не может превышать :max символов.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'status' => 'статус',
            'admin_notes' => 'комментарий администратора'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Автоматически добавляем данные о рассмотрении
        $this->merge([
            'reviewed_by' => auth()->id(),
            'reviewed_at' => now()
        ]);
    }

    /**
     * Get the validated data from the request with additional fields.
     *
     * @return array
     */
    public function validatedWithDefaults(): array
    {
        $validated = $this->validated();
        
        return array_merge($validated, [
            'reviewed_by' => auth()->id(),
            'reviewed_at' => now()
        ]);
    }
}
