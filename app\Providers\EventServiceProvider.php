<?php

namespace App\Providers;

use App\Events\ApplicationCreated;
use App\Events\ApplicationStatusUpdated;
use App\Listeners\CreateNotificationOnApplicationStatusUpdate;
use App\Listeners\SendApplicationStatusNotification;
use App\Listeners\SendNewApplicationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        ApplicationCreated::class => [
            SendNewApplicationNotification::class,
        ],

        ApplicationStatusUpdated::class => [
            SendApplicationStatusNotification::class,
            CreateNotificationOnApplicationStatusUpdate::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
