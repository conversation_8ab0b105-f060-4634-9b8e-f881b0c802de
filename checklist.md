# Madd Label Dashboard Checklist

## 🧱 Laravel Backend

-   [x] Обновить конфигура<PERSON><PERSON><PERSON> (удалить Jetstream UI)
-   [ ] Настроить Auth via Fortify + OAuth (Google, VK)
-   [x] Создать миграции и модели (users, artists, releases, tracks, applications, notifications, settings, analytics)
-   [x] Реализовать политики и роли (admin, artist, manager)
-   [x] Разработать RESTful API для заявок, релизов, аналитики
-   [ ] Настроить валидацию через Form Requests
-   [ ] Реализовать отправку уведомлений через Broadcast + кастомные события
-   [ ] Настроить очереди (Redis) для загрузки и удаления файлов

## 🖼️ Vue Frontend

-   [x] Установить необходимые пакеты (vue 3, pinia, scss)
-   [x] Удалить Tailwind, настроить SCSS
-   [x] Создать адаптивный сайдбар
-   [x] Разработать основные компоненты (Header, NotificationCenter)
-   [x] Создать страницу дашборда
-   [ ] Реализовать модуль релизов (ReleaseForm, TrackUploader)
-   [ ] Реализовать модуль заявок (ApplicationModal)
-   [ ] Создать модуль аналитики (AnalyticsView, Leaderboard)
-   [ ] Интегрировать уведомления + real-time через Laravel Echo

## 💾 Файлы и хранение

-   [ ] Настроить загрузку обложек, треков, мета JSON
-   [ ] Реализовать удаление исходников после подтверждения
-   [ ] Добавить функционал скачивания архива релиза

## 📊 UI/UX

-   [x] Реализовать поддержку светлой/тёмной темы
-   [x] Интегрировать Lucide icons
-   [ ] Создать интерактивный интерфейс заявок
-   [x] Настроить анимации через CSS Transitions
-   [ ] Добавить диалоговые окна подтверждения

## ⚙️ Расширения

-   [ ] Создать настройки профиля
-   [ ] Лидерборд артистов
