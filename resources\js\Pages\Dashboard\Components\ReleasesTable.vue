<script setup>
import { ref, computed } from "vue";
import { <PERSON> } from "@inertiajs/vue3";
import {
    ChevronDown,
    ChevronUp,
    Check,
    Clock,
    X,
    AlertTriangle,
} from "lucide-vue-next";

const props = defineProps({
    releases: {
        type: Array,
        default: () => [],
    },
});

// Состояние для сортировки
const sortField = ref("created_at");
const sortDirection = ref("desc");

// Функция сортировки
const sort = (field) => {
    if (sortField.value === field) {
        sortDirection.value = sortDirection.value === "asc" ? "desc" : "asc";
    } else {
        sortField.value = field;
        sortDirection.value = "desc";
    }
};

// Отсортированные релизы
const sortedReleases = computed(() => {
    return [...props.releases].sort((a, b) => {
        let comparison = 0;

        // Сортировка по выбранному полю
        if (sortField.value === "created_at") {
            comparison = new Date(a.created_at) - new Date(b.created_at);
        } else if (sortField.value === "title") {
            comparison = a.title.localeCompare(b.title);
        } else if (sortField.value === "artist") {
            comparison = a.artist.display_name.localeCompare(
                b.artist.display_name
            );
        } else if (sortField.value === "type") {
            comparison = a.type.localeCompare(b.type);
        } else if (sortField.value === "status") {
            comparison = a.status.localeCompare(b.status);
        }

        // Учитываем направление сортировки
        return sortDirection.value === "asc" ? comparison : -comparison;
    });
});

// Получение статуса релиза
const getStatusIcon = (status) => {
    switch (status) {
        case "approved":
            return Check;
        case "pending":
            return Clock;
        case "rejected":
            return X;
        default:
            return AlertTriangle;
    }
};

// Получение названия статуса
const getStatusName = (status) => {
    switch (status) {
        case "approved":
            return "Одобрен";
        case "pending":
            return "На проверке";
        case "rejected":
            return "Отклонен";
        case "draft":
            return "Черновик";
        default:
            return "Неизвестный статус";
    }
};

// Получение названия типа релиза
const getTypeName = (type) => {
    switch (type) {
        case "single":
            return "Сингл";
        case "ep":
            return "EP";
        case "album":
            return "Альбом";
        default:
            return type;
    }
};

// Форматирование даты
const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("ru-RU", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
    }).format(date);
};

// Получение цвета статуса
const getStatusClass = (status) => {
    switch (status) {
        case "approved":
            return "status-approved";
        case "pending":
            return "status-pending";
        case "rejected":
            return "status-rejected";
        case "draft":
            return "status-draft";
        default:
            return "";
    }
};
</script>

<template>
    <div class="releases-table-container">
        <div class="table-header">
            <h2 class="table-title">Последние релизы</h2>
            <Link href="/releases" class="view-all-link">Все релизы</Link>
        </div>

        <div class="table-responsive">
            <table class="releases-table">
                <thead>
                    <tr>
                        <th class="cover-column">Обложка</th>
                        <th @click="sort('title')" class="sortable">
                            Название
                            <ChevronUp
                                v-if="
                                    sortField === 'title' &&
                                    sortDirection === 'asc'
                                "
                                class="sort-icon"
                                size="14"
                            />
                            <ChevronDown
                                v-else-if="
                                    sortField === 'title' &&
                                    sortDirection === 'desc'
                                "
                                class="sort-icon"
                                size="14"
                            />
                        </th>
                        <th @click="sort('artist')" class="sortable">
                            Артист
                            <ChevronUp
                                v-if="
                                    sortField === 'artist' &&
                                    sortDirection === 'asc'
                                "
                                class="sort-icon"
                                size="14"
                            />
                            <ChevronDown
                                v-else-if="
                                    sortField === 'artist' &&
                                    sortDirection === 'desc'
                                "
                                class="sort-icon"
                                size="14"
                            />
                        </th>
                        <th @click="sort('type')" class="sortable hide-mobile">
                            Тип
                            <ChevronUp
                                v-if="
                                    sortField === 'type' &&
                                    sortDirection === 'asc'
                                "
                                class="sort-icon"
                                size="14"
                            />
                            <ChevronDown
                                v-else-if="
                                    sortField === 'type' &&
                                    sortDirection === 'desc'
                                "
                                class="sort-icon"
                                size="14"
                            />
                        </th>
                        <th @click="sort('status')" class="sortable">
                            Статус
                            <ChevronUp
                                v-if="
                                    sortField === 'status' &&
                                    sortDirection === 'asc'
                                "
                                class="sort-icon"
                                size="14"
                            />
                            <ChevronDown
                                v-else-if="
                                    sortField === 'status' &&
                                    sortDirection === 'desc'
                                "
                                class="sort-icon"
                                size="14"
                            />
                        </th>
                        <th
                            @click="sort('created_at')"
                            class="sortable hide-mobile"
                        >
                            Дата создания
                            <ChevronUp
                                v-if="
                                    sortField === 'created_at' &&
                                    sortDirection === 'asc'
                                "
                                class="sort-icon"
                                size="14"
                            />
                            <ChevronDown
                                v-else-if="
                                    sortField === 'created_at' &&
                                    sortDirection === 'desc'
                                "
                                class="sort-icon"
                                size="14"
                            />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="release in sortedReleases" :key="release.id">
                        <td class="cover-column">
                            <div class="cover-image">
                                <img
                                    :src="release.cover_image_url"
                                    :alt="release.title"
                                />
                            </div>
                        </td>
                        <td>
                            <Link
                                :href="`/releases/${release.id}`"
                                class="release-title"
                            >
                                {{ release.title }}
                            </Link>
                        </td>
                        <td>{{ release.artist.display_name }}</td>
                        <td class="hide-mobile">
                            {{ getTypeName(release.type) }}
                        </td>
                        <td>
                            <div
                                class="status-badge"
                                :class="getStatusClass(release.status)"
                            >
                                <component
                                    :is="getStatusIcon(release.status)"
                                    size="14"
                                    class="status-icon"
                                />
                                <span>{{ getStatusName(release.status) }}</span>
                            </div>
                        </td>
                        <td class="hide-mobile">
                            {{ formatDate(release.created_at) }}
                        </td>
                    </tr>
                    <tr v-if="!sortedReleases.length">
                        <td colspan="6" class="empty-message">
                            Нет доступных релизов
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<style lang="scss">
.releases-table-container {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.view-all-link {
    color: var(--primary-color);
    font-size: 0.875rem;
    text-decoration: none;

    &:hover {
        text-decoration: underline;
    }
}

.table-responsive {
    width: 100%;
    overflow-x: auto;
}

.releases-table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
        text-align: left;
        padding: 12px 16px;
    }

    th {
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 500;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        white-space: nowrap;

        &.sortable {
            cursor: pointer;
            user-select: none;

            &:hover {
                color: var(--text-primary);
            }
        }
    }

    td {
        border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        font-size: 0.875rem;
    }

    tbody tr:last-child td {
        border-bottom: none;
    }

    tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.01);
    }
}

.cover-column {
    width: 48px;
}

.cover-image {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-sm);
    overflow: hidden;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.sort-icon {
    vertical-align: middle;
    margin-left: 4px;
}

.release-title {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;

    &:hover {
        color: var(--primary-color);
        text-decoration: underline;
    }
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    white-space: nowrap;

    &.status-approved {
        background-color: rgba(76, 175, 80, 0.1);
        color: var(--success-color);
    }

    &.status-pending {
        background-color: rgba(255, 152, 0, 0.1);
        color: var(--warning-color);
    }

    &.status-rejected {
        background-color: rgba(244, 67, 54, 0.1);
        color: var(--danger-color);
    }

    &.status-draft {
        background-color: rgba(158, 158, 158, 0.1);
        color: var(--text-secondary);
    }
}

.status-icon {
    flex-shrink: 0;
}

.empty-message {
    text-align: center;
    color: var(--text-disabled);
    padding: 32px !important;
}

/* Адаптивность */
@media (max-width: 768px) {
    .hide-mobile {
        display: none;
    }
}

@media (max-width: 576px) {
    .releases-table {
        th,
        td {
            padding: 8px 12px;
        }
    }

    .cover-image {
        width: 32px;
        height: 32px;
    }
}
</style>
