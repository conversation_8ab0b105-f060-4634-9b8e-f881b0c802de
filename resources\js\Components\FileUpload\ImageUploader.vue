<script setup>
import { ref, computed, watch } from "vue";
import {
    Upload,
    X,
    Image as ImageIcon,
    Camera,
    Crop,
    RotateCw,
    ZoomIn,
    Download,
    AlertCircle,
} from "lucide-vue-next";

const props = defineProps({
    modelValue: {
        type: [File, String, null],
        default: null,
    },
    accept: {
        type: String,
        default: "image/jpeg,image/png,image/webp,image/gif",
    },
    maxSize: {
        type: Number,
        default: 5 * 1024 * 1024, // 5MB
    },
    aspectRatio: {
        type: String,
        default: "1:1", // 1:1, 16:9, 4:3, free
    },
    minWidth: {
        type: Number,
        default: 300,
    },
    minHeight: {
        type: Number,
        default: 300,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: "Выберите изображение",
    },
    showPreview: {
        type: Boolean,
        default: true,
    },
    allowCrop: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(["update:modelValue", "imageSelected", "imageRemoved", "error"]);

// Состояние компонента
const isDragOver = ref(false);
const fileInput = ref(null);
const imagePreview = ref(null);
const imageError = ref("");
const isLoading = ref(false);

// Вычисляемые свойства
const hasImage = computed(() => {
    return props.modelValue || imagePreview.value;
});

const previewUrl = computed(() => {
    if (imagePreview.value) return imagePreview.value;
    if (typeof props.modelValue === "string") return props.modelValue;
    return null;
});

const allowedTypes = computed(() => {
    return props.accept.split(",").map(type => type.trim());
});

// Методы обработки файлов
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        processImage(file);
    }
}

function handleDrop(event) {
    event.preventDefault();
    isDragOver.value = false;
    
    if (props.disabled) return;
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        processImage(files[0]);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    if (!props.disabled) {
        isDragOver.value = true;
    }
}

function handleDragLeave() {
    isDragOver.value = false;
}

function processImage(file) {
    imageError.value = "";
    isLoading.value = true;

    // Валидация файла
    const validation = validateImage(file);
    if (!validation.valid) {
        imageError.value = validation.error;
        isLoading.value = false;
        emit("error", validation.error);
        return;
    }

    // Создание превью
    const reader = new FileReader();
    reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
            // Проверка размеров изображения
            if (img.width < props.minWidth || img.height < props.minHeight) {
                imageError.value = `Минимальный размер изображения: ${props.minWidth}×${props.minHeight}px`;
                isLoading.value = false;
                emit("error", imageError.value);
                return;
            }

            imagePreview.value = e.target.result;
            emit("update:modelValue", file);
            emit("imageSelected", file);
            isLoading.value = false;
        };
        
        img.onerror = () => {
            imageError.value = "Не удалось загрузить изображение";
            isLoading.value = false;
            emit("error", imageError.value);
        };
        
        img.src = e.target.result;
    };
    
    reader.onerror = () => {
        imageError.value = "Ошибка чтения файла";
        isLoading.value = false;
        emit("error", imageError.value);
    };
    
    reader.readAsDataURL(file);
}

function validateImage(file) {
    // Проверка типа файла
    if (!allowedTypes.value.includes(file.type)) {
        return {
            valid: false,
            error: "Поддерживаются только изображения JPEG, PNG, WebP и GIF"
        };
    }

    // Проверка размера файла
    if (file.size > props.maxSize) {
        return {
            valid: false,
            error: `Размер файла не должен превышать ${formatFileSize(props.maxSize)}`
        };
    }

    return { valid: true };
}

function removeImage() {
    imagePreview.value = null;
    imageError.value = "";
    emit("update:modelValue", null);
    emit("imageRemoved");
    
    // Очищаем input
    if (fileInput.value) {
        fileInput.value.value = "";
    }
}

function openFileDialog() {
    if (!props.disabled) {
        fileInput.value?.click();
    }
}

// Утилиты
function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function getAspectRatioClass() {
    switch (props.aspectRatio) {
        case "16:9":
            return "aspect-16-9";
        case "4:3":
            return "aspect-4-3";
        case "1:1":
            return "aspect-1-1";
        default:
            return "aspect-free";
    }
}

// Watchers
watch(() => props.modelValue, (newValue) => {
    if (!newValue) {
        imagePreview.value = null;
        imageError.value = "";
    } else if (typeof newValue === "string") {
        imagePreview.value = newValue;
    }
});
</script>

<template>
    <div class="image-uploader">
        <!-- Превью изображения -->
        <div v-if="hasImage && showPreview" class="image-preview" :class="getAspectRatioClass()">
            <img 
                :src="previewUrl" 
                :alt="placeholder"
                class="preview-image"
                @error="imageError = 'Ошибка загрузки изображения'"
            />
            
            <!-- Оверлей с действиями -->
            <div class="image-overlay">
                <div class="overlay-actions">
                    <button
                        v-if="allowCrop"
                        type="button"
                        class="overlay-btn"
                        title="Обрезать"
                    >
                        <Crop size="16" />
                    </button>
                    
                    <button
                        type="button"
                        class="overlay-btn"
                        @click="openFileDialog"
                        title="Заменить изображение"
                    >
                        <Camera size="16" />
                    </button>
                    
                    <button
                        type="button"
                        class="overlay-btn remove-btn"
                        @click="removeImage"
                        title="Удалить изображение"
                    >
                        <X size="16" />
                    </button>
                </div>
            </div>
        </div>

        <!-- Область загрузки -->
        <div
            v-else
            class="upload-area"
            :class="{
                'drag-over': isDragOver,
                'disabled': disabled,
                'has-error': imageError,
                [getAspectRatioClass()]: true,
            }"
            @drop="handleDrop"
            @dragover="handleDragOver"
            @dragleave="handleDragLeave"
            @click="openFileDialog"
        >
            <div class="upload-content">
                <div v-if="isLoading" class="loading-state">
                    <div class="loading-spinner"></div>
                    <p class="loading-text">Загрузка изображения...</p>
                </div>
                
                <div v-else class="upload-state">
                    <ImageIcon class="upload-icon" size="48" />
                    <p class="upload-text">{{ placeholder }}</p>
                    <p class="upload-hint">
                        Перетащите изображение сюда или нажмите для выбора
                    </p>
                    <p class="upload-specs">
                        {{ allowedTypes.map(t => t.split('/')[1].toUpperCase()).join(', ') }} • 
                        Макс. {{ formatFileSize(maxSize) }} • 
                        Мин. {{ minWidth }}×{{ minHeight }}px
                    </p>
                </div>
            </div>
            
            <input
                ref="fileInput"
                type="file"
                class="file-input"
                :accept="accept"
                :disabled="disabled"
                @change="handleFileSelect"
            />
        </div>

        <!-- Ошибки -->
        <div v-if="imageError" class="error-message">
            <AlertCircle class="error-icon" size="16" />
            <span>{{ imageError }}</span>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.image-uploader {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.image-preview {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.image-preview:hover .image-overlay {
    opacity: 1;
}

.overlay-actions {
    display: flex;
    gap: 12px;
}

.overlay-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
        background: white;
        transform: scale(1.1);
    }

    &.remove-btn {
        background: rgba(220, 53, 69, 0.9);
        color: white;

        &:hover {
            background: var(--danger-color);
        }
    }
}

.upload-area {
    border: 2px dashed rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-lg);
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    position: relative;

    &:hover:not(.disabled) {
        border-color: var(--primary-color);
        background: rgba(var(--primary-rgb), 0.05);
    }

    &.drag-over {
        border-color: var(--primary-color);
        background: rgba(var(--primary-rgb), 0.1);
        transform: scale(1.02);
    }

    &.disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: var(--bg-secondary);
    }

    &.has-error {
        border-color: var(--danger-color);
        background: rgba(220, 53, 69, 0.05);
    }
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 12px;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(var(--primary-rgb), 0.2);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    color: var(--primary-color);
    font-weight: 500;
    margin: 0;
}

.upload-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.upload-icon {
    color: var(--text-secondary);
    transition: color 0.2s ease;
}

.upload-area:hover:not(.disabled) .upload-icon {
    color: var(--primary-color);
}

.upload-text {
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.upload-hint {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.upload-specs {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

.file-input {
    display: none;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: var(--radius-md);
    color: var(--danger-color);
    font-size: 0.875rem;
}

.error-icon {
    flex-shrink: 0;
}

// Aspect ratio classes
.aspect-1-1 {
    aspect-ratio: 1 / 1;
}

.aspect-16-9 {
    aspect-ratio: 16 / 9;
}

.aspect-4-3 {
    aspect-ratio: 4 / 3;
}

.aspect-free {
    min-height: 200px;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .upload-area {
        padding: 30px 15px;
    }

    .overlay-actions {
        gap: 8px;
    }

    .overlay-btn {
        width: 36px;
        height: 36px;
    }
}
</style>
