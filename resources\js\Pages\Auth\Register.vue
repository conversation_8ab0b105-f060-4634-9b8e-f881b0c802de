<script setup>
import { Link, useForm } from "@inertiajs/vue3";
import AuthLayout from "@/Layouts/AuthLayout.vue";
import Checkbox from "@/Components/Checkbox.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import TextInput from "@/Components/TextInput.vue";
import OAuthButtons from "@/Components/Auth/OAuthButtons.vue";

defineProps({
    oauthProviders: {
        type: Object,
        default: () => ({}),
    },
});

const form = useForm({
    name: "",
    email: "",
    password: "",
    password_confirmation: "",
    terms: false,
});

const submit = () => {
    form.post(route("register"), {
        onFinish: () => form.reset("password", "password_confirmation"),
    });
};
</script>

<template>
    <AuthLayout title="Регистрация">
        <div class="auth-form">
            <h2 class="form-title">Регистрация</h2>
            <p class="form-subtitle">Создайте аккаунт для доступа к системе</p>

            <form @submit.prevent="submit">
                <div class="form-group">
                    <InputLabel for="name" value="Имя" />
                    <TextInput
                        id="name"
                        v-model="form.name"
                        type="text"
                        class="form-control"
                        required
                        autofocus
                        autocomplete="name"
                    />
                    <InputError
                        class="error-text"
                        :message="form.errors.name"
                    />
                </div>

                <div class="form-group">
                    <InputLabel for="email" value="Email" />
                    <TextInput
                        id="email"
                        v-model="form.email"
                        type="email"
                        class="form-control"
                        required
                        autocomplete="username"
                    />
                    <InputError
                        class="error-text"
                        :message="form.errors.email"
                    />
                </div>

                <div class="form-group">
                    <InputLabel for="password" value="Пароль" />
                    <TextInput
                        id="password"
                        v-model="form.password"
                        type="password"
                        class="form-control"
                        required
                        autocomplete="new-password"
                    />
                    <InputError
                        class="error-text"
                        :message="form.errors.password"
                    />
                </div>

                <div class="form-group">
                    <InputLabel
                        for="password_confirmation"
                        value="Подтверждение пароля"
                    />
                    <TextInput
                        id="password_confirmation"
                        v-model="form.password_confirmation"
                        type="password"
                        class="form-control"
                        required
                        autocomplete="new-password"
                    />
                    <InputError
                        class="error-text"
                        :message="form.errors.password_confirmation"
                    />
                </div>

                <div
                    v-if="$page.props.jetstream.hasTermsAndPrivacyPolicyFeature"
                    class="form-group"
                >
                    <label class="terms-container">
                        <Checkbox
                            id="terms"
                            v-model:checked="form.terms"
                            name="terms"
                            required
                        />
                        <span class="terms-text">
                            Я согласен с
                            <a
                                target="_blank"
                                :href="route('terms.show')"
                                class="terms-link"
                                >Условиями использования</a
                            >
                            и
                            <a
                                target="_blank"
                                :href="route('policy.show')"
                                class="terms-link"
                                >Политикой конфиденциальности</a
                            >
                        </span>
                    </label>
                    <InputError
                        class="error-text"
                        :message="form.errors.terms"
                    />
                </div>

                <div class="form-actions">
                    <PrimaryButton
                        class="register-btn"
                        :class="{ disabled: form.processing }"
                        :disabled="form.processing"
                    >
                        {{
                            form.processing
                                ? "Регистрация..."
                                : "Зарегистрироваться"
                        }}
                    </PrimaryButton>
                </div>

                <div class="form-footer">
                    <span>Уже есть аккаунт?</span>
                    <Link :href="route('login')" class="login-link">
                        Войти
                    </Link>
                </div>
            </form>

            <!-- OAuth кнопки -->
            <OAuthButtons :providers="oauthProviders" />
        </div>
    </AuthLayout>
</template>

<style lang="scss">
.auth-form {
    width: 100%;

    .form-title {
        font-size: 1.75rem;
        font-weight: 700;
        margin: 0 0 8px;
        color: var(--text-primary);
    }

    .form-subtitle {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin: 0 0 32px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: var(--radius-md);
        font-size: 0.9375rem;
        margin-top: 6px;
        transition: border-color 0.2s;

        &:focus {
            border-color: var(--primary-color);
            outline: none;
        }
    }

    .error-text {
        color: var(--danger-color);
        font-size: 0.75rem;
        margin-top: 6px;
    }

    .terms-container {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        cursor: pointer;
    }

    .terms-text {
        font-size: 0.875rem;
        color: var(--text-secondary);
        line-height: 1.5;
    }

    .terms-link {
        color: var(--primary-color);
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }

    .form-actions {
        margin-bottom: 24px;
        margin-top: 32px;
    }

    .register-btn {
        width: 100%;
        padding: 12px;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: var(--radius-md);
        font-size: 0.9375rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
            background-color: var(--secondary-color);
        }

        &.disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
    }

    .form-footer {
        text-align: center;
        font-size: 0.875rem;
        color: var(--text-secondary);

        .login-link {
            color: var(--primary-color);
            text-decoration: none;
            margin-left: 6px;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}
</style>
