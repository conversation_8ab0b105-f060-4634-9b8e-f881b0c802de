import { defineStore } from "pinia";
import { ref, computed } from "vue";

export const useNotificationStore = defineStore("notifications", () => {
    // State
    const notifications = ref([]);
    const unreadCount = ref(0);
    const loading = ref(false);
    const isConnected = ref(false);

    // Getters
    const unreadNotifications = computed(() =>
        notifications.value.filter((n) => !n.read_at)
    );

    const readNotifications = computed(() =>
        notifications.value.filter((n) => n.read_at)
    );

    // Actions
    const initializeNotifications = (
        initialNotifications = [],
        initialUnreadCount = 0
    ) => {
        notifications.value = initialNotifications;
        unreadCount.value = initialUnreadCount;
    };

    const updateUnreadCount = () => {
        // Подсчитываем непрочитанные уведомления локально
        unreadCount.value = notifications.value.filter(
            (n) => !n.read_at
        ).length;
    };

    const markAsRead = (notificationId) => {
        // Обновляем локальное состояние
        const notification = notifications.value.find(
            (n) => n.id === notificationId
        );
        if (notification && !notification.read_at) {
            notification.read_at = new Date().toISOString();
            updateUnreadCount();
        }
    };

    const markAllAsRead = () => {
        // Обновляем все уведомления
        notifications.value.forEach((notification) => {
            if (!notification.read_at) {
                notification.read_at = new Date().toISOString();
            }
        });
        updateUnreadCount();
    };

    const deleteNotification = (notificationId) => {
        // Удаляем из локального состояния
        const index = notifications.value.findIndex(
            (n) => n.id === notificationId
        );
        if (index !== -1) {
            notifications.value.splice(index, 1);
            updateUnreadCount();
        }
    };

    const clearAllNotifications = () => {
        notifications.value = [];
        unreadCount.value = 0;
    };

    const addNotification = (notification) => {
        // Добавляем новое уведомление в начало списка
        notifications.value.unshift(notification);
        unreadCount.value++;
    };

    const setupRealTimeListeners = (user) => {
        if (!window.Echo || !user) {
            console.log("Echo не доступен или пользователь не авторизован");
            isConnected.value = false;
            return;
        }

        try {
            // Проверяем, что Echo настроен правильно
            if (
                window.Echo.connector &&
                window.Echo.connector.name === "null"
            ) {
                console.log(
                    "Используется null broadcaster - real-time уведомления отключены"
                );
                isConnected.value = false;
                return;
            }

            // Слушаем личный канал пользователя для уведомлений
            window.Echo.private(`App.Models.User.${user.id}`).notification(
                (notification) => {
                    console.log("Получено новое уведомление:", notification);
                    addNotification(notification);
                }
            );

            // Если пользователь админ или менеджер, слушаем админский канал
            if (["admin", "manager"].includes(user.role)) {
                window.Echo.private("admin.applications").listen(
                    "ApplicationCreated",
                    (e) => {
                        console.log("Новая заявка создана:", e);
                        // Уведомление будет получено через личный канал
                    }
                );
            }

            isConnected.value = true;
            console.log(
                "Real-time уведомления подключены для пользователя:",
                user.name
            );
        } catch (error) {
            console.error(
                "Ошибка подключения к real-time уведомлениям:",
                error
            );
            isConnected.value = false;
        }
    };

    const disconnectRealTime = () => {
        if (window.Echo) {
            window.Echo.disconnect();
            isConnected.value = false;
        }
    };

    return {
        // State
        notifications,
        unreadCount,
        loading,
        isConnected,

        // Getters
        unreadNotifications,
        readNotifications,

        // Actions
        initializeNotifications,
        updateUnreadCount,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        clearAllNotifications,
        addNotification,
        setupRealTimeListeners,
        disconnectRealTime,
    };
});
