<script setup>
import { ref, computed, watch } from "vue";
import { router } from "@inertiajs/vue3";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import ApplicationModal from "@/Components/Application/ApplicationModal.vue";
import { $confirm } from "@/composables/useConfirm";
import {
    Search,
    Filter,
    Eye,
    CheckCircle,
    X,
    Clock,
    User,
    Calendar,
    FileText,
    MoreVertical,
    Check,
    Trash2,
    MessageSquare,
} from "lucide-vue-next";

const props = defineProps({
    applications: {
        type: Object,
        required: true,
    },
    filters: {
        type: Object,
        required: true,
    },
});

// Состояние компонента
const showModal = ref(false);
const modalMode = ref("view");
const selectedApplication = ref(null);
const searchQuery = ref(props.filters.search || "");
const statusFilter = ref(props.filters.status || "all");
const typeFilter = ref(props.filters.type || "all");
const showFilters = ref(false);
const selectedItems = ref([]);
const showBulkActions = ref(false);

// Конфигурация статусов
const statusConfig = {
    pending: { label: "На рассмотрении", color: "warning", icon: Clock },
    approved: { label: "Одобрена", color: "success", icon: CheckCircle },
    rejected: { label: "Отклонена", color: "danger", icon: X },
};

// Типы заявок
const applicationTypes = {
    promo: "Промо-кампания",
    collaboration: "Сотрудничество",
    release: "Релиз",
    custom: "Другое",
};

// Опции фильтров
const statusOptions = [
    { value: "all", label: "Все статусы" },
    { value: "pending", label: "На рассмотрении" },
    { value: "approved", label: "Одобрены" },
    { value: "rejected", label: "Отклонены" },
];

const typeOptions = [
    { value: "all", label: "Все типы" },
    { value: "promo", label: "Промо-кампания" },
    { value: "collaboration", label: "Сотрудничество" },
    { value: "release", label: "Релиз" },
    { value: "custom", label: "Другое" },
];

// Вычисляемые свойства
const hasFilters = computed(() => {
    return (
        searchQuery.value ||
        statusFilter.value !== "all" ||
        typeFilter.value !== "all"
    );
});

const allSelected = computed(() => {
    return (
        props.applications.data.length > 0 &&
        selectedItems.value.length === props.applications.data.length
    );
});

const someSelected = computed(() => {
    return (
        selectedItems.value.length > 0 &&
        selectedItems.value.length < props.applications.data.length
    );
});

// Методы управления модальным окном
function openViewModal(application) {
    selectedApplication.value = application;
    modalMode.value = "view";
    showModal.value = true;
}

function openReviewModal(application) {
    selectedApplication.value = application;
    modalMode.value = "review";
    showModal.value = true;
}

function closeModal() {
    showModal.value = false;
    selectedApplication.value = null;
}

// Обработчики событий
function onApplicationReviewed() {
    router.reload({ only: ["applications"] });
}

// Фильтрация и поиск
function applyFilters() {
    const params = {
        search: searchQuery.value || undefined,
        status: statusFilter.value !== "all" ? statusFilter.value : undefined,
        type: typeFilter.value !== "all" ? typeFilter.value : undefined,
    };

    router.get("/admin/applications", params, {
        preserveState: true,
        replace: true,
    });
}

function clearFilters() {
    searchQuery.value = "";
    statusFilter.value = "all";
    typeFilter.value = "all";
    applyFilters();
}

// Обновление статуса заявки
async function updateApplicationStatus(application, newStatus) {
    if (application.status === newStatus) return;

    const statusLabels = {
        pending: "на рассмотрение",
        approved: "одобрить",
        rejected: "отклонить",
    };

    const confirmed = await $confirm.show(
        `${
            statusLabels[newStatus].charAt(0).toUpperCase() +
            statusLabels[newStatus].slice(1)
        } заявку "${application.title}"?`,
        "Подтверждение действия"
    );

    if (confirmed) {
        router.post(
            `/admin/applications/${application.id}/status`,
            {
                status: newStatus,
            },
            {
                preserveScroll: true,
            }
        );
    }
}

// Быстрые действия
async function quickApprove(application) {
    const confirmed = await $confirm.show(
        `Одобрить заявку "${application.title}"?`,
        "Подтверждение"
    );

    if (confirmed) {
        router.post(
            `/admin/applications/${application.id}/status`,
            {
                status: "approved",
            },
            {
                preserveScroll: true,
            }
        );
    }
}

async function quickReject(application) {
    const confirmed = await $confirm.danger(
        `Отклонить заявку "${application.title}"?`,
        "Подтверждение"
    );

    if (confirmed) {
        router.post(
            `/admin/applications/${application.id}/status`,
            {
                status: "rejected",
            },
            {
                preserveScroll: true,
            }
        );
    }
}

async function deleteApplication(application) {
    const confirmed = await $confirm.danger(
        `Заявка "${application.title}" будет удалена безвозвратно.`,
        "Удалить заявку?"
    );

    if (confirmed) {
        router.delete(`/admin/applications/${application.id}`, {
            preserveScroll: true,
        });
    }
}

// Массовые действия
function toggleSelectAll() {
    if (allSelected.value) {
        selectedItems.value = [];
    } else {
        selectedItems.value = props.applications.data.map((app) => app.id);
    }
}

function toggleSelectItem(applicationId) {
    const index = selectedItems.value.indexOf(applicationId);
    if (index > -1) {
        selectedItems.value.splice(index, 1);
    } else {
        selectedItems.value.push(applicationId);
    }
}

async function bulkAction(action) {
    if (selectedItems.value.length === 0) return;

    let confirmed = false;
    let message = "";

    switch (action) {
        case "approve":
            message = `Одобрить ${selectedItems.value.length} заявок?`;
            confirmed = await $confirm.show(message);
            break;
        case "reject":
            message = `Отклонить ${selectedItems.value.length} заявок?`;
            confirmed = await $confirm.danger(message);
            break;
        case "delete":
            message = `Удалить ${selectedItems.value.length} заявок безвозвратно?`;
            confirmed = await $confirm.danger(message);
            break;
    }

    if (confirmed) {
        router.post(
            "/admin/applications/bulk",
            {
                action,
                application_ids: selectedItems.value,
            },
            {
                preserveScroll: true,
                onSuccess: () => {
                    selectedItems.value = [];
                    showBulkActions.value = false;
                },
            }
        );
    }
}

// Форматирование даты
function formatDate(date) {
    return new Date(date).toLocaleDateString("ru-RU", {
        year: "numeric",
        month: "short",
        day: "numeric",
    });
}

// Watchers для автоматического применения фильтров
watch([searchQuery], () => {
    const timeoutId = setTimeout(() => {
        applyFilters();
    }, 500);

    return () => clearTimeout(timeoutId);
});

watch([statusFilter, typeFilter], () => {
    applyFilters();
});

watch(
    selectedItems,
    (newValue) => {
        showBulkActions.value = newValue.length > 0;
    },
    { deep: true }
);
</script>

<template>
    <DashboardLayout title="Управление заявками">
        <template #header>
            <div class="page-header">
                <div class="header-content">
                    <h1 class="page-title">Управление заявками</h1>
                    <p class="page-subtitle">
                        Модерация и управление заявками пользователей
                    </p>
                </div>
            </div>
        </template>

        <div class="admin-applications">
            <!-- Фильтры и поиск -->
            <div class="filters-section">
                <div class="search-bar">
                    <div class="search-input-wrapper">
                        <Search class="search-icon" size="20" />
                        <input
                            v-model="searchQuery"
                            type="text"
                            class="search-input"
                            placeholder="Поиск по заголовку, описанию или автору..."
                        />
                    </div>

                    <button
                        type="button"
                        class="filter-toggle"
                        :class="{ active: showFilters }"
                        @click="showFilters = !showFilters"
                    >
                        <Filter size="16" />
                        <span>Фильтры</span>
                    </button>
                </div>

                <!-- Расширенные фильтры -->
                <Transition name="filters">
                    <div v-if="showFilters" class="filters-panel">
                        <div class="filter-group">
                            <label class="filter-label">Статус</label>
                            <select
                                v-model="statusFilter"
                                class="filter-select"
                            >
                                <option
                                    v-for="option in statusOptions"
                                    :key="option.value"
                                    :value="option.value"
                                >
                                    {{ option.label }}
                                </option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">Тип</label>
                            <select v-model="typeFilter" class="filter-select">
                                <option
                                    v-for="option in typeOptions"
                                    :key="option.value"
                                    :value="option.value"
                                >
                                    {{ option.label }}
                                </option>
                            </select>
                        </div>

                        <div class="filter-actions">
                            <button
                                v-if="hasFilters"
                                type="button"
                                class="btn btn-outline btn-sm"
                                @click="clearFilters"
                            >
                                Сбросить
                            </button>
                        </div>
                    </div>
                </Transition>
            </div>

            <!-- Массовые действия -->
            <Transition name="bulk-actions">
                <div v-if="showBulkActions" class="bulk-actions-bar">
                    <div class="bulk-info">
                        <span>Выбрано: {{ selectedItems.length }}</span>
                    </div>

                    <div class="bulk-actions">
                        <button
                            type="button"
                            class="btn btn-success btn-sm"
                            @click="bulkAction('approve')"
                        >
                            <CheckCircle size="16" />
                            <span>Одобрить</span>
                        </button>

                        <button
                            type="button"
                            class="btn btn-danger btn-sm"
                            @click="bulkAction('reject')"
                        >
                            <X size="16" />
                            <span>Отклонить</span>
                        </button>

                        <button
                            type="button"
                            class="btn btn-outline btn-sm"
                            @click="bulkAction('delete')"
                        >
                            <Trash2 size="16" />
                            <span>Удалить</span>
                        </button>
                    </div>
                </div>
            </Transition>

            <!-- Список заявок -->
            <div class="applications-table">
                <div class="table-header">
                    <div class="header-cell checkbox-cell">
                        <input
                            type="checkbox"
                            class="checkbox"
                            :checked="allSelected"
                            :indeterminate="someSelected"
                            @change="toggleSelectAll"
                        />
                    </div>
                    <div class="header-cell">Заявка</div>
                    <div class="header-cell">Автор</div>
                    <div class="header-cell">Тип</div>
                    <div class="header-cell">Статус</div>
                    <div class="header-cell">Дата</div>
                    <div class="header-cell actions-cell">Действия</div>
                </div>

                <div class="table-body">
                    <div
                        v-for="application in applications.data"
                        :key="application.id"
                        class="table-row"
                        :class="{
                            selected: selectedItems.includes(application.id),
                        }"
                    >
                        <div class="table-cell checkbox-cell">
                            <input
                                type="checkbox"
                                class="checkbox"
                                :checked="
                                    selectedItems.includes(application.id)
                                "
                                @change="toggleSelectItem(application.id)"
                            />
                        </div>

                        <div class="table-cell">
                            <div class="application-info">
                                <h4 class="application-title">
                                    {{ application.title }}
                                </h4>
                                <p class="application-description">
                                    {{ application.description }}
                                </p>
                            </div>
                        </div>

                        <div class="table-cell">
                            <div class="user-info">
                                <span class="user-name">{{
                                    application.user?.name || "Неизвестно"
                                }}</span>
                                <span class="user-email">{{
                                    application.user?.email
                                }}</span>
                            </div>
                        </div>

                        <div class="table-cell">
                            <span class="type-badge">
                                {{
                                    applicationTypes[application.type] ||
                                    application.type
                                }}
                            </span>
                        </div>

                        <div class="table-cell">
                            <select
                                class="status-select"
                                :value="application.status"
                                @change="
                                    updateApplicationStatus(
                                        application,
                                        $event.target.value
                                    )
                                "
                                :class="`status-${application.status}`"
                            >
                                <option value="pending">На рассмотрении</option>
                                <option value="approved">Одобрена</option>
                                <option value="rejected">Отклонена</option>
                            </select>
                        </div>

                        <div class="table-cell">
                            <span class="date-text">{{
                                formatDate(application.created_at)
                            }}</span>
                        </div>

                        <div class="table-cell actions-cell">
                            <div class="actions-dropdown">
                                <button type="button" class="dropdown-toggle">
                                    <MoreVertical size="16" />
                                </button>

                                <div class="dropdown-menu">
                                    <button
                                        type="button"
                                        class="dropdown-item"
                                        @click="openViewModal(application)"
                                    >
                                        <Eye size="14" />
                                        <span>Просмотр</span>
                                    </button>

                                    <button
                                        v-if="application.status === 'pending'"
                                        type="button"
                                        class="dropdown-item"
                                        @click="openReviewModal(application)"
                                    >
                                        <MessageSquare size="14" />
                                        <span>Рассмотреть</span>
                                    </button>

                                    <button
                                        v-if="application.status === 'pending'"
                                        type="button"
                                        class="dropdown-item success"
                                        @click="quickApprove(application)"
                                    >
                                        <CheckCircle size="14" />
                                        <span>Одобрить</span>
                                    </button>

                                    <button
                                        v-if="application.status === 'pending'"
                                        type="button"
                                        class="dropdown-item danger"
                                        @click="quickReject(application)"
                                    >
                                        <X size="14" />
                                        <span>Отклонить</span>
                                    </button>

                                    <button
                                        type="button"
                                        class="dropdown-item danger"
                                        @click="deleteApplication(application)"
                                    >
                                        <Trash2 size="14" />
                                        <span>Удалить</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Пустое состояние -->
                <div v-if="applications.data.length === 0" class="empty-state">
                    <FileText class="empty-icon" size="48" />
                    <h3 class="empty-title">Заявок не найдено</h3>
                    <p class="empty-text">
                        {{
                            hasFilters
                                ? "По вашему запросу ничего не найдено. Попробуйте изменить фильтры."
                                : "Пока нет заявок для модерации."
                        }}
                    </p>
                </div>
            </div>

            <!-- Пагинация -->
            <div v-if="applications.links.length > 3" class="pagination">
                <Link
                    v-for="link in applications.links"
                    :key="link.label"
                    :href="link.url"
                    class="pagination-link"
                    :class="{
                        active: link.active,
                        disabled: !link.url,
                    }"
                    v-html="link.label"
                />
            </div>
        </div>

        <!-- Модальное окно -->
        <ApplicationModal
            :show="showModal"
            :mode="modalMode"
            :application="selectedApplication"
            :can-review="true"
            @close="closeModal"
            @reviewed="onApplicationReviewed"
        />
    </DashboardLayout>
</template>

<style lang="scss" scoped>
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    margin-bottom: 32px;

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}

.header-content {
    flex: 1;
}

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px;
}

.page-subtitle {
    color: var(--text-secondary);
    margin: 0;
}

.admin-applications {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.filters-section {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    padding: 24px;
}

.search-bar {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-bottom: 16px;
}

.search-input-wrapper {
    position: relative;
    flex: 1;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-input {
    width: 100%;
    padding: 12px 12px 12px 44px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }

    &::placeholder {
        color: var(--text-secondary);
    }
}

.filter-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;

    &:hover {
        background: rgba(0, 0, 0, 0.05);
    }

    &.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }
}

.filters-panel {
    display: flex;
    gap: 16px;
    align-items: flex-end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 150px;
}

.filter-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }
}

.filter-actions {
    display: flex;
    gap: 8px;
}

.bulk-actions-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    padding: 16px 24px;
    background: rgba(var(--primary-rgb), 0.05);
    border: 1px solid rgba(var(--primary-rgb), 0.2);
    border-radius: var(--radius-lg);
}

.bulk-info {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.bulk-actions {
    display: flex;
    gap: 8px;
}

.applications-table {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 40px 1fr 200px 120px 120px 100px 120px;
    gap: 16px;
    padding: 16px 24px;
    background: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-body {
    display: flex;
    flex-direction: column;
}

.table-row {
    display: grid;
    grid-template-columns: 40px 1fr 200px 120px 120px 100px 120px;
    gap: 16px;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.02);
    }

    &.selected {
        background: rgba(var(--primary-rgb), 0.05);
        border-color: rgba(var(--primary-rgb), 0.2);
    }

    &:last-child {
        border-bottom: none;
    }
}

.table-cell {
    display: flex;
    align-items: center;
    min-height: 40px;
}

.checkbox-cell {
    justify-content: center;
}

.actions-cell {
    justify-content: flex-end;
}

.checkbox {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-sm);
    cursor: pointer;
    accent-color: var(--primary-color);
}

.application-info {
    min-width: 0;
}

.application-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px;
    line-height: 1.4;
}

.application-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.user-email {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.type-badge {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    background: rgba(0, 0, 0, 0.05);
    color: var(--text-primary);
}

.status-select {
    padding: 4px 8px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
    }

    &.status-pending {
        background: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
        border-color: rgba(245, 158, 11, 0.3);
    }

    &.status-approved {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
        border-color: rgba(34, 197, 94, 0.3);
    }

    &.status-rejected {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        border-color: rgba(239, 68, 68, 0.3);
    }
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;

    &.warning {
        background: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
    }

    &.success {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
    }

    &.danger {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
    }
}

.status-icon {
    flex-shrink: 0;
}

.date-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.actions-dropdown {
    position: relative;
}

.dropdown-toggle {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.05);
        color: var(--text-primary);
    }
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 8px 0;
    min-width: 150px;
    z-index: 10;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
}

.actions-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        background: rgba(0, 0, 0, 0.05);
    }

    &.success {
        color: #22c55e;
    }

    &.danger {
        color: #ef4444;
    }
}

.empty-state {
    text-align: center;
    padding: 60px 24px;
    color: var(--text-secondary);
}

.empty-icon {
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px;
}

.empty-text {
    margin: 0;
    line-height: 1.5;
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 24px;
}

.pagination-link {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s ease;

    &:hover:not(.disabled) {
        background: rgba(0, 0, 0, 0.05);
    }

    &.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    text-decoration: none;
    font-size: 0.875rem;

    &.btn-sm {
        padding: 6px 12px;
        font-size: 0.75rem;
    }

    &.btn-outline {
        background: transparent;
        border: 1px solid rgba(0, 0, 0, 0.2);
        color: var(--text-primary);

        &:hover {
            background: rgba(0, 0, 0, 0.05);
        }
    }

    &.btn-success {
        background: #22c55e;
        color: white;

        &:hover {
            background: #16a34a;
        }
    }

    &.btn-danger {
        background: #ef4444;
        color: white;

        &:hover {
            background: #dc2626;
        }
    }
}

// Transitions
.filters-enter-active,
.filters-leave-active {
    transition: all 0.3s ease;
}

.filters-enter-from,
.filters-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

.bulk-actions-enter-active,
.bulk-actions-leave-active {
    transition: all 0.3s ease;
}

.bulk-actions-enter-from,
.bulk-actions-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

@media (max-width: 1024px) {
    .table-header,
    .table-row {
        grid-template-columns: 40px 1fr 100px 80px;
        gap: 12px;
    }

    .table-cell:nth-child(3),
    .table-cell:nth-child(6) {
        display: none;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }

    .search-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .filters-panel {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .bulk-actions-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .bulk-actions {
        justify-content: stretch;
        flex-wrap: wrap;
    }

    .btn {
        justify-content: center;
        flex: 1;
        min-width: 120px;
    }

    .applications-table {
        border: none;
        background: transparent;
    }

    .table-header {
        display: none;
    }

    .table-body {
        gap: 12px;
    }

    .table-row {
        display: block;
        padding: 20px;
        border-radius: var(--radius-lg);
        margin-bottom: 0;
        border: 1px solid rgba(0, 0, 0, 0.1);
        background: var(--bg-primary);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        &.selected {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
        }
    }

    .table-cell {
        display: block;
        min-height: auto;
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .checkbox-cell {
        position: absolute;
        top: 16px;
        right: 16px;
        margin-bottom: 0;
    }

    .application-info {
        margin-top: 8px;
    }

    .application-title {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .user-info {
        margin-bottom: 12px;
        padding: 12px;
        background: rgba(0, 0, 0, 0.02);
        border-radius: var(--radius-md);
    }

    .user-name {
        font-size: 0.875rem;
        margin-bottom: 4px;
    }

    .user-email {
        font-size: 0.75rem;
    }

    .type-badge {
        display: inline-block;
        margin-bottom: 8px;
    }

    .status-select {
        width: 100%;
        padding: 8px 12px;
        font-size: 0.875rem;
        margin-bottom: 8px;
    }

    .date-text {
        font-size: 0.75rem;
        color: var(--text-secondary);
        margin-bottom: 12px;
    }

    .actions-cell {
        display: flex;
        gap: 8px;
        justify-content: stretch;
    }

    .actions-dropdown {
        flex: 1;
    }

    .dropdown-toggle {
        width: 100%;
        height: 40px;
        justify-content: center;
        background: rgba(0, 0, 0, 0.05);
        border-radius: var(--radius-md);
    }

    .dropdown-menu {
        position: fixed;
        top: auto;
        bottom: 20px;
        left: 20px;
        right: 20px;
        width: auto;
        border-radius: var(--radius-lg);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .dropdown-item {
        padding: 16px 20px;
        font-size: 1rem;
        justify-content: center;
    }

    .empty-state {
        padding: 40px 20px;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 4px;
        padding: 16px;
    }

    .pagination-link {
        min-width: 40px;
        text-align: center;
    }
}
</style>
