<?php

namespace App\Notifications;

use App\Models\Application;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewApplicationReceived extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Application $application
    ) {
        $this->onQueue('notifications');
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject("Новая заявка: {$this->application->title}")
            ->greeting("Здравствуйте, {$notifiable->name}!")
            ->line("Получена новая заявка от {$this->application->user->name}.")
            ->line("Тип заявки: {$this->getTypeText($this->application->type)}")
            ->line("Заголовок: {$this->application->title}")
            ->action('Рассмотреть заявку', route('applications.show', $this->application))
            ->line('Madd Label - система управления лейблом');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'new_application_received',
            'application_id' => $this->application->id,
            'application_title' => $this->application->title,
            'application_type' => $this->application->type,
            'user_name' => $this->application->user->name,
            'user_id' => $this->application->user_id,
            'message' => $this->getMessage(),
            'action_url' => route('applications.show', $this->application),
        ];
    }

    /**
     * Get the broadcast representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'new_application_received',
            'data' => $this->toArray($notifiable),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the notification message.
     */
    private function getMessage(): string
    {
        $typeText = $this->getTypeText($this->application->type);
        return "Новая заявка \"{$this->application->title}\" ({$typeText}) от {$this->application->user->name}";
    }

    /**
     * Get human-readable type text.
     */
    private function getTypeText(string $type): string
    {
        return match($type) {
            'promo' => 'Промо',
            'collaboration' => 'Коллаборация',
            'release' => 'Релиз',
            'custom' => 'Другое',
            default => ucfirst($type),
        };
    }
}
