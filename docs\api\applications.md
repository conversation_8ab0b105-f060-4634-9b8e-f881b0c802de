# API для работы с заявками

## Обзор

API для управления заявками артистов в системе Madd Label. Поддерживает создание, просмотр, редактирование и рассмотрение заявок с полной валидацией данных.

## Аутентификация

Все endpoints требуют аутентификации через Laravel Sanctum. Добавьте токен в заголовок:
```
Authorization: Bearer {your-token}
```

## Endpoints

### 1. Получить список заявок

**GET** `/api/applications`

**Параметры запроса:**
- `status` (string, optional) - Фильтр по статусу: `pending`, `approved`, `rejected`, `all` (по умолчанию: `all`)
- `type` (string, optional) - Фильтр по типу: `promo`, `collaboration`, `release`, `custom`, `all` (по умолчанию: `all`)
- `user_id` (integer, optional) - Фильтр по пользователю (только для админов/менеджеров)
- `per_page` (integer, optional) - Количество элементов на странице (1-100, по умолчанию: 15)
- `page` (integer, optional) - Номер страницы (по умолчанию: 1)
- `sort_by` (string, optional) - Поле сортировки: `created_at`, `updated_at`, `title`, `status`, `type` (по умолчанию: `created_at`)
- `sort_direction` (string, optional) - Направление сортировки: `asc`, `desc` (по умолчанию: `desc`)
- `search` (string, optional) - Поиск по заголовку и описанию
- `date_from` (date, optional) - Фильтр по дате создания (от)
- `date_to` (date, optional) - Фильтр по дате создания (до)

**Ответ:**
```json
{
  "data": [
    {
      "id": 1,
      "type": "promo",
      "title": "Промо заявка",
      "description": "Описание заявки",
      "status": "pending",
      "attachments": [...],
      "created_at": "2025-07-01T10:00:00.000000Z",
      "updated_at": "2025-07-01T10:00:00.000000Z",
      "user": {
        "id": 1,
        "name": "Артист"
      },
      "reviewer": null
    }
  ],
  "links": {...},
  "meta": {...}
}
```

### 2. Создать заявку

**POST** `/api/applications`

**Тело запроса:**
```json
{
  "type": "promo",
  "title": "Заголовок заявки",
  "description": "Подробное описание заявки",
  "attachments": [файлы] // optional
}
```

**Валидация:**
- `type` - обязательно, один из: `promo`, `collaboration`, `release`, `custom`
- `title` - обязательно, строка 3-255 символов
- `description` - обязательно, строка 10-5000 символов
- `attachments` - опционально, массив до 10 файлов
- `attachments.*` - файл до 10MB, форматы: PDF, DOC, DOCX, TXT, JPG, JPEG, PNG, GIF, MP3, WAV, FLAC, ZIP, RAR

**Ответ:**
```json
{
  "message": "Заявка успешно создана.",
  "application": {
    "id": 1,
    "type": "promo",
    "title": "Заголовок заявки",
    "description": "Подробное описание заявки",
    "status": "pending",
    "user_id": 1,
    "attachments": [...],
    "created_at": "2025-07-01T10:00:00.000000Z",
    "updated_at": "2025-07-01T10:00:00.000000Z"
  }
}
```

### 3. Просмотр заявки

**GET** `/api/applications/{id}`

**Ответ:**
```json
{
  "id": 1,
  "type": "promo",
  "title": "Заголовок заявки",
  "description": "Подробное описание заявки",
  "status": "pending",
  "admin_notes": null,
  "attachments": [...],
  "reviewed_at": null,
  "created_at": "2025-07-01T10:00:00.000000Z",
  "updated_at": "2025-07-01T10:00:00.000000Z",
  "user": {
    "id": 1,
    "name": "Артист"
  },
  "reviewer": null
}
```

### 4. Обновить заявку

**PUT** `/api/applications/{id}`

**Ограничения:**
- Только владелец заявки может её редактировать
- Можно редактировать только заявки со статусом `pending`

**Тело запроса:**
```json
{
  "type": "collaboration",
  "title": "Обновленный заголовок",
  "description": "Обновленное описание",
  "attachments": [новые файлы] // optional
}
```

**Ответ:**
```json
{
  "message": "Заявка успешно обновлена.",
  "application": {
    // обновленные данные заявки
  }
}
```

### 5. Рассмотреть заявку (только админы/менеджеры)

**POST** `/api/applications/{id}/review`

**Тело запроса:**
```json
{
  "status": "approved",
  "admin_notes": "Комментарий администратора"
}
```

**Валидация:**
- `status` - обязательно, один из: `approved`, `rejected`
- `admin_notes` - опционально, строка до 2000 символов

**Ответ:**
```json
{
  "message": "Заявка успешно одобрена.",
  "application": {
    // данные заявки с обновленным статусом
  }
}
```

### 6. Удалить заявку

**DELETE** `/api/applications/{id}`

**Ограничения:**
- Владелец заявки может удалить свою заявку
- Админы могут удалить любую заявку

**Ответ:**
```json
{
  "message": "Заявка успешно удалена."
}
```

### 7. Скачать вложение

**GET** `/api/applications/{id}/attachments/{attachmentIndex}`

**Ответ:** Файл для скачивания

## Коды ошибок

- `401` - Не авторизован
- `403` - Нет доступа к ресурсу
- `404` - Ресурс не найден
- `422` - Ошибки валидации

## Примеры использования

### Создание заявки с файлом
```javascript
const formData = new FormData();
formData.append('type', 'promo');
formData.append('title', 'Промо заявка');
formData.append('description', 'Описание промо заявки');
formData.append('attachments[]', file);

fetch('/api/applications', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
});
```

### Фильтрация заявок
```javascript
fetch('/api/applications?status=pending&type=promo&per_page=20', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```
