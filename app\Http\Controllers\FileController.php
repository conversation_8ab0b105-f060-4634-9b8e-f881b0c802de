<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Symfony\Component\HttpFoundation\StreamedResponse;

class FileController extends Controller
{
    /**
     * Serve image files.
     */
    public function serveImage(Request $request, string $path)
    {
        // Проверяем различные возможные пути
        $possiblePaths = [
            "public/images/{$path}",
            "private/public/images/{$path}",
            "images/{$path}",
            $path
        ];

        foreach ($possiblePaths as $filePath) {
            if (Storage::exists($filePath)) {
                return $this->serveFile($filePath);
            }
        }

        // Если файл не найден, возвращаем 404
        abort(404, 'Image not found');
    }

    /**
     * Serve cover images.
     */
    public function serveCover(Request $request, string $filename)
    {
        $possiblePaths = [
            "public/images/covers/{$filename}",
            "private/public/images/covers/{$filename}",
            "images/covers/{$filename}",
            "covers/{$filename}"
        ];

        foreach ($possiblePaths as $filePath) {
            if (Storage::exists($filePath)) {
                return $this->serveFile($filePath);
            }
        }

        abort(404, 'Cover image not found');
    }

    /**
     * Serve track files.
     */
    public function serveTrack(Request $request, string $filename)
    {
        $user = auth()->user();
        
        // Проверяем права доступа (только для авторизованных пользователей)
        if (!$user) {
            abort(403, 'Unauthorized');
        }

        $possiblePaths = [
            "public/tracks/{$filename}",
            "private/public/tracks/{$filename}",
            "tracks/{$filename}",
            "private/tracks/{$filename}"
        ];

        foreach ($possiblePaths as $filePath) {
            if (Storage::exists($filePath)) {
                return $this->serveFile($filePath, true);
            }
        }

        abort(404, 'Track not found');
    }

    /**
     * Serve attachment files.
     */
    public function serveAttachment(Request $request, string $filename)
    {
        $user = auth()->user();
        
        // Проверяем права доступа
        if (!$user) {
            abort(403, 'Unauthorized');
        }

        $possiblePaths = [
            "public/applications/{$filename}",
            "private/public/applications/{$filename}",
            "applications/{$filename}",
            "private/applications/{$filename}"
        ];

        foreach ($possiblePaths as $filePath) {
            if (Storage::exists($filePath)) {
                return $this->serveFile($filePath, true);
            }
        }

        abort(404, 'Attachment not found');
    }

    /**
     * Serve temporary uploaded files.
     */
    public function serveTempFile(Request $request, string $filename)
    {
        $user = auth()->user();

        if (!$user) {
            abort(403, 'Unauthorized');
        }

        // Проверяем различные возможные пути для временных файлов
        $possiblePaths = [
            "temp/uploads/{$filename}",
            "private/temp/uploads/{$filename}",
            "uploads/{$filename}",
            // Добавляем поддержку для файлов в подпапках
            "temp/{$filename}",
            "private/temp/{$filename}"
        ];

        foreach ($possiblePaths as $filePath) {
            if (Storage::disk('local')->exists($filePath)) {
                return $this->serveFile($filePath, true, 'local');
            }
        }

        abort(404, 'Temporary file not found');
    }

    /**
     * Serve file with proper headers.
     */
    private function serveFile(string $path, bool $requireAuth = false, string $disk = 'public'): Response|StreamedResponse
    {
        if ($requireAuth && !auth()->check()) {
            abort(403, 'Unauthorized');
        }

        $storage = Storage::disk($disk);
        $file = $storage->get($path);
        $mimeType = $storage->mimeType($path) ?: 'application/octet-stream';
        $size = $storage->size($path);

        // Для изображений возвращаем обычный Response
        if (str_starts_with($mimeType, 'image/')) {
            return response($file, 200, [
                'Content-Type' => $mimeType,
                'Content-Length' => $size,
                'Cache-Control' => 'public, max-age=31536000',
                'Expires' => gmdate('D, d M Y H:i:s \G\M\T', time() + 31536000),
            ]);
        }

        // Для аудио и других файлов используем StreamedResponse
        return response()->stream(function () use ($file) {
            echo $file;
        }, 200, [
            'Content-Type' => $mimeType,
            'Content-Length' => $size,
            'Accept-Ranges' => 'bytes',
            'Cache-Control' => 'no-cache, must-revalidate',
        ]);
    }

    /**
     * Download attachment file.
     */
    public function downloadAttachment(Request $request, string $filename)
    {
        $user = auth()->user();
        
        if (!$user) {
            abort(403, 'Unauthorized');
        }

        $possiblePaths = [
            "public/applications/{$filename}",
            "private/public/applications/{$filename}",
            "applications/{$filename}",
            "private/applications/{$filename}"
        ];

        foreach ($possiblePaths as $filePath) {
            if (Storage::exists($filePath)) {
                $file = Storage::get($filePath);
                $mimeType = Storage::mimeType($filePath);
                
                return response($file, 200, [
                    'Content-Type' => $mimeType,
                    'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                    'Content-Length' => Storage::size($filePath),
                ]);
            }
        }

        abort(404, 'Attachment not found');
    }

    /**
     * List files in directory (for debugging).
     */
    public function listFiles(Request $request, string $directory = '')
    {
        if (!auth()->user() || !auth()->user()->isAdmin()) {
            abort(403, 'Admin access required');
        }

        $files = Storage::allFiles($directory);
        $directories = Storage::allDirectories($directory);

        return response()->json([
            'current_directory' => $directory,
            'directories' => $directories,
            'files' => $files,
        ]);
    }
}
