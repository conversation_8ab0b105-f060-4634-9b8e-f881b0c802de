<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Analytic extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'analyzable_type',
        'analyzable_id',
        'metric_type',
        'count',
        'source',
        'meta_data',
        'ip_address',
        'user_agent',
        'user_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'meta_data' => 'json',
        'count' => 'integer',
    ];

    /**
     * Get the parent analyzable model.
     */
    public function analyzable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user associated with the analytic record.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include analytics of a specific metric type.
     */
    public function scopeMetricType($query, $type)
    {
        return $query->where('metric_type', $type);
    }

    /**
     * Scope a query to only include analytics from a specific source.
     */
    public function scopeSource($query, $source)
    {
        return $query->where('source', $source);
    }

    /**
     * Log a new analytics record.
     *
     * @param Model $model
     * @param string $metricType
     * @param string|null $source
     * @param array $metaData
     * @param User|null $user
     * @return Analytic
     */
    public static function log($model, string $metricType, ?string $source = null, array $metaData = [], ?User $user = null): Analytic
    {
        $data = [
            'metric_type' => $metricType,
            'source' => $source,
            'meta_data' => $metaData,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'user_id' => $user?->id,
        ];

        return $model->analytics()->create($data);
    }
}
