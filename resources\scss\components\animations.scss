// Современные микроанимации и переходы 2025

// Базовые анимации
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(var(--primary-color-rgb), 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(var(--primary-color-rgb), 0.8);
    }
}

// Классы анимаций
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-slide-in-up {
    animation: slideInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-slide-in-down {
    animation: slideInDown 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-bounce {
    animation: bounce 1s;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-shake {
    animation: shake 0.5s;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

// Задержки анимации
.animate-delay-100 {
    animation-delay: 0.1s;
}

.animate-delay-200 {
    animation-delay: 0.2s;
}

.animate-delay-300 {
    animation-delay: 0.3s;
}

.animate-delay-500 {
    animation-delay: 0.5s;
}

.animate-delay-700 {
    animation-delay: 0.7s;
}

.animate-delay-1000 {
    animation-delay: 1s;
}

// Hover эффекты
.hover-lift {
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    
    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
    }
}

.hover-scale {
    transition: transform var(--transition-bounce);
    
    &:hover {
        transform: scale(1.05);
    }
}

.hover-rotate {
    transition: transform var(--transition-bounce);
    
    &:hover {
        transform: rotate(5deg);
    }
}

.hover-glow {
    transition: box-shadow var(--transition-normal);
    
    &:hover {
        box-shadow: var(--shadow-glow);
    }
}

.hover-slide-right {
    transition: transform var(--transition-normal);
    
    &:hover {
        transform: translateX(4px);
    }
}

.hover-slide-up {
    transition: transform var(--transition-normal);
    
    &:hover {
        transform: translateY(-2px);
    }
}

// Эффекты фокуса
.focus-ring {
    &:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
        border-radius: var(--radius-sm);
    }
}

.focus-glow {
    &:focus {
        box-shadow: var(--shadow-glow);
        outline: none;
    }
}

// Эффекты загрузки
.loading-skeleton {
    background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

// Эффекты появления элементов
.stagger-children > * {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
    
    @for $i from 1 through 10 {
        &:nth-child(#{$i}) {
            animation-delay: #{$i * 0.1}s;
        }
    }
}

// Эффекты прокрутки
.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
    
    &.revealed {
        opacity: 1;
        transform: translateY(0);
    }
}

// Эффекты для модальных окон
.modal-backdrop {
    opacity: 0;
    transition: opacity var(--transition-normal);
    
    &.show {
        opacity: 1;
    }
}

.modal-content {
    transform: scale(0.8) translateY(-50px);
    opacity: 0;
    transition: all var(--transition-normal);
    
    &.show {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

// Эффекты для уведомлений
.toast {
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    
    &.show {
        transform: translateX(0);
    }
    
    &.hide {
        transform: translateX(100%);
    }
}

// Эффекты для выпадающих меню
.dropdown-menu {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
    transition: all var(--transition-fast);
    
    &.show {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
