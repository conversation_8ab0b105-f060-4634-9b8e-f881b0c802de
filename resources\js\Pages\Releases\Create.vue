<script setup>
import { ref, computed, watch } from "vue";
import { Head, useForm } from "@inertiajs/vue3";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import {
    Calendar,
    Music,
    Check,
    Upload,
    AlertCircle,
    X,
    Globe,
    Info,
    Save,
} from "lucide-vue-next";

const props = defineProps({
    artist: Object,
    genres: Object,
    languages: Object,
    errors: Object,
});

// Тип релиза
const releaseTypes = [
    { value: "album", label: "Альбом" },
    { value: "ep", label: "EP" },
    { value: "single", label: "Сингл" },
];

// Форма создания релиза
const form = useForm({
    title: "",
    description: "",
    type: "single", // По умолчанию сингл
    genre: "",
    language: "ru", // По умолчанию русский
    is_explicit: false,
    release_date: new Date().toISOString().substring(0, 10), // Текущая дата
    cover_image: null,
    status: "pending",
});

// Хлебные крошки
const breadcrumbs = [
    { name: "Релизы", link: route("releases.index") },
    { name: "Создать релиз" },
];

// Предпросмотр обложки
const coverPreview = ref(null);
const imageError = ref("");

// Тип отправки формы
const submissionType = ref(null);

// Обработчик загрузки обложки
function handleCoverUpload(event) {
    const file = event.target.files[0];
    imageError.value = "";

    // Проверка файла
    if (!file) {
        form.cover_image = null;
        coverPreview.value = null;
        return;
    }

    // Проверка типа файла
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];
    if (!allowedTypes.includes(file.type)) {
        imageError.value =
            "Неподдерживаемый формат изображения. Используйте JPG или PNG.";
        event.target.value = null;
        return;
    }

    // Проверка размера файла (макс. 2MB)
    if (file.size > 2 * 1024 * 1024) {
        imageError.value = "Размер изображения не должен превышать 2MB.";
        event.target.value = null;
        return;
    }

    // Установка файла и превью
    form.cover_image = file;

    const reader = new FileReader();
    reader.onload = (e) => {
        coverPreview.value = e.target.result;
    };
    reader.readAsDataURL(file);
}

// Удалить выбранную обложку
function removeCover() {
    form.cover_image = null;
    coverPreview.value = null;
    const fileInput = document.getElementById("cover_image");
    if (fileInput) fileInput.value = "";
}

// Отправка формы
function submitForm(status = "published") {
    submissionType.value = status;
    form.status = status;

    form.post(route("releases.store"), {
        preserveScroll: true,
        onSuccess: () => {
            form.reset();
            coverPreview.value = null;
            submissionType.value = null;
        },
        onFinish: () => {
            submissionType.value = null;
        },
    });
}

// Проверка наличия артиста
const artistMissing = computed(() => !props.artist);

// Базовая валидация (для черновиков)
const isBasicValid = computed(() => {
    return form.title && form.genre && form.language;
});

// Полная валидация (для публикации)
const isValid = computed(() => {
    return (
        form.title &&
        form.genre &&
        form.language &&
        form.cover_image &&
        !imageError.value
    );
});
</script>

<template>
    <DashboardLayout title="Создать новый релиз" :breadcrumbs="breadcrumbs">
        <Head title="Создать релиз" />

        <div class="content-container">
            <!-- Сообщение об ошибке, если у пользователя нет профиля артиста -->
            <div v-if="artistMissing" class="error-alert">
                <AlertCircle class="error-icon" size="24" />
                <div>
                    <h3 class="error-title">Профиль артиста отсутствует</h3>
                    <p class="error-message">
                        Перед созданием релиза необходимо создать профиль
                        артиста.
                        <a href="/profile" class="alert-link"
                            >Перейти в профиль</a
                        >
                    </p>
                </div>
            </div>

            <form v-else @submit.prevent="submitForm" class="release-form">
                <div class="form-grid">
                    <!-- Левая колонка: детали релиза -->
                    <div class="form-column">
                        <h3 class="section-title">Информация о релизе</h3>

                        <!-- Название релиза -->
                        <div class="form-group">
                            <label for="title" class="form-label">
                                Название релиза *
                            </label>
                            <input
                                id="title"
                                v-model="form.title"
                                type="text"
                                class="form-input"
                                :class="{ error: form.errors.title }"
                                placeholder="Введите название релиза"
                                required
                            />
                            <p v-if="form.errors.title" class="error-text">
                                {{ form.errors.title }}
                            </p>
                        </div>

                        <!-- Тип релиза -->
                        <div class="form-group">
                            <label class="form-label">Тип релиза *</label>
                            <div class="release-type-selector">
                                <button
                                    v-for="type in releaseTypes"
                                    :key="type.value"
                                    type="button"
                                    class="release-type-button"
                                    :class="{
                                        active: form.type === type.value,
                                    }"
                                    @click="form.type = type.value"
                                >
                                    <Check
                                        v-if="form.type === type.value"
                                        class="check-icon"
                                        size="16"
                                    />
                                    {{ type.label }}
                                </button>
                            </div>
                            <p v-if="form.errors.type" class="error-text">
                                {{ form.errors.type }}
                            </p>
                        </div>

                        <!-- Описание -->
                        <div class="form-group">
                            <label for="description" class="form-label">
                                Описание
                                <span class="optional-text"
                                    >(необязательно)</span
                                >
                            </label>
                            <textarea
                                id="description"
                                v-model="form.description"
                                class="form-textarea"
                                :class="{ error: form.errors.description }"
                                placeholder="Расскажите о релизе"
                                rows="4"
                            ></textarea>
                            <p
                                v-if="form.errors.description"
                                class="error-text"
                            >
                                {{ form.errors.description }}
                            </p>
                        </div>

                        <!-- Жанр -->
                        <div class="form-group">
                            <label for="genre" class="form-label">Жанр *</label>
                            <select
                                id="genre"
                                v-model="form.genre"
                                class="form-select"
                                :class="{ error: form.errors.genre }"
                                required
                            >
                                <option value="" disabled selected>
                                    Выберите жанр
                                </option>
                                <option
                                    v-for="(label, value) in genres"
                                    :key="value"
                                    :value="value"
                                >
                                    {{ label }}
                                </option>
                            </select>
                            <p v-if="form.errors.genre" class="error-text">
                                {{ form.errors.genre }}
                            </p>
                        </div>

                        <!-- Язык -->
                        <div class="form-group">
                            <label for="language" class="form-label"
                                >Язык *</label
                            >
                            <div class="input-with-icon">
                                <Globe class="input-icon" size="18" />
                                <select
                                    id="language"
                                    v-model="form.language"
                                    class="form-select"
                                    :class="{ error: form.errors.language }"
                                    required
                                >
                                    <option
                                        v-for="(label, value) in languages"
                                        :key="value"
                                        :value="value"
                                    >
                                        {{ label }}
                                    </option>
                                </select>
                            </div>
                            <p v-if="form.errors.language" class="error-text">
                                {{ form.errors.language }}
                            </p>
                        </div>

                        <!-- Дата релиза -->
                        <div class="form-group">
                            <label for="release_date" class="form-label">
                                Дата релиза *
                            </label>
                            <div class="input-with-icon">
                                <Calendar class="input-icon" size="18" />
                                <input
                                    id="release_date"
                                    v-model="form.release_date"
                                    type="date"
                                    class="form-input"
                                    :class="{ error: form.errors.release_date }"
                                    required
                                />
                            </div>
                            <p
                                v-if="form.errors.release_date"
                                class="error-text"
                            >
                                {{ form.errors.release_date }}
                            </p>
                        </div>

                        <!-- Возрастные ограничения -->
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input
                                    id="is_explicit"
                                    v-model="form.is_explicit"
                                    type="checkbox"
                                    class="form-checkbox"
                                />
                                <label for="is_explicit" class="checkbox-label">
                                    Контент содержит ненормативную лексику (18+)
                                </label>
                            </div>
                            <p
                                v-if="form.errors.is_explicit"
                                class="error-text"
                            >
                                {{ form.errors.is_explicit }}
                            </p>
                        </div>
                    </div>

                    <!-- Правая колонка: обложка -->
                    <div class="form-column">
                        <h3 class="section-title">Обложка релиза</h3>

                        <!-- Загрузка обложки -->
                        <div
                            class="cover-upload-area"
                            :class="{
                                'has-preview': coverPreview,
                                error: form.errors.cover_image || imageError,
                            }"
                        >
                            <div
                                v-if="!coverPreview"
                                class="upload-placeholder"
                            >
                                <Upload class="upload-icon" size="48" />
                                <p class="upload-text">
                                    Перетащите изображение сюда или нажмите,
                                    чтобы выбрать
                                </p>
                                <p class="upload-hint">
                                    Рекомендуемый размер: 1400x1400 px, формат
                                    JPG или PNG
                                </p>
                                <input
                                    id="cover_image"
                                    type="file"
                                    class="file-input"
                                    accept="image/png, image/jpeg"
                                    @change="handleCoverUpload"
                                />
                            </div>

                            <div v-else class="cover-preview">
                                <img
                                    :src="coverPreview"
                                    alt="Предпросмотр обложки"
                                />
                                <button
                                    type="button"
                                    @click="removeCover"
                                    class="remove-cover-btn"
                                >
                                    <X size="20" />
                                </button>
                            </div>
                        </div>

                        <!-- Ошибки обложки -->
                        <p
                            v-if="form.errors.cover_image"
                            class="error-text mt-2"
                        >
                            {{ form.errors.cover_image }}
                        </p>
                        <p v-else-if="imageError" class="error-text mt-2">
                            {{ imageError }}
                        </p>

                        <!-- Инфо о требованиях -->
                        <div class="info-box">
                            <Info class="info-icon" size="20" />
                            <div>
                                <p class="info-title">Требования к обложке:</p>
                                <ul class="info-list">
                                    <li>Квадратное изображение (1:1)</li>
                                    <li>
                                        Минимальный размер: 1400x1400 пикселей
                                    </li>
                                    <li>Максимальный размер файла: 2MB</li>
                                    <li>Формат: JPG или PNG</li>
                                    <li>Не должен содержать текст</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Кнопки формы -->
                <div class="form-actions">
                    <button
                        type="button"
                        class="btn btn-outline"
                        @click="$inertia.visit(route('releases.index'))"
                    >
                        Отмена
                    </button>
                    <button
                        type="button"
                        class="btn btn-secondary"
                        :disabled="!isBasicValid || form.processing"
                        @click="submitForm('draft')"
                    >
                        <Save
                            v-if="!form.processing"
                            class="btn-icon"
                            size="18"
                        />
                        <span
                            v-if="form.processing && submissionType === 'draft'"
                            >Сохранение...</span
                        >
                        <span v-else>Сохранить в черновики</span>
                    </button>
                    <button
                        type="button"
                        class="btn btn-primary"
                        :disabled="!isValid || form.processing"
                        @click="submitForm('pending')"
                    >
                        <Music
                            v-if="!form.processing"
                            class="btn-icon"
                            size="18"
                        />
                        <span
                            v-if="
                                form.processing && submissionType === 'pending'
                            "
                            >Отправка...</span
                        >
                        <span v-else>Отправить на проверку</span>
                    </button>
                </div>
            </form>
        </div>
    </DashboardLayout>
</template>

<style lang="scss">
.content-container {
    max-width: 1200px;
    margin: 0 auto;
}

.error-alert {
    background-color: rgba(244, 67, 54, 0.1);
    border-left: 4px solid var(--danger-color);
    padding: 16px 20px;
    border-radius: var(--radius-md);
    margin-bottom: 24px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

.error-icon {
    color: var(--danger-color);
    flex-shrink: 0;
}

.error-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 8px;
    color: var(--text-primary);
}

.error-message {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.alert-link {
    color: var(--primary-color);
    text-decoration: underline;

    &:hover {
        text-decoration: none;
    }
}

.release-form {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 24px;
    box-shadow: var(--shadow-sm);
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    margin-bottom: 32px;

    @media (max-width: 992px) {
        grid-template-columns: 1fr;
    }
}

.form-column {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 8px;
    color: var(--text-primary);
}

.form-group {
    margin-bottom: 8px;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 6px;
    color: var(--text-secondary);
}

.optional-text {
    font-size: 0.75rem;
    font-weight: 400;
    color: var(--text-disabled);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: border-color 0.2s;

    &:focus {
        border-color: var(--primary-color);
        outline: none;
    }

    &.error {
        border-color: var(--danger-color);
    }
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.input-with-icon {
    position: relative;

    .input-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
        pointer-events: none;
    }

    .form-input,
    .form-select {
        padding-left: 38px;
    }
}

.release-type-selector {
    display: flex;
    gap: 12px;
    margin-bottom: 8px;

    @media (max-width: 576px) {
        flex-direction: column;
        gap: 8px;
    }
}

.release-type-button {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s;

    &:hover {
        background-color: rgba(var(--primary-color-rgb, 63, 81, 181), 0.05);
    }

    &.active {
        background-color: rgba(var(--primary-color-rgb, 63, 81, 181), 0.1);
        border-color: var(--primary-color);
        color: var(--primary-color);
    }

    .check-icon {
        color: var(--primary-color);
    }
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-checkbox {
    width: 18px;
    height: 18px;
    margin: 0;
}

.checkbox-label {
    font-size: 0.875rem;
    color: var(--text-primary);
}

.cover-upload-area {
    width: 100%;
    aspect-ratio: 1;
    border: 2px dashed rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    overflow: hidden;
    position: relative;
    margin-bottom: 16px;

    &:hover {
        border-color: var(--primary-color);
    }

    &.has-preview {
        border-style: solid;
    }

    &.error {
        border-color: var(--danger-color);
    }
}

.upload-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    cursor: pointer;

    .upload-icon {
        color: var(--text-secondary);
        margin-bottom: 16px;
    }

    .upload-text {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
        margin: 0 0 8px;
        text-align: center;
    }

    .upload-hint {
        font-size: 0.75rem;
        color: var(--text-secondary);
        margin: 0;
        text-align: center;
    }
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.cover-preview {
    width: 100%;
    height: 100%;
    position: relative;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .remove-cover-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &:hover {
            background-color: rgba(0, 0, 0, 0.7);
        }
    }
}

.error-text {
    color: var(--danger-color);
    font-size: 0.75rem;
    margin: 4px 0 0;
}

.mt-2 {
    margin-top: 8px;
}

.info-box {
    background-color: rgba(33, 150, 243, 0.05);
    border-radius: var(--radius-md);
    padding: 16px;
    display: flex;
    gap: 12px;
    margin-top: 16px;

    .info-icon {
        color: var(--info-color);
        flex-shrink: 0;
    }

    .info-title {
        font-size: 0.875rem;
        font-weight: 500;
        margin: 0 0 8px;
        color: var(--text-primary);
    }

    .info-list {
        margin: 0;
        padding-left: 16px;
        font-size: 0.8125rem;
        color: var(--text-secondary);

        li {
            margin-bottom: 4px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.btn {
    padding: 10px 20px;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.btn-outline {
    background-color: transparent;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: var(--text-primary);

    &:hover:not(:disabled) {
        background-color: var(--bg-secondary);
    }
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;

    &:hover:not(:disabled) {
        background-color: var(--secondary-color);
    }
}

.btn-icon {
    flex-shrink: 0;
}
</style>
