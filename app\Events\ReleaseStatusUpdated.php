<?php

namespace App\Events;

use App\Models\Release;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReleaseStatusUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $release;
    public $oldStatus;

    /**
     * Create a new event instance.
     */
    public function __construct(Release $release, string $oldStatus)
    {
        $this->release = $release;
        $this->oldStatus = $oldStatus;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('App.Models.User.' . $this->release->artist->user_id),
            new PrivateChannel('admin.releases'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'release' => [
                'id' => $this->release->id,
                'title' => $this->release->title,
                'status' => $this->release->status,
                'old_status' => $this->oldStatus,
                'admin_notes' => $this->release->admin_notes,
                'reviewed_at' => $this->release->reviewed_at,
            ],
            'message' => $this->getStatusMessage(),
        ];
    }

    /**
     * Get status change message.
     */
    private function getStatusMessage(): string
    {
        switch ($this->release->status) {
            case 'published':
                return "Ваш релиз \"{$this->release->title}\" был опубликован!";
            case 'rejected':
                return "Ваш релиз \"{$this->release->title}\" был отклонен.";
            case 'pending':
                return "Ваш релиз \"{$this->release->title}\" отправлен на рассмотрение.";
            default:
                return "Статус вашего релиза \"{$this->release->title}\" изменен на: {$this->release->status}";
        }
    }
}
