<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;

class MonitorQueues extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:monitor {--refresh=5 : Refresh interval in seconds}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor Redis queue status and statistics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $refreshInterval = (int) $this->option('refresh');
        
        $this->info('Мониторинг очередей Redis (обновление каждые ' . $refreshInterval . ' сек)');
        $this->info('Нажмите Ctrl+C для выхода');
        $this->newLine();

        while (true) {
            $this->clearScreen();
            $this->displayHeader();
            $this->displayQueueStats();
            $this->displayFailedJobs();
            $this->displayWorkerStats();
            
            sleep($refreshInterval);
        }
    }

    /**
     * Clear screen (cross-platform).
     */
    private function clearScreen(): void
    {
        if (PHP_OS_FAMILY === 'Windows') {
            system('cls');
        } else {
            system('clear');
        }
    }

    /**
     * Display header.
     */
    private function displayHeader(): void
    {
        $this->info('=== MADD LABEL - МОНИТОРИНГ ОЧЕРЕДЕЙ ===');
        $this->info('Время: ' . now()->format('Y-m-d H:i:s'));
        $this->newLine();
    }

    /**
     * Display queue statistics.
     */
    private function displayQueueStats(): void
    {
        $this->info('📊 СТАТИСТИКА ОЧЕРЕДЕЙ:');
        $this->newLine();

        $queues = ['default', 'files', 'notifications'];
        
        $headers = ['Очередь', 'Ожидает', 'Обрабатывается', 'Отложено', 'Всего'];
        $rows = [];

        foreach ($queues as $queue) {
            $stats = $this->getQueueStats($queue);
            $rows[] = [
                $queue,
                $stats['waiting'],
                $stats['processing'],
                $stats['delayed'],
                $stats['total']
            ];
        }

        $this->table($headers, $rows);
        $this->newLine();
    }

    /**
     * Get statistics for a specific queue.
     */
    private function getQueueStats(string $queue): array
    {
        try {
            $redis = Redis::connection();
            $prefix = config('database.redis.options.prefix');
            
            $waiting = $redis->llen("{$prefix}queues:{$queue}") ?: 0;
            $delayed = $redis->zcard("{$prefix}queues:{$queue}:delayed") ?: 0;
            $processing = $redis->zcard("{$prefix}queues:{$queue}:reserved") ?: 0;
            
            return [
                'waiting' => $waiting,
                'processing' => $processing,
                'delayed' => $delayed,
                'total' => $waiting + $processing + $delayed
            ];
        } catch (\Exception $e) {
            return [
                'waiting' => 'N/A',
                'processing' => 'N/A',
                'delayed' => 'N/A',
                'total' => 'N/A'
            ];
        }
    }

    /**
     * Display failed jobs.
     */
    private function displayFailedJobs(): void
    {
        try {
            $failedJobs = DB::table('failed_jobs')
                ->orderBy('failed_at', 'desc')
                ->limit(5)
                ->get();

            if ($failedJobs->count() > 0) {
                $this->error('❌ НЕУДАЧНЫЕ ЗАДАЧИ (последние 5):');
                $this->newLine();

                $headers = ['ID', 'Очередь', 'Класс', 'Ошибка', 'Время'];
                $rows = [];

                foreach ($failedJobs as $job) {
                    $payload = json_decode($job->payload, true);
                    $jobClass = $payload['displayName'] ?? 'Unknown';
                    $queue = $payload['job'] ?? 'default';
                    
                    $rows[] = [
                        substr($job->uuid, 0, 8) . '...',
                        $queue,
                        class_basename($jobClass),
                        substr($job->exception, 0, 50) . '...',
                        \Carbon\Carbon::parse($job->failed_at)->diffForHumans()
                    ];
                }

                $this->table($headers, $rows);
            } else {
                $this->info('✅ Неудачных задач нет');
            }
        } catch (\Exception $e) {
            $this->error('Ошибка получения неудачных задач: ' . $e->getMessage());
        }

        $this->newLine();
    }

    /**
     * Display worker statistics.
     */
    private function displayWorkerStats(): void
    {
        $this->info('👷 СТАТИСТИКА ВОРКЕРОВ:');
        $this->newLine();

        try {
            $processCount = $this->getWorkerProcessCount();
            $this->info("Активных процессов: {$processCount}");
            
            $memoryUsage = $this->getMemoryUsage();
            $this->info("Использование памяти: {$memoryUsage}");
            
        } catch (\Exception $e) {
            $this->error('Ошибка получения статистики воркеров: ' . $e->getMessage());
        }

        $this->newLine();
        $this->info('💡 КОМАНДЫ:');
        $this->line('  php artisan queue:start-workers     - Запустить воркеры');
        $this->line('  php artisan queue:start-workers --stop - Остановить воркеры');
        $this->line('  php artisan queue:retry all          - Повторить неудачные задачи');
        $this->line('  php artisan queue:flush              - Очистить все очереди');
    }

    /**
     * Get worker process count.
     */
    private function getWorkerProcessCount(): int
    {
        if (PHP_OS_FAMILY === 'Windows') {
            $output = shell_exec('tasklist /FI "IMAGENAME eq php.exe" /FO CSV');
            if ($output) {
                return substr_count($output, 'queue:work');
            }
        } else {
            $output = shell_exec('ps aux | grep "queue:work" | grep -v grep | wc -l');
            if ($output) {
                return (int) trim($output);
            }
        }

        return 0;
    }

    /**
     * Get memory usage.
     */
    private function getMemoryUsage(): string
    {
        $bytes = memory_get_usage(true);
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
