<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Application>
 */
class ApplicationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'type' => $this->faker->randomElement(['promo', 'collaboration', 'release', 'custom']),
            'title' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph(3),
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected']),
            'admin_notes' => $this->faker->optional()->paragraph(),
            'attachments' => null,
            'reviewed_at' => $this->faker->optional()->dateTime(),
            'reviewed_by' => $this->faker->optional()->randomElement(User::where('role', 'admin')->pluck('id')->toArray()),
        ];
    }

    /**
     * Indicate that the application is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'admin_notes' => null,
            'reviewed_at' => null,
            'reviewed_by' => null,
        ]);
    }

    /**
     * Indicate that the application is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'admin_notes' => $this->faker->paragraph(),
            'reviewed_at' => $this->faker->dateTime(),
            'reviewed_by' => User::factory()->create(['role' => 'admin'])->id,
        ]);
    }

    /**
     * Indicate that the application is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
            'admin_notes' => $this->faker->paragraph(),
            'reviewed_at' => $this->faker->dateTime(),
            'reviewed_by' => User::factory()->create(['role' => 'admin'])->id,
        ]);
    }
}
