import { defineStore } from "pinia";
import { ref, watch } from "vue";

export const useThemeStore = defineStore("theme", () => {
    // Состояние
    const theme = ref("light");

    // Проверить наличие сохраненной темы в localStorage
    const initializeTheme = () => {
        if (typeof window !== "undefined") {
            // Проверяем сохраненную тему
            const savedTheme = localStorage.getItem("theme");

            if (savedTheme) {
                theme.value = savedTheme;
            } else {
                // Если нет сохраненной темы, проверяем системные настройки
                const prefersDark = window.matchMedia(
                    "(prefers-color-scheme: dark)"
                ).matches;
                theme.value = prefersDark ? "dark" : "light";
            }

            // Применяем тему к документу
            applyTheme();
        }
    };

    // Применить текущую тему к HTML-документу
    const applyTheme = () => {
        if (typeof document !== "undefined") {
            document.documentElement.setAttribute("data-theme", theme.value);
        }
    };

    // Переключение темы
    const toggleTheme = () => {
        theme.value = theme.value === "light" ? "dark" : "light";

        // Сохраняем в localStorage
        if (typeof window !== "undefined") {
            localStorage.setItem("theme", theme.value);
        }
    };

    // Установка конкретной темы
    const setTheme = (newTheme) => {
        if (newTheme === "light" || newTheme === "dark") {
            theme.value = newTheme;

            // Сохраняем в localStorage
            if (typeof window !== "undefined") {
                localStorage.setItem("theme", theme.value);
            }
        }
    };

    // Наблюдение за изменением темы для применения
    watch(theme, () => {
        applyTheme();
    });

    // При создании хранилища инициализируем тему
    initializeTheme();

    // Наблюдение за изменением системных настроек
    if (typeof window !== "undefined") {
        window
            .matchMedia("(prefers-color-scheme: dark)")
            .addEventListener("change", (e) => {
                // Только если пользователь не выбрал тему явно
                if (!localStorage.getItem("theme")) {
                    theme.value = e.matches ? "dark" : "light";
                }
            });
    }

    return {
        theme,
        toggleTheme,
        setTheme,
    };
});
