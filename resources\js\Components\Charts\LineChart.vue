<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from "vue";
import {
    Chart,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    Filler,
} from "chart.js";

// Регистрируем компоненты Chart.js
Chart.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    Filler
);

const props = defineProps({
    data: {
        type: Object,
        required: true,
    },
    options: {
        type: Object,
        default: () => ({}),
    },
    height: {
        type: Number,
        default: 300,
    },
    responsive: {
        type: Boolean,
        default: true,
    },
});

const chartRef = ref(null);
const chartInstance = ref(null);

// Дефолтные опции для графика
const defaultOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: "top",
            labels: {
                usePointStyle: true,
                padding: 20,
                font: {
                    size: 12,
                },
            },
        },
        tooltip: {
            mode: "index",
            intersect: false,
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            titleColor: "#fff",
            bodyColor: "#fff",
            borderColor: "rgba(255, 255, 255, 0.1)",
            borderWidth: 1,
            cornerRadius: 8,
            padding: 12,
        },
    },
    scales: {
        x: {
            display: true,
            grid: {
                display: false,
            },
            ticks: {
                font: {
                    size: 11,
                },
                color: "#6b7280",
            },
        },
        y: {
            display: true,
            beginAtZero: true,
            grid: {
                color: "rgba(0, 0, 0, 0.05)",
                drawBorder: false,
            },
            ticks: {
                font: {
                    size: 11,
                },
                color: "#6b7280",
                precision: 0,
            },
        },
    },
    elements: {
        line: {
            tension: 0.4,
            borderWidth: 2,
        },
        point: {
            radius: 4,
            hoverRadius: 6,
            borderWidth: 2,
            backgroundColor: "#fff",
        },
    },
    interaction: {
        mode: "nearest",
        axis: "x",
        intersect: false,
    },
};

// Объединяем дефолтные опции с переданными
const mergedOptions = computed(() => {
    return {
        ...defaultOptions,
        ...props.options,
        plugins: {
            ...defaultOptions.plugins,
            ...props.options.plugins,
        },
        scales: {
            ...defaultOptions.scales,
            ...props.options.scales,
        },
    };
});

// Создание графика
const createChart = () => {
    if (!chartRef.value) return;

    // Уничтожаем предыдущий график если он существует
    if (chartInstance.value) {
        chartInstance.value.destroy();
    }

    chartInstance.value = new Chart(chartRef.value, {
        type: "line",
        data: props.data,
        options: mergedOptions.value,
    });
};

// Обновление данных графика
const updateChart = () => {
    if (!chartInstance.value) return;

    chartInstance.value.data = props.data;
    chartInstance.value.update("none");
};

// Уничтожение графика
const destroyChart = () => {
    if (chartInstance.value) {
        chartInstance.value.destroy();
        chartInstance.value = null;
    }
};

// Lifecycle hooks
onMounted(async () => {
    await nextTick();
    createChart();
});

onUnmounted(() => {
    destroyChart();
});

// Watchers
watch(
    () => props.data,
    () => {
        if (chartInstance.value) {
            updateChart();
        } else {
            createChart();
        }
    },
    { deep: true }
);

watch(
    () => props.options,
    () => {
        createChart();
    },
    { deep: true }
);

// Expose methods for parent components
defineExpose({
    chart: chartInstance,
    updateChart,
    destroyChart,
});
</script>

<template>
    <div class="chart-container" :style="{ height: height + 'px' }">
        <canvas
            ref="chartRef"
            class="chart-canvas"
            :class="{ responsive: responsive }"
        ></canvas>
    </div>
</template>

<style lang="scss" scoped>
.chart-container {
    position: relative;
    width: 100%;

    .chart-canvas {
        width: 100% !important;
        height: 100% !important;

        &.responsive {
            max-width: 100%;
            max-height: 100%;
        }
    }
}
</style>
