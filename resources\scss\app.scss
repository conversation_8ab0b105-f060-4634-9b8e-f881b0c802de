// Импорт компонентов
@import "./components/buttons";
@import "./components/forms";
@import "./components/cards";
@import "./components/modals";
@import "./components/notifications";

// Импорт макетов
@import "./layout/sidebar";
@import "./layout/header";
@import "./layout/main";
@import "./layout/footer";

// Импорт тем
@import "./themes/light";
@import "./themes/dark";

// Variables
:root {
    // Основные цвета
    --primary-color: #ff0f3e;
    --primary-color-rgb: 255, 15, 62;
    --secondary-color: #ffdbe2;
    --secondary-color-rgb: 255, 219, 226;
    --success-color: #4caf50;
    --success-color-rgb: 76, 175, 80;
    --danger-color: #f44336;
    --danger-color-rgb: 244, 67, 54;
    --warning-color: #ff9800;
    --warning-color-rgb: 255, 152, 0;
    --info-color: #2196f3;
    --info-color-rgb: 33, 150, 243;

    // Цвета текста
    --text-primary: #212121;
    --text-secondary: #757575;
    --text-disabled: #9e9e9e;

    // Фоновые цвета
    --bg-primary: #ffffff;
    --bg-secondary: #f5f5f5;
    --bg-sidebar: #fafafa;
    --bg-card: #ffffff;

    // Размеры
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 70px;
    --header-height: 64px;

    // Тени
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    // Скругления
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;

    // Переходы
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

// Темная тема
[data-theme="dark"] {
    --primary-color: #ff0f3e;
    --secondary-color: #ffdbe2;

    // Цвета текста
    --text-primary: #e0e0e0;
    --text-secondary: #b0b0b0;
    --text-disabled: #757575;

    // Фоновые цвета
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-sidebar: #1a1a1a;
    --bg-card: #242424;

    // Тени
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
}

@import url("https://fonts.googleapis.com/css2?family=Onest:wght@100..900&display=swap");

// Reset
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    accent-color: var(--primary-color);
}

body {
    font-family: "Onest", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
        Helvetica, Arial, sans-serif;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.5;
    font-size: 16px;
}

a {
    text-decoration: none;
    color: var(--primary-color);

    &:hover {
        text-decoration: underline;
    }
}

button,
input,
select,
textarea {
    font-family: inherit;
}
