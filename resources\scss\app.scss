// Импорт компонентов
@import "./components/buttons";
@import "./components/forms";
@import "./components/cards";
@import "./components/modals";
@import "./components/notifications";
@import "./components/animations";

// Импорт макетов
@import "./layout/sidebar";
@import "./layout/header";
@import "./layout/main";
@import "./layout/footer";

// Импорт тем
@import "./themes/light";
@import "./themes/dark";

// Variables - Современная цветовая схема 2025
:root {
    // Основные цвета - современная палитра с градиентами
    --primary-color: #6366f1;
    --primary-color-rgb: 99, 102, 241;
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --primary-hover: #5b5bd6;
    --primary-light: #e0e7ff;
    --primary-dark: #4338ca;

    --secondary-color: #64748b;
    --secondary-color-rgb: 100, 116, 139;
    --secondary-gradient: linear-gradient(135deg, #64748b 0%, #475569 100%);
    --secondary-light: #f1f5f9;

    --accent-color: #06b6d4;
    --accent-color-rgb: 6, 182, 212;
    --accent-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);

    --success-color: #10b981;
    --success-color-rgb: 16, 185, 129;
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --success-light: #d1fae5;

    --danger-color: #ef4444;
    --danger-color-rgb: 239, 68, 68;
    --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --danger-light: #fee2e2;

    --warning-color: #f59e0b;
    --warning-color-rgb: 245, 158, 11;
    --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --warning-light: #fef3c7;

    --info-color: #3b82f6;
    --info-color-rgb: 59, 130, 246;
    --info-gradient: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    --info-light: #dbeafe;

    // Нейтральные цвета - улучшенная типографика
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --text-disabled: #94a3b8;
    --text-inverse: #ffffff;
    --text-muted: #6b7280;

    // Фоновые цвета - современные градиенты и поверхности
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-sidebar: #ffffff;
    --bg-card: #ffffff;
    --bg-elevated: #ffffff;
    --bg-overlay: rgba(15, 23, 42, 0.5);
    --bg-glass: rgba(255, 255, 255, 0.8);
    --bg-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --bg-gradient-surface: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);

    // Границы
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-medium: #cbd5e1;
    --border-dark: #94a3b8;

    // Размеры - оптимизированные для современного UX
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --header-height: 72px;
    --content-max-width: 1400px;
    --container-padding: 24px;

    // Тени - многослойные современные тени
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);

    // Скругления - современные радиусы
    --radius-xs: 2px;
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 20px;
    --radius-3xl: 24px;
    --radius-full: 9999px;

    // Переходы - плавные анимации
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-spring: 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    // Размеры шрифтов - современная типографическая шкала
    --font-size-xs: 0.75rem;    // 12px
    --font-size-sm: 0.875rem;   // 14px
    --font-size-base: 1rem;     // 16px
    --font-size-lg: 1.125rem;   // 18px
    --font-size-xl: 1.25rem;    // 20px
    --font-size-2xl: 1.5rem;    // 24px
    --font-size-3xl: 1.875rem;  // 30px
    --font-size-4xl: 2.25rem;   // 36px
    --font-size-5xl: 3rem;      // 48px

    // Высота строк
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    // Веса шрифтов
    --font-weight-thin: 100;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    // Z-индексы
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

// Темная тема - современная палитра 2025
[data-theme="dark"] {
    // Основные цвета остаются яркими в темной теме
    --primary-color: #818cf8;
    --primary-hover: #6366f1;
    --primary-light: #312e81;
    --primary-dark: #4338ca;

    --secondary-color: #94a3b8;
    --secondary-light: #1e293b;

    --accent-color: #22d3ee;

    // Цвета текста - улучшенный контраст
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --text-disabled: #64748b;
    --text-inverse: #0f172a;
    --text-muted: #94a3b8;

    // Фоновые цвета - современные темные поверхности
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-sidebar: #1e293b;
    --bg-card: #1e293b;
    --bg-elevated: #334155;
    --bg-overlay: rgba(15, 23, 42, 0.8);
    --bg-glass: rgba(30, 41, 59, 0.8);
    --bg-gradient-primary: linear-gradient(135deg, #4338ca 0%, #7c3aed 100%);
    --bg-gradient-secondary: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    --bg-gradient-surface: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);

    // Границы
    --border-color: #334155;
    --border-light: #475569;
    --border-medium: #64748b;
    --border-dark: #94a3b8;

    // Тени - адаптированные для темной темы
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);
    --shadow-glow: 0 0 20px rgba(129, 140, 248, 0.4);

    // Цвета состояний остаются яркими для хорошей видимости
    --success-light: #064e3b;
    --danger-light: #7f1d1d;
    --warning-light: #78350f;
    --info-light: #1e3a8a;
}

// Современные шрифты 2025
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=JetBrains+Mono:wght@100..800&display=swap");

// Улучшенный reset
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    accent-color: var(--primary-color);
    scroll-behavior: smooth;
    font-size: 16px;

    @media (max-width: 768px) {
        font-size: 14px;
    }
}

body {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
        "Helvetica Neue", Arial, sans-serif;
    font-weight: var(--font-weight-normal);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: "kern" 1, "liga" 1;
    overflow-x: hidden;
}

// Улучшенная типографика
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    color: var(--text-primary);
    margin-bottom: 0.5em;

    &:first-child {
        margin-top: 0;
    }
}

h1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);

    @media (max-width: 768px) {
        font-size: var(--font-size-3xl);
    }
}

h2 {
    font-size: var(--font-size-3xl);

    @media (max-width: 768px) {
        font-size: var(--font-size-2xl);
    }
}

h3 {
    font-size: var(--font-size-2xl);

    @media (max-width: 768px) {
        font-size: var(--font-size-xl);
    }
}

h4 {
    font-size: var(--font-size-xl);
}

h5 {
    font-size: var(--font-size-lg);
}

h6 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
}

p {
    margin-bottom: 1em;
    line-height: var(--line-height-relaxed);

    &:last-child {
        margin-bottom: 0;
    }
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);

    &:hover {
        color: var(--primary-hover);
        text-decoration: underline;
    }

    &:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
        border-radius: var(--radius-sm);
    }
}

// Улучшенные элементы форм
button,
input,
select,
textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

// Код
code,
pre {
    font-family: "JetBrains Mono", "Fira Code", "Monaco", "Consolas", monospace;
    font-size: 0.875em;
}

code {
    background-color: var(--bg-tertiary);
    padding: 0.125rem 0.25rem;
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
}

pre {
    background-color: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--radius-md);
    overflow-x: auto;

    code {
        background: none;
        padding: 0;
    }
}

// Улучшенные списки
ul, ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;

    li {
        margin-bottom: 0.25rem;
        line-height: var(--line-height-relaxed);
    }
}

// Выделение текста
::selection {
    background-color: rgba(var(--primary-color-rgb), 0.2);
    color: var(--text-primary);
}

// Скроллбар
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-full);

    &:hover {
        background: var(--border-dark);
    }
}

// Фокус для доступности
:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

// Адаптивная система 2025
// Брейкпоинты
$breakpoints: (
    xs: 480px,
    sm: 640px,
    md: 768px,
    lg: 1024px,
    xl: 1280px,
    2xl: 1536px
);

// Миксины для медиа-запросов
@mixin mobile-only {
    @media (max-width: #{map-get($breakpoints, sm) - 1px}) {
        @content;
    }
}

@mixin tablet-up {
    @media (min-width: #{map-get($breakpoints, md)}) {
        @content;
    }
}

@mixin desktop-up {
    @media (min-width: #{map-get($breakpoints, lg)}) {
        @content;
    }
}

@mixin large-desktop-up {
    @media (min-width: #{map-get($breakpoints, xl)}) {
        @content;
    }
}

// Контейнеры
.container {
    width: 100%;
    margin: 0 auto;
    padding: 0 var(--mobile-padding);

    @include tablet-up {
        padding: 0 var(--tablet-padding);
    }

    @include desktop-up {
        padding: 0 var(--desktop-padding);
        max-width: 1200px;
    }

    @include large-desktop-up {
        max-width: 1400px;
    }
}

.container-fluid {
    width: 100%;
    padding: 0 var(--mobile-padding);

    @include tablet-up {
        padding: 0 var(--tablet-padding);
    }

    @include desktop-up {
        padding: 0 var(--desktop-padding);
    }
}

// Адаптивные утилиты
.d-mobile-none {
    @include mobile-only {
        display: none !important;
    }
}

.d-tablet-none {
    @include tablet-up {
        display: none !important;
    }
}

.d-desktop-none {
    @include desktop-up {
        display: none !important;
    }
}

// Адаптивный текст
.text-responsive {
    font-size: var(--font-size-lg);

    @include mobile-only {
        font-size: var(--font-size-base);
    }

    @include tablet-up {
        font-size: var(--font-size-xl);
    }

    @include desktop-up {
        font-size: var(--font-size-2xl);
    }
}

// Адаптивные отступы
.p-responsive {
    padding: var(--mobile-padding);

    @include tablet-up {
        padding: var(--tablet-padding);
    }

    @include desktop-up {
        padding: var(--desktop-padding);
    }
}

// Адаптивная сетка
.grid {
    display: grid;
    gap: 16px;

    &.grid-1 {
        grid-template-columns: 1fr;
    }

    &.grid-2 {
        grid-template-columns: 1fr;

        @include tablet-up {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    &.grid-3 {
        grid-template-columns: 1fr;

        @include tablet-up {
            grid-template-columns: repeat(2, 1fr);
        }

        @include desktop-up {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    &.grid-4 {
        grid-template-columns: 1fr;

        @include tablet-up {
            grid-template-columns: repeat(2, 1fr);
        }

        @include desktop-up {
            grid-template-columns: repeat(4, 1fr);
        }
    }
}

// Адаптивные флексы
.flex-responsive {
    display: flex;
    flex-direction: column;
    gap: 16px;

    @include tablet-up {
        flex-direction: row;
        gap: 24px;
    }
}

// Мобильные улучшения
@include mobile-only {
    // Увеличиваем размеры кликабельных элементов
    .btn, .form-control, .action-button {
        min-height: 48px;
        font-size: var(--font-size-base);
    }

    // Улучшаем читаемость
    body {
        font-size: var(--font-size-base);
        line-height: var(--line-height-relaxed);
    }

    // Оптимизируем карточки для мобильных
    .card {
        margin-bottom: 16px;
        border-radius: var(--radius-lg);

        .card-header, .card-body, .card-footer {
            padding: 16px;
        }
    }

    // Адаптивные таблицы
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}
