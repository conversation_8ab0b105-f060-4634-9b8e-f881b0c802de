<script setup>
import { Head } from "@inertiajs/vue3";
import { ref, onMounted } from "vue";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import UpdateProfileInformationForm from "./Partials/UpdateProfileInformationForm.vue";
import UpdatePasswordForm from "./Partials/UpdatePasswordForm.vue";
import UpdateArtistProfileForm from "./Partials/UpdateArtistProfileForm.vue";
import { User, Lock, Music } from "lucide-vue-next";

// Свойства из маршрута
defineProps({
    sessions: Array,
    confirmsTwoFactorAuthentication: Boolean,
});

// Активная вкладка
const activeTab = ref("personal-info");

// Функция для переключения вкладок
const switchTab = (tabId) => {
    activeTab.value = tabId;
};

// Хлебные крошки для страницы
const breadcrumbs = [{ name: "Профиль" }];

// Инициализация вкладок по хэшу URL
onMounted(() => {
    const hash = window.location.hash.substring(1);
    if (["personal-info", "security", "artist-profile"].includes(hash)) {
        activeTab.value = hash;
    }
});
</script>

<template>
    <DashboardLayout title="Профиль пользователя" :breadcrumbs="breadcrumbs">
        <Head title="Профиль" />

        <div class="content-container">
            <div class="tabs">
                <a
                    href="#personal-info"
                    class="tab-item"
                    :class="{ active: activeTab === 'personal-info' }"
                    @click.prevent="switchTab('personal-info')"
                >
                    <User size="18" class="tab-icon" />
                    <span>Личная информация</span>
                </a>
                <a
                    href="#security"
                    class="tab-item"
                    :class="{ active: activeTab === 'security' }"
                    @click.prevent="switchTab('security')"
                >
                    <Lock size="18" class="tab-icon" />
                    <span>Безопасность</span>
                </a>
                <a
                    href="#artist-profile"
                    class="tab-item"
                    :class="{ active: activeTab === 'artist-profile' }"
                    @click.prevent="switchTab('artist-profile')"
                >
                    <Music size="18" class="tab-icon" />
                    <span>Профиль артиста</span>
                </a>
            </div>

            <div class="tab-content">
                <!-- Личная информация -->
                <div
                    id="personal-info"
                    class="tab-pane"
                    :class="{ active: activeTab === 'personal-info' }"
                >
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Личная информация</h3>
                            <p class="card-description">
                                Обновите свою учетную запись
                            </p>
                        </div>
                        <div class="card-body">
                            <UpdateProfileInformationForm
                                :user="$page.props.auth.user"
                            />
                        </div>
                    </div>
                </div>

                <!-- Безопасность -->
                <div
                    id="security"
                    class="tab-pane"
                    :class="{ active: activeTab === 'security' }"
                >
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Настройки безопасности</h3>
                            <p class="card-description">Управление паролем</p>
                        </div>
                        <div class="card-body">
                            <UpdatePasswordForm />
                        </div>
                    </div>
                </div>

                <!-- Профиль артиста -->
                <div
                    id="artist-profile"
                    class="tab-pane"
                    :class="{ active: activeTab === 'artist-profile' }"
                >
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Профиль артиста</h3>
                            <p class="card-description">
                                Управление вашим публичным профилем
                            </p>
                        </div>
                        <div class="card-body">
                            <UpdateArtistProfileForm />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </DashboardLayout>
</template>

<style scoped>
.tabs {
    display: flex;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--border-color);
}

.tab-item {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    gap: 8px;
    color: var(--text-secondary);
    border-bottom: 2px solid transparent;
    text-decoration: none;
}

.tab-item.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-icon {
    color: currentColor;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: 24px;
}

.card-header {
    padding: 24px;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    margin: 0 0 4px;
    font-size: 1.125rem;
    font-weight: 600;
}

.card-description {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.875rem;
}

.card-body {
    padding: 24px;
}
</style>
