<?php

namespace Tests\Feature;

use App\Events\ApplicationCreated;
use App\Events\ApplicationStatusUpdated;
use App\Models\Application;
use App\Models\User;
use App\Notifications\ApplicationStatusChanged;
use App\Notifications\NewApplicationReceived;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class NotificationTest extends TestCase
{
    use RefreshDatabase;

    private User $artist;
    private User $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->artist = User::factory()->create(['role' => 'artist']);
        $this->admin = User::factory()->create(['role' => 'admin']);
    }

    /** @test */
    public function it_sends_notification_when_application_is_created()
    {
        Notification::fake();

        $application = Application::factory()->create([
            'user_id' => $this->artist->id,
            'status' => 'pending'
        ]);

        // Отправляем событие
        event(new ApplicationCreated($application));

        // Проверяем, что админы получили уведомление
        Notification::assertSentTo(
            [$this->admin],
            NewApplicationReceived::class,
            function ($notification) use ($application) {
                return $notification->application->id === $application->id;
            }
        );
    }

    /** @test */
    public function it_sends_notification_when_application_status_changes()
    {
        Notification::fake();

        $application = Application::factory()->create([
            'user_id' => $this->artist->id,
            'status' => 'pending'
        ]);

        // Отправляем событие об изменении статуса
        event(new ApplicationStatusUpdated($application, 'pending', 'approved'));

        // Проверяем, что артист получил уведомление
        Notification::assertSentTo(
            [$this->artist],
            ApplicationStatusChanged::class,
            function ($notification) use ($application) {
                return $notification->application->id === $application->id &&
                       $notification->oldStatus === 'pending' &&
                       $notification->newStatus === 'approved';
            }
        );
    }

    /** @test */
    public function user_can_get_their_notifications()
    {
        $this->actingAs($this->artist);

        // Создаем уведомление
        $this->artist->notify(new ApplicationStatusChanged(
            Application::factory()->create(['user_id' => $this->artist->id]),
            'pending',
            'approved'
        ));

        $response = $this->getJson('/api/notifications');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id',
                             'type',
                             'data',
                             'read_at',
                             'created_at'
                         ]
                     ]
                 ]);
    }

    /** @test */
    public function user_can_get_unread_notifications_count()
    {
        $this->actingAs($this->artist);

        // Создаем несколько уведомлений
        $this->artist->notify(new ApplicationStatusChanged(
            Application::factory()->create(['user_id' => $this->artist->id]),
            'pending',
            'approved'
        ));

        $this->artist->notify(new ApplicationStatusChanged(
            Application::factory()->create(['user_id' => $this->artist->id]),
            'pending',
            'rejected'
        ));

        $response = $this->getJson('/api/notifications/unread-count');

        $response->assertStatus(200)
                 ->assertJson(['count' => 2]);
    }

    /** @test */
    public function user_can_mark_notification_as_read()
    {
        $this->actingAs($this->artist);

        // Создаем уведомление
        $this->artist->notify(new ApplicationStatusChanged(
            Application::factory()->create(['user_id' => $this->artist->id]),
            'pending',
            'approved'
        ));

        $notification = $this->artist->notifications()->first();

        $response = $this->postJson("/api/notifications/{$notification->id}/read");

        $response->assertStatus(200);

        // Проверяем, что уведомление отмечено как прочитанное
        $notification->refresh();
        $this->assertNotNull($notification->read_at);
    }

    /** @test */
    public function user_can_mark_all_notifications_as_read()
    {
        $this->actingAs($this->artist);

        // Создаем несколько уведомлений
        $this->artist->notify(new ApplicationStatusChanged(
            Application::factory()->create(['user_id' => $this->artist->id]),
            'pending',
            'approved'
        ));

        $this->artist->notify(new ApplicationStatusChanged(
            Application::factory()->create(['user_id' => $this->artist->id]),
            'pending',
            'rejected'
        ));

        $response = $this->postJson('/api/notifications/mark-all-read');

        $response->assertStatus(200);

        // Проверяем, что все уведомления отмечены как прочитанные
        $unreadCount = $this->artist->unreadNotifications()->count();
        $this->assertEquals(0, $unreadCount);
    }

    /** @test */
    public function user_can_delete_notification()
    {
        $this->actingAs($this->artist);

        // Создаем уведомление
        $this->artist->notify(new ApplicationStatusChanged(
            Application::factory()->create(['user_id' => $this->artist->id]),
            'pending',
            'approved'
        ));

        $notification = $this->artist->notifications()->first();

        $response = $this->deleteJson("/api/notifications/{$notification->id}");

        $response->assertStatus(200);

        // Проверяем, что уведомление удалено
        $this->assertDatabaseMissing('notifications', ['id' => $notification->id]);
    }

    /** @test */
    public function user_can_clear_all_notifications()
    {
        $this->actingAs($this->artist);

        // Создаем несколько уведомлений
        $this->artist->notify(new ApplicationStatusChanged(
            Application::factory()->create(['user_id' => $this->artist->id]),
            'pending',
            'approved'
        ));

        $this->artist->notify(new ApplicationStatusChanged(
            Application::factory()->create(['user_id' => $this->artist->id]),
            'pending',
            'rejected'
        ));

        $response = $this->deleteJson('/api/notifications');

        $response->assertStatus(200);

        // Проверяем, что все уведомления удалены
        $notificationsCount = $this->artist->notifications()->count();
        $this->assertEquals(0, $notificationsCount);
    }

    /** @test */
    public function user_cannot_access_other_users_notifications()
    {
        $otherUser = User::factory()->create(['role' => 'artist']);
        
        $this->actingAs($this->artist);

        // Создаем уведомление для другого пользователя
        $otherUser->notify(new ApplicationStatusChanged(
            Application::factory()->create(['user_id' => $otherUser->id]),
            'pending',
            'approved'
        ));

        $notification = $otherUser->notifications()->first();

        // Пытаемся получить доступ к чужому уведомлению
        $response = $this->postJson("/api/notifications/{$notification->id}/read");

        $response->assertStatus(404);
    }

    /** @test */
    public function notification_contains_correct_data_structure()
    {
        $application = Application::factory()->create(['user_id' => $this->artist->id]);
        
        $notification = new ApplicationStatusChanged($application, 'pending', 'approved');
        $data = $notification->toArray($this->artist);

        $this->assertArrayHasKey('type', $data);
        $this->assertArrayHasKey('application_id', $data);
        $this->assertArrayHasKey('application_title', $data);
        $this->assertArrayHasKey('old_status', $data);
        $this->assertArrayHasKey('new_status', $data);
        $this->assertArrayHasKey('message', $data);
        $this->assertArrayHasKey('action_url', $data);

        $this->assertEquals('application_status_changed', $data['type']);
        $this->assertEquals($application->id, $data['application_id']);
        $this->assertEquals('pending', $data['old_status']);
        $this->assertEquals('approved', $data['new_status']);
    }
}
