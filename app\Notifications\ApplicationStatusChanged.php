<?php

namespace App\Notifications;

use App\Models\Application;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ApplicationStatusChanged extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Application $application,
        public string $oldStatus,
        public string $newStatus
    ) {
        $this->onQueue('notifications');
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $statusText = $this->getStatusText($this->newStatus);

        return (new MailMessage)
            ->subject("Статус заявки изменен: {$this->application->title}")
            ->greeting("Здравствуйте, {$notifiable->name}!")
            ->line("Статус вашей заявки \"{$this->application->title}\" был изменен.")
            ->line("Новый статус: {$statusText}")
            ->when($this->application->admin_notes, function ($mail) {
                return $mail->line("Комментарий администратора: {$this->application->admin_notes}");
            })
            ->action('Просмотреть заявку', route('applications.show', $this->application))
            ->line('Спасибо за использование Madd Label!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'application_status_changed',
            'application_id' => $this->application->id,
            'application_title' => $this->application->title,
            'old_status' => $this->oldStatus,
            'new_status' => $this->newStatus,
            'admin_notes' => $this->application->admin_notes,
            'message' => $this->getMessage(),
            'action_url' => route('applications.show', $this->application),
        ];
    }

    /**
     * Get the broadcast representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'application_status_changed',
            'data' => $this->toArray($notifiable),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the notification message.
     */
    private function getMessage(): string
    {
        $statusText = $this->getStatusText($this->newStatus);
        return "Статус заявки \"{$this->application->title}\" изменен на: {$statusText}";
    }

    /**
     * Get human-readable status text.
     */
    private function getStatusText(string $status): string
    {
        return match($status) {
            'pending' => 'На рассмотрении',
            'approved' => 'Одобрена',
            'rejected' => 'Отклонена',
            default => ucfirst($status),
        };
    }
}
