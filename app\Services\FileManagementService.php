<?php

namespace App\Services;

use App\Jobs\ProcessFileUpload;
use App\Jobs\ProcessFileCleanup;
use App\Jobs\CreateReleaseArchive;
use App\Models\Release;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class FileManagementService
{
    /**
     * Upload file asynchronously.
     */
    public function uploadFileAsync(
        UploadedFile $file,
        string $destinationPath,
        ?array $metadata = null
    ): string {
        // Сохраняем файл во временное хранилище
        $tempPath = $file->store('temp/uploads', 'local');

        // Запускаем асинхронную обработку
        ProcessFileUpload::dispatch(
            filePath: $tempPath,
            originalName: $file->getClientOriginalName(),
            mimeType: $file->getMimeType(),
            destinationPath: $destinationPath,
            metadata: $metadata
        );

        Log::info("Файл поставлен в очередь на обработку: {$file->getClientOriginalName()}");

        return $tempPath;
    }

    /**
     * Upload multiple files asynchronously.
     */
    public function uploadMultipleFilesAsync(
        array $files,
        string $destinationPath,
        ?array $metadata = null
    ): array {
        $tempPaths = [];

        foreach ($files as $file) {
            if ($file instanceof UploadedFile) {
                $tempPaths[] = $this->uploadFileAsync($file, $destinationPath, $metadata);
            }
        }

        return $tempPaths;
    }

    /**
     * Schedule file cleanup after approval.
     */
    public function scheduleCleanupAfterApproval(array $filePaths, int $delayHours = 24): void
    {
        ProcessFileCleanup::cleanupAfterApproval($filePaths, $delayHours)->dispatch();
        
        Log::info("Запланирована очистка файлов после одобрения через {$delayHours} часов");
    }

    /**
     * Schedule file cleanup after rejection.
     */
    public function scheduleCleanupAfterRejection(array $filePaths, int $delayHours = 72): void
    {
        ProcessFileCleanup::cleanupAfterRejection($filePaths, $delayHours)->dispatch();
        
        Log::info("Запланирована очистка файлов после отклонения через {$delayHours} часов");
    }

    /**
     * Clean up temporary files immediately.
     */
    public function cleanupTemporaryFiles(array $filePaths): void
    {
        ProcessFileCleanup::cleanupTemporary($filePaths)->dispatch();
        
        Log::info("Запущена немедленная очистка временных файлов");
    }

    /**
     * Create release archive.
     */
    public function createReleaseArchive(int $releaseId, string $requestedBy, string $format = 'zip'): void
    {
        CreateReleaseArchive::dispatch($releaseId, $requestedBy, $format);
        
        Log::info("Запущено создание архива для релиза ID: {$releaseId}");
    }

    /**
     * Get release archive download URL.
     */
    public function getReleaseArchiveUrl(Release $release): ?string
    {
        $metaInfo = json_decode($release->meta_info, true) ?: [];
        $archives = $metaInfo['archives'] ?? [];

        if (empty($archives)) {
            return null;
        }

        // Возвращаем последний созданный архив
        $latestArchive = end($archives);
        
        if (Storage::disk('public')->exists($latestArchive['path'])) {
            return Storage::disk('public')->url($latestArchive['path']);
        }

        return null;
    }

    /**
     * Validate file type and size.
     */
    public function validateFile(UploadedFile $file, array $allowedTypes, int $maxSizeMB): bool
    {
        // Проверка типа файла
        if (!in_array($file->getMimeType(), $allowedTypes)) {
            return false;
        }

        // Проверка размера файла
        $maxSizeBytes = $maxSizeMB * 1024 * 1024;
        if ($file->getSize() > $maxSizeBytes) {
            return false;
        }

        return true;
    }

    /**
     * Get allowed file types for different contexts.
     */
    public function getAllowedFileTypes(string $context): array
    {
        return match($context) {
            'cover' => [
                'image/jpeg',
                'image/png',
                'image/webp'
            ],
            'track' => [
                'audio/mpeg',
                'audio/wav',
                'audio/flac',
                'audio/aac'
            ],
            'attachment' => [
                'application/pdf',
                'image/jpeg',
                'image/png',
                'text/plain',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ],
            default => []
        };
    }

    /**
     * Get max file size for different contexts (in MB).
     */
    public function getMaxFileSize(string $context): int
    {
        return match($context) {
            'cover' => 5,      // 5MB для обложек
            'track' => 50,     // 50MB для треков
            'attachment' => 10, // 10MB для вложений
            default => 2
        };
    }

    /**
     * Clean up old temporary files.
     */
    public function cleanupOldTemporaryFiles(int $olderThanHours = 24): void
    {
        $tempFiles = Storage::disk('local')->files('temp');
        $cutoffTime = now()->subHours($olderThanHours);
        $filesToDelete = [];

        foreach ($tempFiles as $file) {
            $lastModified = Storage::disk('local')->lastModified($file);
            if ($lastModified < $cutoffTime->timestamp) {
                $filesToDelete[] = $file;
            }
        }

        if (!empty($filesToDelete)) {
            foreach ($filesToDelete as $file) {
                Storage::disk('local')->delete($file);
            }
            
            Log::info("Очищено старых временных файлов: " . count($filesToDelete));
        }
    }

    /**
     * Get file storage statistics.
     */
    public function getStorageStatistics(): array
    {
        $publicDisk = Storage::disk('public');
        $localDisk = Storage::disk('local');

        return [
            'public_storage' => [
                'total_files' => count($publicDisk->allFiles()),
                'total_size' => $this->calculateDirectorySize($publicDisk, ''),
            ],
            'temp_storage' => [
                'total_files' => count($localDisk->files('temp')),
                'total_size' => $this->calculateDirectorySize($localDisk, 'temp'),
            ],
            'archives' => [
                'total_files' => count($publicDisk->files('archives')),
                'total_size' => $this->calculateDirectorySize($publicDisk, 'archives'),
            ]
        ];
    }

    /**
     * Calculate directory size.
     */
    private function calculateDirectorySize($disk, string $directory): int
    {
        $files = $disk->allFiles($directory);
        $totalSize = 0;

        foreach ($files as $file) {
            $totalSize += $disk->size($file);
        }

        return $totalSize;
    }

    /**
     * Clean up old archives (static method for scheduler).
     */
    public static function cleanupOldArchives(int $olderThanDays = 30): void
    {
        $publicDisk = Storage::disk('public');
        $cutoffTime = now()->subDays($olderThanDays);
        $archiveFiles = $publicDisk->files('archives');
        $deletedCount = 0;

        foreach ($archiveFiles as $file) {
            $lastModified = $publicDisk->lastModified($file);
            if ($lastModified < $cutoffTime->timestamp) {
                $publicDisk->delete($file);
                $deletedCount++;
            }
        }

        if ($deletedCount > 0) {
            Log::info("Очищено старых архивов: {$deletedCount}");
        }
    }
}
