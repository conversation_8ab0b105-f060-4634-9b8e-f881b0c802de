<?php

namespace Tests\Feature;

use App\Models\Application;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class NotificationEndToEndTest extends TestCase
{
    use RefreshDatabase;

    private User $artist;
    private User $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->artist = User::factory()->create(['role' => 'artist']);
        $this->admin = User::factory()->create(['role' => 'admin']);
    }

    /** @test */
    public function complete_notification_flow_works()
    {
        // 1. Создаем заявку через API
        $this->actingAs($this->artist);
        
        $applicationData = [
            'type' => 'promo',
            'title' => 'Тестовая заявка для уведомлений',
            'description' => 'Это тестовая заявка для проверки полного цикла уведомлений'
        ];

        $response = $this->postJson('/api/applications', $applicationData);
        $response->assertStatus(201);
        
        $application = Application::latest()->first();
        $this->assertNotNull($application);

        // 2. Проверяем, что админ получил уведомление о новой заявке
        $this->actingAs($this->admin);
        
        $notificationsResponse = $this->getJson('/api/notifications');
        $notificationsResponse->assertStatus(200);
        
        $notifications = $notificationsResponse->json('data');
        $this->assertNotEmpty($notifications);
        
        $newApplicationNotification = collect($notifications)->first(function ($notification) use ($application) {
            return $notification['data']['type'] === 'new_application_received' &&
                   $notification['data']['application_id'] === $application->id;
        });
        
        $this->assertNotNull($newApplicationNotification);

        // 3. Проверяем количество непрочитанных уведомлений
        $unreadCountResponse = $this->getJson('/api/notifications/unread-count');
        $unreadCountResponse->assertStatus(200);
        $this->assertGreaterThan(0, $unreadCountResponse->json('count'));

        // 4. Админ рассматривает заявку
        $reviewResponse = $this->postJson("/api/applications/{$application->id}/review", [
            'status' => 'approved',
            'admin_notes' => 'Заявка одобрена'
        ]);
        $reviewResponse->assertStatus(200);

        // 5. Проверяем, что артист получил уведомление об изменении статуса
        $this->actingAs($this->artist);
        
        $artistNotificationsResponse = $this->getJson('/api/notifications');
        $artistNotificationsResponse->assertStatus(200);
        
        $artistNotifications = $artistNotificationsResponse->json('data');
        $statusChangeNotification = collect($artistNotifications)->first(function ($notification) use ($application) {
            return $notification['data']['type'] === 'application_status_changed' &&
                   $notification['data']['application_id'] === $application->id;
        });
        
        $this->assertNotNull($statusChangeNotification);
        $this->assertEquals('approved', $statusChangeNotification['data']['new_status']);

        // 6. Артист отмечает уведомление как прочитанное
        $markReadResponse = $this->postJson("/api/notifications/{$statusChangeNotification['id']}/read");
        $markReadResponse->assertStatus(200);

        // 7. Проверяем, что уведомление отмечено как прочитанное
        $updatedNotificationsResponse = $this->getJson('/api/notifications');
        $updatedNotifications = $updatedNotificationsResponse->json('data');
        
        $readNotification = collect($updatedNotifications)->first(function ($notification) use ($statusChangeNotification) {
            return $notification['id'] === $statusChangeNotification['id'];
        });
        
        $this->assertNotNull($readNotification['read_at']);
    }

    /** @test */
    public function notification_api_endpoints_work_correctly()
    {
        $this->actingAs($this->artist);

        // Создаем несколько уведомлений
        $application1 = Application::factory()->create(['user_id' => $this->artist->id]);
        $application2 = Application::factory()->create(['user_id' => $this->artist->id]);

        // Имитируем отправку уведомлений
        $this->artist->notify(new \App\Notifications\ApplicationStatusChanged($application1, 'pending', 'approved'));
        $this->artist->notify(new \App\Notifications\ApplicationStatusChanged($application2, 'pending', 'rejected'));

        // Тестируем получение уведомлений
        $response = $this->getJson('/api/notifications');
        $response->assertStatus(200);
        $notifications = $response->json('data');
        $this->assertCount(2, $notifications);

        // Тестируем количество непрочитанных
        $unreadResponse = $this->getJson('/api/notifications/unread-count');
        $unreadResponse->assertStatus(200)
                      ->assertJson(['count' => 2]);

        // Тестируем отметку всех как прочитанных
        $markAllResponse = $this->postJson('/api/notifications/mark-all-read');
        $markAllResponse->assertStatus(200);

        // Проверяем, что количество непрочитанных стало 0
        $unreadAfterResponse = $this->getJson('/api/notifications/unread-count');
        $unreadAfterResponse->assertStatus(200)
                           ->assertJson(['count' => 0]);

        // Тестируем удаление уведомления
        $notificationId = $notifications[0]['id'];
        $deleteResponse = $this->deleteJson("/api/notifications/{$notificationId}");
        $deleteResponse->assertStatus(200);

        // Проверяем, что уведомление удалено
        $afterDeleteResponse = $this->getJson('/api/notifications');
        $afterDeleteNotifications = $afterDeleteResponse->json('data');
        $this->assertCount(1, $afterDeleteNotifications);

        // Тестируем очистку всех уведомлений
        $clearResponse = $this->deleteJson('/api/notifications');
        $clearResponse->assertStatus(200);

        // Проверяем, что все уведомления удалены
        $finalResponse = $this->getJson('/api/notifications');
        $finalNotifications = $finalResponse->json('data');
        $this->assertCount(0, $finalNotifications);
    }

    /** @test */
    public function notification_security_works()
    {
        // Создаем двух пользователей
        $user1 = User::factory()->create(['role' => 'artist']);
        $user2 = User::factory()->create(['role' => 'artist']);

        // Создаем уведомление для первого пользователя
        $application = Application::factory()->create(['user_id' => $user1->id]);
        $user1->notify(new \App\Notifications\ApplicationStatusChanged($application, 'pending', 'approved'));

        $notification = $user1->notifications()->first();

        // Второй пользователь не должен иметь доступ к уведомлению первого
        $this->actingAs($user2);
        
        $response = $this->postJson("/api/notifications/{$notification->id}/read");
        $response->assertStatus(404);

        $deleteResponse = $this->deleteJson("/api/notifications/{$notification->id}");
        $deleteResponse->assertStatus(404);
    }
}
