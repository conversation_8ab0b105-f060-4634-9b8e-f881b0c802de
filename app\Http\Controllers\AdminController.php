<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Release;
use App\Models\User;
use App\Models\Artist;
use App\Events\ApplicationStatusUpdated;
use App\Events\ReleaseStatusUpdated;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Check if user has admin access.
     */
    private function checkAdminAccess(): void
    {
        /** @var User|null $user */
        $user = Auth::user();
        if (!$user || (!$user->isAdmin() && !$user->isManager())) {
            abort(403, 'Доступ запрещен');
        }
    }

    /**
     * Admin dashboard with overview.
     */
    public function dashboard(): Response
    {
        $this->checkAdminAccess();

        // Статистика для админ-дашборда
        $stats = [
            'pending_applications' => Application::where('status', 'pending')->count(),
            'pending_releases' => Release::where('status', 'pending')->count(),
            'total_users' => User::count(),
            'new_users_today' => User::whereDate('created_at', today())->count(),
            'total_artists' => Artist::count(),
            'active_artists' => Artist::whereHas('releases', function($query) {
                $query->where('created_at', '>=', now()->subDays(30));
            })->count(),
        ];

        // Последние заявки на рассмотрении
        $pendingApplications = Application::with(['user'])
            ->where('status', 'pending')
            ->latest()
            ->limit(5)
            ->get();

        // Последние релизы на рассмотрении
        $pendingReleases = Release::with(['artist.user'])
            ->where('status', 'pending')
            ->latest()
            ->limit(5)
            ->get();

        // Последние зарегистрированные пользователи
        $recentUsers = User::latest()
            ->limit(5)
            ->get();

        // Данные для графиков
        $chartsData = $this->getChartsData();

        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'pendingApplications' => $pendingApplications,
            'pendingReleases' => $pendingReleases,
            'recentUsers' => $recentUsers,
            'chartsData' => $chartsData,
        ]);
    }

    /**
     * Applications management page.
     */
    public function applications(Request $request): Response
    {
        $this->checkAdminAccess();

        $query = Application::with(['user']);

        // Фильтрация
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function($userQuery) use ($request) {
                      $userQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $applications = $query->latest()->paginate(20);

        return Inertia::render('Admin/Applications', [
            'applications' => $applications,
            'filters' => $request->only(['status', 'type', 'search']),
        ]);
    }

    /**
     * Releases management page.
     */
    public function releases(Request $request): Response
    {
        $this->checkAdminAccess();

        $query = Release::with(['artist.user']);

        // Фильтрация
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhereHas('artist.user', function($userQuery) use ($request) {
                      $userQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $releases = $query->latest()->paginate(20);

        return Inertia::render('Admin/Releases', [
            'releases' => $releases,
            'filters' => $request->only(['status', 'type', 'search']),
        ]);
    }

    /**
     * Users management page.
     */
    public function users(Request $request): Response
    {
        $this->checkAdminAccess();

        $query = User::with(['artist']);

        // Фильтрация
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        $users = $query->latest()->paginate(20);

        return Inertia::render('Admin/Users', [
            'users' => $users,
            'filters' => $request->only(['role', 'search']),
        ]);
    }

    /**
     * Update application status.
     */
    public function updateApplicationStatus(Request $request, Application $application): JsonResponse
    {
        $this->checkAdminAccess();

        $request->validate([
            'status' => 'required|in:pending,approved,rejected',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $oldStatus = $application->status;
        
        $application->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
        ]);

        // Отправляем событие об изменении статуса
        ApplicationStatusUpdated::dispatch($application, $oldStatus);

        return response()->json([
            'message' => 'Статус заявки обновлен',
            'application' => $application->load(['user', 'reviewer']),
        ]);
    }

    /**
     * Update release status.
     */
    public function updateReleaseStatus(Request $request, Release $release): JsonResponse
    {
        $this->checkAdminAccess();

        $request->validate([
            'status' => 'required|in:draft,pending,published,rejected',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $oldStatus = $release->status;
        
        $release->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
        ]);

        // Отправляем событие об изменении статуса
        ReleaseStatusUpdated::dispatch($release, $oldStatus);

        return response()->json([
            'message' => 'Статус релиза обновлен',
            'release' => $release->load(['artist.user']),
        ]);
    }

    /**
     * Update user role.
     */
    public function updateUserRole(Request $request, User $user): JsonResponse
    {
        $this->checkAdminAccess();

        $request->validate([
            'role' => 'required|in:artist,manager,admin',
        ]);

        $user->update([
            'role' => $request->role,
        ]);

        return response()->json([
            'message' => 'Роль пользователя обновлена',
            'user' => $user->load(['artist']),
        ]);
    }

    /**
     * Bulk actions for applications.
     */
    public function bulkApplicationAction(Request $request): JsonResponse
    {
        $this->checkAdminAccess();

        $request->validate([
            'action' => 'required|in:approve,reject,delete',
            'application_ids' => 'required|array',
            'application_ids.*' => 'exists:applications,id',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $applications = Application::whereIn('id', $request->application_ids)->get();
        $count = 0;

        foreach ($applications as $application) {
            switch ($request->action) {
                case 'approve':
                    $application->update([
                        'status' => 'approved',
                        'admin_notes' => $request->admin_notes,
                        'reviewed_by' => Auth::id(),
                        'reviewed_at' => now(),
                    ]);
                    ApplicationStatusUpdated::dispatch($application, $application->getOriginal('status'));
                    $count++;
                    break;

                case 'reject':
                    $application->update([
                        'status' => 'rejected',
                        'admin_notes' => $request->admin_notes,
                        'reviewed_by' => Auth::id(),
                        'reviewed_at' => now(),
                    ]);
                    ApplicationStatusUpdated::dispatch($application, $application->getOriginal('status'));
                    $count++;
                    break;

                case 'delete':
                    $application->delete();
                    $count++;
                    break;
            }
        }

        return response()->json([
            'message' => "Обработано заявок: {$count}",
            'count' => $count,
        ]);
    }

    /**
     * Analytics for admin.
     */
    public function analytics(Request $request): Response
    {
        $this->checkAdminAccess();

        // Используем существующий AnalyticsController
        $analyticsController = new AnalyticsController();
        return $analyticsController->index($request);
    }

    /**
     * Get charts data for admin dashboard.
     */
    private function getChartsData(): array
    {
        // Данные за последние 30 дней
        $days = 30;
        $dates = [];
        $applicationsData = [];
        $releasesData = [];
        $usersData = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dates[] = $date->format('M d');

            $dayStart = $date->startOfDay();
            $dayEnd = $date->endOfDay();

            $applicationsData[] = Application::whereBetween('created_at', [$dayStart, $dayEnd])->count();
            $releasesData[] = Release::whereBetween('created_at', [$dayStart, $dayEnd])->count();
            $usersData[] = User::whereBetween('created_at', [$dayStart, $dayEnd])->count();
        }

        // Статистика по статусам заявок
        $applicationStatusData = [
            'labels' => ['На рассмотрении', 'Одобрены', 'Отклонены'],
            'datasets' => [[
                'data' => [
                    Application::where('status', 'pending')->count(),
                    Application::where('status', 'approved')->count(),
                    Application::where('status', 'rejected')->count(),
                ],
                'backgroundColor' => [
                    '#f59e0b', // orange
                    '#10b981', // green
                    '#ef4444', // red
                ],
            ]],
        ];

        // Статистика по статусам релизов
        $releaseStatusData = [
            'labels' => ['Черновики', 'На рассмотрении', 'Опубликованы', 'Отклонены'],
            'datasets' => [[
                'data' => [
                    Release::where('status', 'draft')->count(),
                    Release::where('status', 'pending')->count(),
                    Release::where('status', 'published')->count(),
                    Release::where('status', 'rejected')->count(),
                ],
                'backgroundColor' => [
                    '#6b7280', // gray
                    '#f59e0b', // orange
                    '#10b981', // green
                    '#ef4444', // red
                ],
            ]],
        ];

        return [
            'activity' => [
                'labels' => $dates,
                'datasets' => [
                    [
                        'label' => 'Заявки',
                        'data' => $applicationsData,
                        'borderColor' => '#f59e0b',
                        'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                        'fill' => true,
                    ],
                    [
                        'label' => 'Релизы',
                        'data' => $releasesData,
                        'borderColor' => '#3b82f6',
                        'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                        'fill' => true,
                    ],
                    [
                        'label' => 'Пользователи',
                        'data' => $usersData,
                        'borderColor' => '#10b981',
                        'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                        'fill' => true,
                    ],
                ],
            ],
            'applicationStatus' => $applicationStatusData,
            'releaseStatus' => $releaseStatusData,
        ];
    }
}
