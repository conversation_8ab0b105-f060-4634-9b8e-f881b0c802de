<?php

namespace App\Http\Controllers;

use App\Models\Release;
use App\Models\Track;
use App\Services\FileManagementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class TrackController extends Controller
{
    /**
     * Отображение списка треков.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $query = Track::query()->with(['release', 'release.artist']);
        
        // Фильтр по релизу
        if ($request->has('release_id') && $request->release_id) {
            $query->where('release_id', $request->release_id);
        }
        
        // Для обычных пользователей показываем только их треки
        if (!$user->isAdmin() && !$user->isManager()) {
            $query->whereHas('release.artist', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            });
        }
        
        // Сортировка
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);
        
        $tracks = $query->paginate(15)->withQueryString();
        
        return Inertia::render('Tracks/Index', [
            'tracks' => $tracks,
            'filters' => [
                'release_id' => $request->release_id,
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
        ]);
    }

    /**
     * Загрузка трека для релиза.
     */
    public function store(Request $request, Release $release, FileManagementService $fileService)
    {
        // Проверка прав доступа
        $this->authorizeUpload($release);

        // Валидация данных трека
        $validatedData = $request->validate([
            'title' => 'required|string|max:255',
            'track_number' => 'required|integer|min:1',
            'file' => 'required|file|max:51200', // 50MB макс для треков
            'is_explicit' => 'boolean',
            'lyrics' => 'nullable|string',
        ]);
        
        // Проверка, что номер трека уникален для этого релиза
        $existingTrack = $release->tracks()
            ->where('track_number', $validatedData['track_number'])
            ->exists();
            
        if ($existingTrack) {
            return redirect()->back()
                ->withErrors(['track_number' => 'Трек с таким номером уже существует в релизе.']);
        }
        
        // Валидация и сохранение файла трека через FileManagementService
        if ($request->hasFile('file')) {
            $trackFile = $request->file('file');

            // Валидация файла
            if (!$fileService->validateFile(
                $trackFile,
                $fileService->getAllowedFileTypes('track'),
                $fileService->getMaxFileSize('track')
            )) {
                return redirect()->back()
                    ->withErrors(['file' => 'Неподдерживаемый тип файла или размер превышает лимит.'])
                    ->withInput();
            }

            // Асинхронная загрузка трека
            $tempPath = $fileService->uploadFileAsync($trackFile, "tracks/{$release->id}", [
                'release_id' => $release->id,
                'track_title' => $validatedData['title'],
                'track_number' => $validatedData['track_number'],
                'user_id' => $request->user()->id
            ]);

            // Временно сохраняем путь к временному файлу
            $validatedData['file_path'] = $tempPath;
            $validatedData['file_type'] = $trackFile->getClientOriginalExtension();
            $validatedData['duration_seconds'] = 0; // Будет обновлено после обработки
        }
        
        // Сохранение метаданных
        $validatedData['meta_info'] = json_encode([
            'uploaded_by' => $request->user()->name,
            'uploaded_at' => now()->toDateTimeString(),
        ]);
        
        // Создание трека
        $track = $release->tracks()->create($validatedData);
        
        return redirect()->route('releases.show', $release->id)
            ->with('success', 'Трек успешно добавлен.');
    }

    /**
     * Обновление информации о треке.
     */
    public function update(Request $request, Track $track)
    {
        $release = $track->release;
        
        // Проверка прав доступа
        $this->authorizeUpload($release);
        
        // Валидация данных трека
        $validatedData = $request->validate([
            'title' => 'required|string|max:255',
            'track_number' => 'required|integer|min:1',
            'is_explicit' => 'boolean',
            'lyrics' => 'nullable|string',
            'file' => 'nullable|file|mimes:mp3,wav,ogg,m4a|max:20480', // 20MB макс
        ]);
        
        // Проверка, что номер трека уникален для этого релиза, если он изменился
        if ($track->track_number !== (int)$validatedData['track_number']) {
            $existingTrack = $release->tracks()
                ->where('track_number', $validatedData['track_number'])
                ->where('id', '!=', $track->id)
                ->exists();
                
            if ($existingTrack) {
                return redirect()->back()
                    ->withErrors(['track_number' => 'Трек с таким номером уже существует в релизе.']);
            }
        }
        
        // Обновление файла трека, если загружен новый
        if ($request->hasFile('file')) {
            // Удаляем старый файл, если есть
            if ($track->file_path) {
                Storage::delete('public/' . $track->file_path);
            }
            
            // Сохраняем новый файл
            $trackPath = $request->file('file')
                ->store('tracks/' . $release->id, 'public');
            $validatedData['file_path'] = $trackPath;
            $validatedData['file_type'] = $request->file('file')->getClientOriginalExtension();
            
            // Получение длительности трека (требуется установка getID3)
            try {
                $getID3 = new \getID3();
                $fileInfo = $getID3->analyze(storage_path('app/' . $trackPath));
                $validatedData['duration_seconds'] = isset($fileInfo['playtime_seconds']) 
                    ? (int)$fileInfo['playtime_seconds'] 
                    : 0;
            } catch (\Exception $e) {
                $validatedData['duration_seconds'] = 0; // Если не удалось определить длительность
            }
        }
        
        // Обновление метаданных
        $metaInfo = json_decode($track->meta_info, true) ?: [];
        $metaInfo['updated_by'] = $request->user()->name;
        $metaInfo['updated_at'] = now()->toDateTimeString();
        $validatedData['meta_info'] = json_encode($metaInfo);
        
        // Обновление трека
        $track->update($validatedData);
        
        return redirect()->route('releases.show', $release->id)
            ->with('success', 'Трек успешно обновлен.');
    }

    /**
     * Удаление трека.
     */
    public function destroy(Track $track)
    {
        $release = $track->release;
        
        // Проверка прав доступа
        $this->authorizeUpload($release);
        
        // Удаление файла трека
        if ($track->file_path) {
            Storage::delete('public/' . $track->file_path);
        }
        
        // Удаление трека
        $track->delete();
        
        return redirect()->route('releases.show', $release->id)
            ->with('success', 'Трек успешно удален.');
    }
    
    /**
     * Проверка права на загрузку/редактирование треков релиза.
     */
    private function authorizeUpload(Release $release)
    {
        $user = auth()->user();
        
        // Админы и менеджеры могут управлять всеми треками
        if ($user->isAdmin() || $user->isManager()) {
            return true;
        }
        
        // Обычные пользователи могут управлять треками только своих релизов в статусе черновик
        if ($release->artist->user_id !== $user->id || $release->status !== 'draft') {
            abort(403, 'Доступ запрещен или релиз уже отправлен на модерацию.');
        }
        
        return true;
    }
} 