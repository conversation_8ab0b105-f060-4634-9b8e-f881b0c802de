<?php

namespace App\Listeners;

use App\Events\ApplicationStatusUpdated;
use App\Models\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CreateNotificationOnApplicationStatusUpdate implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ApplicationStatusUpdated $event): void
    {
        $application = $event->application;
        $newStatus = $event->newStatus;

        // Определяем тип уведомления и сообщение
        $notificationType = "application_{$newStatus}";
        $title = $this->getNotificationTitle($newStatus);
        $message = $this->getNotificationMessage($application->title, $newStatus);

        // Создаем уведомление для пользователя
        Notification::createForUser(
            $application->user_id,
            $notificationType,
            $title,
            $message,
            [
                'application_id' => $application->id,
                'application_title' => $application->title,
                'old_status' => $event->oldStatus,
                'new_status' => $newStatus,
            ]
        );
    }

    /**
     * Get notification title based on status.
     */
    private function getNotificationTitle(string $status): string
    {
        return match ($status) {
            'approved' => 'Заявка одобрена!',
            'rejected' => 'Заявка отклонена',
            'pending' => 'Заявка на рассмотрении',
            default => 'Статус заявки изменен',
        };
    }

    /**
     * Get notification message based on status.
     */
    private function getNotificationMessage(string $applicationTitle, string $status): string
    {
        return match ($status) {
            'approved' => "Ваша заявка \"{$applicationTitle}\" была одобрена администратором.",
            'rejected' => "Ваша заявка \"{$applicationTitle}\" была отклонена администратором.",
            'pending' => "Ваша заявка \"{$applicationTitle}\" отправлена на рассмотрение.",
            default => "Статус вашей заявки \"{$applicationTitle}\" изменен на: {$status}",
        };
    }
}
