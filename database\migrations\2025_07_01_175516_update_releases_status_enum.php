<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Для SQLite нужно пересоздать таблицу с новым enum
        if (DB::getDriverName() === 'sqlite') {
            // Создаем временную таблицу с новой структурой
            Schema::create('releases_temp', function (Blueprint $table) {
                $table->id();
                $table->foreignId('artist_id')->constrained()->onDelete('cascade');
                $table->string('title');
                $table->text('description')->nullable();
                $table->enum('type', ['album', 'single', 'ep'])->default('single');
                $table->enum('status', ['draft', 'pending', 'approved', 'rejected', 'published'])->default('draft');
                $table->string('cover_image', 2048)->nullable();
                $table->json('meta_info')->nullable();
                $table->string('genre')->nullable();
                $table->string('language')->nullable();
                $table->boolean('is_explicit')->default(false);
                $table->date('release_date')->nullable();
                $table->timestamps();
            });

            // Копируем данные, заменяя 'approved' на 'published' если нужно
            DB::statement('INSERT INTO releases_temp SELECT * FROM releases');

            // Удаляем старую таблицу
            Schema::dropIfExists('releases');

            // Переименовываем временную таблицу
            Schema::rename('releases_temp', 'releases');
        } else {
            // Для других БД используем ALTER TABLE
            DB::statement("ALTER TABLE releases MODIFY COLUMN status ENUM('draft', 'pending', 'approved', 'rejected', 'published') DEFAULT 'draft'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (DB::getDriverName() === 'sqlite') {
            // Создаем временную таблицу с старой структурой
            Schema::create('releases_temp', function (Blueprint $table) {
                $table->id();
                $table->foreignId('artist_id')->constrained()->onDelete('cascade');
                $table->string('title');
                $table->text('description')->nullable();
                $table->enum('type', ['album', 'single', 'ep'])->default('single');
                $table->enum('status', ['draft', 'pending', 'approved', 'rejected'])->default('draft');
                $table->string('cover_image', 2048)->nullable();
                $table->json('meta_info')->nullable();
                $table->string('genre')->nullable();
                $table->string('language')->nullable();
                $table->boolean('is_explicit')->default(false);
                $table->date('release_date')->nullable();
                $table->timestamps();
            });

            // Копируем данные, заменяя 'published' на 'approved'
            DB::statement("INSERT INTO releases_temp SELECT id, artist_id, title, description, type, CASE WHEN status = 'published' THEN 'approved' ELSE status END, cover_image, meta_info, genre, language, is_explicit, release_date, created_at, updated_at FROM releases");

            // Удаляем новую таблицу
            Schema::dropIfExists('releases');

            // Переименовываем временную таблицу
            Schema::rename('releases_temp', 'releases');
        } else {
            DB::statement("ALTER TABLE releases MODIFY COLUMN status ENUM('draft', 'pending', 'approved', 'rejected') DEFAULT 'draft'");
        }
    }
};
